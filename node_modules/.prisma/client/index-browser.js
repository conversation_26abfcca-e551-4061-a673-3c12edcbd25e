
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  clerkId: 'clerkId',
  email: 'email',
  role: 'role',
  firstName: 'firstName',
  lastName: 'lastName',
  phone: 'phone',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AgentScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  employeeId: 'employeeId',
  photoUrl: 'photoUrl',
  certifications: 'certifications',
  skills: 'skills',
  availability: 'availability',
  performanceStats: 'performanceStats',
  emergencyContactName: 'emergencyContactName',
  emergencyContactPhone: 'emergencyContactPhone',
  hireDate: 'hireDate',
  hourlyRate: 'hourlyRate',
  isAvailable: 'isAvailable',
  currentLatitude: 'currentLatitude',
  currentLongitude: 'currentLongitude',
  lastLocationUpdate: 'lastLocationUpdate',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ClientScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyName: 'companyName',
  contactPerson: 'contactPerson',
  billingAddress: 'billingAddress',
  serviceLevel: 'serviceLevel',
  contractStartDate: 'contractStartDate',
  contractEndDate: 'contractEndDate',
  billingCycle: 'billingCycle',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SiteScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  name: 'name',
  address: 'address',
  latitude: 'latitude',
  longitude: 'longitude',
  geofenceRadius: 'geofenceRadius',
  qrCode: 'qrCode',
  siteType: 'siteType',
  specialInstructions: 'specialInstructions',
  accessCodes: 'accessCodes',
  emergencyContacts: 'emergencyContacts',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ShiftScalarFieldEnum = {
  id: 'id',
  siteId: 'siteId',
  agentId: 'agentId',
  supervisorId: 'supervisorId',
  shiftDate: 'shiftDate',
  startTime: 'startTime',
  endTime: 'endTime',
  status: 'status',
  actualStartTime: 'actualStartTime',
  actualEndTime: 'actualEndTime',
  breakDuration: 'breakDuration',
  overtimeHours: 'overtimeHours',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TimeEntryScalarFieldEnum = {
  id: 'id',
  shiftId: 'shiftId',
  agentId: 'agentId',
  clockInTime: 'clockInTime',
  clockOutTime: 'clockOutTime',
  clockInLatitude: 'clockInLatitude',
  clockInLongitude: 'clockInLongitude',
  clockOutLatitude: 'clockOutLatitude',
  clockOutLongitude: 'clockOutLongitude',
  clockInMethod: 'clockInMethod',
  clockOutMethod: 'clockOutMethod',
  clockInAccuracy: 'clockInAccuracy',
  clockOutAccuracy: 'clockOutAccuracy',
  totalHours: 'totalHours',
  isVerified: 'isVerified',
  verifiedBy: 'verifiedBy',
  verifiedAt: 'verifiedAt',
  discrepancyNotes: 'discrepancyNotes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ReportScalarFieldEnum = {
  id: 'id',
  type: 'type',
  shiftId: 'shiftId',
  siteId: 'siteId',
  agentId: 'agentId',
  supervisorId: 'supervisorId',
  title: 'title',
  description: 'description',
  observations: 'observations',
  incidents: 'incidents',
  actionsTaken: 'actionsTaken',
  recommendations: 'recommendations',
  status: 'status',
  priority: 'priority',
  clientSignature: 'clientSignature',
  clientFeedback: 'clientFeedback',
  photos: 'photos',
  videos: 'videos',
  attachments: 'attachments',
  latitude: 'latitude',
  longitude: 'longitude',
  weatherConditions: 'weatherConditions',
  temperature: 'temperature',
  submittedAt: 'submittedAt',
  approvedAt: 'approvedAt',
  rejectedAt: 'rejectedAt',
  rejectionReason: 'rejectionReason',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  recipientId: 'recipientId',
  senderId: 'senderId',
  type: 'type',
  title: 'title',
  message: 'message',
  data: 'data',
  isRead: 'isRead',
  priority: 'priority',
  deliveryMethod: 'deliveryMethod',
  scheduledFor: 'scheduledFor',
  deliveredAt: 'deliveredAt',
  createdAt: 'createdAt',
  readAt: 'readAt'
};

exports.Prisma.CommunicationScalarFieldEnum = {
  id: 'id',
  senderId: 'senderId',
  recipientId: 'recipientId',
  threadId: 'threadId',
  messageType: 'messageType',
  content: 'content',
  attachments: 'attachments',
  isRead: 'isRead',
  isUrgent: 'isUrgent',
  replyToId: 'replyToId',
  createdAt: 'createdAt',
  readAt: 'readAt',
  editedAt: 'editedAt'
};

exports.Prisma.AuditLogScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  action: 'action',
  tableName: 'tableName',
  recordId: 'recordId',
  oldValues: 'oldValues',
  newValues: 'newValues',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  sessionId: 'sessionId',
  apiEndpoint: 'apiEndpoint',
  httpMethod: 'httpMethod',
  responseStatus: 'responseStatus',
  createdAt: 'createdAt'
};

exports.Prisma.ClientRequestScalarFieldEnum = {
  id: 'id',
  clientId: 'clientId',
  siteId: 'siteId',
  requestType: 'requestType',
  priority: 'priority',
  title: 'title',
  description: 'description',
  status: 'status',
  assignedToId: 'assignedToId',
  estimatedCompletion: 'estimatedCompletion',
  actualCompletion: 'actualCompletion',
  clientSatisfactionRating: 'clientSatisfactionRating',
  clientFeedback: 'clientFeedback',
  internalNotes: 'internalNotes',
  costEstimate: 'costEstimate',
  actualCost: 'actualCost',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  resolvedAt: 'resolvedAt'
};

exports.Prisma.GeofenceViolationScalarFieldEnum = {
  id: 'id',
  agentId: 'agentId',
  siteId: 'siteId',
  shiftId: 'shiftId',
  violationType: 'violationType',
  agentLatitude: 'agentLatitude',
  agentLongitude: 'agentLongitude',
  distanceFromSite: 'distanceFromSite',
  durationMinutes: 'durationMinutes',
  isResolved: 'isResolved',
  resolutionNotes: 'resolutionNotes',
  createdAt: 'createdAt',
  resolvedAt: 'resolvedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  SUPERVISOR: 'SUPERVISOR',
  AGENT: 'AGENT',
  CLIENT: 'CLIENT'
};

exports.ShiftStatus = exports.$Enums.ShiftStatus = {
  SCHEDULED: 'SCHEDULED',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
  NO_SHOW: 'NO_SHOW'
};

exports.ClockMethod = exports.$Enums.ClockMethod = {
  GPS: 'GPS',
  QR_CODE: 'QR_CODE',
  MANUAL: 'MANUAL',
  NFC: 'NFC'
};

exports.ReportType = exports.$Enums.ReportType = {
  PATROL: 'PATROL',
  INCIDENT: 'INCIDENT',
  INSPECTION: 'INSPECTION',
  MAINTENANCE: 'MAINTENANCE'
};

exports.ReportStatus = exports.$Enums.ReportStatus = {
  DRAFT: 'DRAFT',
  SUBMITTED: 'SUBMITTED',
  UNDER_REVIEW: 'UNDER_REVIEW',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  ARCHIVED: 'ARCHIVED'
};

exports.Priority = exports.$Enums.Priority = {
  LOW: 'LOW',
  NORMAL: 'NORMAL',
  HIGH: 'HIGH',
  CRITICAL: 'CRITICAL',
  EMERGENCY: 'EMERGENCY'
};

exports.DeliveryMethod = exports.$Enums.DeliveryMethod = {
  APP: 'APP',
  EMAIL: 'EMAIL',
  SMS: 'SMS',
  PUSH: 'PUSH'
};

exports.MessageType = exports.$Enums.MessageType = {
  TEXT: 'TEXT',
  IMAGE: 'IMAGE',
  FILE: 'FILE',
  VOICE: 'VOICE',
  VIDEO: 'VIDEO'
};

exports.ClientRequestStatus = exports.$Enums.ClientRequestStatus = {
  OPEN: 'OPEN',
  ACKNOWLEDGED: 'ACKNOWLEDGED',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED',
  CANCELLED: 'CANCELLED'
};

exports.Prisma.ModelName = {
  User: 'User',
  Agent: 'Agent',
  Client: 'Client',
  Site: 'Site',
  Shift: 'Shift',
  TimeEntry: 'TimeEntry',
  Report: 'Report',
  Notification: 'Notification',
  Communication: 'Communication',
  AuditLog: 'AuditLog',
  ClientRequest: 'ClientRequest',
  GeofenceViolation: 'GeofenceViolation'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
