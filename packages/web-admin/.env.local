# BahinLink Web Admin Environment Variables
# ⚠️ CRITICAL: Production environment configuration

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_IHdUQQJ0YWh2ilwmbSiMxhjOoJDyEvvOYFhhuIoGzz

# Clerk URLs
NEXT_PUBLIC_CLERK_SIGN_IN_URL=/sign-in
NEXT_PUBLIC_CLERK_SIGN_UP_URL=/sign-up
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL=/dashboard
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL=/dashboard

# API Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001
API_BASE_URL=http://localhost:3001

# App Configuration
NEXT_PUBLIC_APP_NAME=BahinLink Admin
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_COMPANY_NAME=Bahin SARL

# Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Feature Flags
NEXT_PUBLIC_ENABLE_REAL_TIME_MAP=true
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_REPORTS=true
NEXT_PUBLIC_ENABLE_CLIENT_PORTAL=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG_MODE=true

# Database (for admin operations)
DATABASE_URL="prisma://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19qTGVVWXk1ZXZ1NTZvLUFublhqNzIiLCJhcGlfa2V5IjoiMDFKWldXSzJQRFNUV1JZNVcxVjI1TVZKR0oiLCJ0ZW5hbnRfaWQiOiI5ZDA3NjJhYzgxNGEwMTYyNjYyMzFhY2I2MjQ0NmViYzFhYjRlODJkNTk2ZDRlNGE0YzZiY2Y2NGY1MDQyYTRmIiwiaW50ZXJuYWxfc2VjcmV0IjoiMzA5MWY3NTktMDU5Ny00NTBlLTlhYWQtNGY0ZmI1NDM1OTIyIn0.H-twYVvGFtn4TM6wJPFgexRcUfSDyBGrH614Mvcg4Ws"

# Webhook Configuration
CLERK_WEBHOOK_SECRET=whsec_dyTwNQATnQEWOwPOwExwWKmdApqy5eXm

# Security
NEXTAUTH_SECRET=bahinlink-nextauth-secret-key-2024
NEXTAUTH_URL=http://localhost:3000

# Analytics
NEXT_PUBLIC_ANALYTICS_ID=your-analytics-id

# Monitoring
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=50MB
NEXT_PUBLIC_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,video/mp4,application/pdf
