/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(rsc)/./src/app/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGbm9kZV9tb2R1bGVzJTJGJTQwY2xlcmslMkZuZXh0anMlMkZkaXN0JTJGZXNtJTJGYXBwLXJvdXRlciUyRmNsaWVudCUyRkNsZXJrUHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDbGllbnRDbGVya1Byb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZmaW5hbGFnZW50LW1haW4tZmluYWwlMkZub2RlX21vZHVsZXMlMkYlNDBjbGVyayUyRm5leHRqcyUyRmRpc3QlMkZlc20lMkZjbGllbnQtYm91bmRhcnklMkZjb250cm9sQ29tcG9uZW50cy5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhlbnRpY2F0ZVdpdGhSZWRpcmVjdENhbGxiYWNrJTIyJTJDJTIyQ2xlcmtMb2FkZWQlMjIlMkMlMjJDbGVya0xvYWRpbmclMjIlMkMlMjJNdWx0aXNlc3Npb25BcHBTdXBwb3J0JTIyJTJDJTIyUmVkaXJlY3RUb0NyZWF0ZU9yZ2FuaXphdGlvbiUyMiUyQyUyMlJlZGlyZWN0VG9Pcmdhbml6YXRpb25Qcm9maWxlJTIyJTJDJTIyUmVkaXJlY3RUb1NpZ25JbiUyMiUyQyUyMlJlZGlyZWN0VG9TaWduVXAlMjIlMkMlMjJSZWRpcmVjdFRvVXNlclByb2ZpbGUlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGd29ya3NwYWNlcyUyRmZpbmFsYWdlbnQtbWFpbi1maW5hbCUyRm5vZGVfbW9kdWxlcyUyRiU0MGNsZXJrJTJGbmV4dGpzJTJGZGlzdCUyRmVzbSUyRmNsaWVudC1ib3VuZGFyeSUyRmhvb2tzLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRW1haWxMaW5rRXJyb3JDb2RlJTIyJTJDJTIyTWFnaWNMaW5rRXJyb3JDb2RlJTIyJTJDJTIyV2l0aENsZXJrJTIyJTJDJTIyV2l0aFNlc3Npb24lMjIlMkMlMjJXaXRoVXNlciUyMiUyQyUyMmlzQ2xlcmtBUElSZXNwb25zZUVycm9yJTIyJTJDJTIyaXNFbWFpbExpbmtFcnJvciUyMiUyQyUyMmlzS25vd25FcnJvciUyMiUyQyUyMmlzTWFnaWNMaW5rRXJyb3IlMjIlMkMlMjJpc01ldGFtYXNrRXJyb3IlMjIlMkMlMjJ1c2VBdXRoJTIyJTJDJTIydXNlQ2xlcmslMjIlMkMlMjJ1c2VFbWFpbExpbmslMjIlMkMlMjJ1c2VNYWdpY0xpbmslMjIlMkMlMjJ1c2VPcmdhbml6YXRpb24lMjIlMkMlMjJ1c2VPcmdhbml6YXRpb25MaXN0JTIyJTJDJTIydXNlT3JnYW5pemF0aW9ucyUyMiUyQyUyMnVzZVNlc3Npb24lMjIlMkMlMjJ1c2VTZXNzaW9uTGlzdCUyMiUyQyUyMnVzZVNpZ25JbiUyMiUyQyUyMnVzZVNpZ25VcCUyMiUyQyUyMnVzZVVzZXIlMjIlMkMlMjJ3aXRoQ2xlcmslMjIlMkMlMjJ3aXRoU2Vzc2lvbiUyMiUyQyUyMndpdGhVc2VyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZmaW5hbGFnZW50LW1haW4tZmluYWwlMkZub2RlX21vZHVsZXMlMkYlNDBjbGVyayUyRm5leHRqcyUyRmRpc3QlMkZlc20lMkZjbGllbnQtYm91bmRhcnklMkZ1aUNvbXBvbmVudHMuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDcmVhdGVPcmdhbml6YXRpb24lMjIlMkMlMjJHb29nbGVPbmVUYXAlMjIlMkMlMjJPcmdhbml6YXRpb25MaXN0JTIyJTJDJTIyT3JnYW5pemF0aW9uUHJvZmlsZSUyMiUyQyUyMk9yZ2FuaXphdGlvblN3aXRjaGVyJTIyJTJDJTIyU2lnbkluJTIyJTJDJTIyU2lnbkluQnV0dG9uJTIyJTJDJTIyU2lnbkluV2l0aE1ldGFtYXNrQnV0dG9uJTIyJTJDJTIyU2lnbk91dEJ1dHRvbiUyMiUyQyUyMlNpZ25VcCUyMiUyQyUyMlNpZ25VcEJ1dHRvbiUyMiUyQyUyMlVzZXJCdXR0b24lMjIlMkMlMjJVc2VyUHJvZmlsZSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC5qcyU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGd29ya3NwYWNlcyUyRmZpbmFsYWdlbnQtbWFpbi1maW5hbCUyRnBhY2thZ2VzJTJGd2ViLWFkbWluJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9RQUFzTDtBQUN0TDtBQUNBLHdRQUF3WDtBQUN4WDtBQUNBLGdQQUF5aEI7QUFDemhCO0FBQ0EsOFBBQXVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vP2E0NzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRDbGVya1Byb3ZpZGVyXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2FwcC1yb3V0ZXIvY2xpZW50L0NsZXJrUHJvdmlkZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhlbnRpY2F0ZVdpdGhSZWRpcmVjdENhbGxiYWNrXCIsXCJDbGVya0xvYWRlZFwiLFwiQ2xlcmtMb2FkaW5nXCIsXCJNdWx0aXNlc3Npb25BcHBTdXBwb3J0XCIsXCJSZWRpcmVjdFRvQ3JlYXRlT3JnYW5pemF0aW9uXCIsXCJSZWRpcmVjdFRvT3JnYW5pemF0aW9uUHJvZmlsZVwiLFwiUmVkaXJlY3RUb1NpZ25JblwiLFwiUmVkaXJlY3RUb1NpZ25VcFwiLFwiUmVkaXJlY3RUb1VzZXJQcm9maWxlXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2NsaWVudC1ib3VuZGFyeS9jb250cm9sQ29tcG9uZW50cy5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRW1haWxMaW5rRXJyb3JDb2RlXCIsXCJNYWdpY0xpbmtFcnJvckNvZGVcIixcIldpdGhDbGVya1wiLFwiV2l0aFNlc3Npb25cIixcIldpdGhVc2VyXCIsXCJpc0NsZXJrQVBJUmVzcG9uc2VFcnJvclwiLFwiaXNFbWFpbExpbmtFcnJvclwiLFwiaXNLbm93bkVycm9yXCIsXCJpc01hZ2ljTGlua0Vycm9yXCIsXCJpc01ldGFtYXNrRXJyb3JcIixcInVzZUF1dGhcIixcInVzZUNsZXJrXCIsXCJ1c2VFbWFpbExpbmtcIixcInVzZU1hZ2ljTGlua1wiLFwidXNlT3JnYW5pemF0aW9uXCIsXCJ1c2VPcmdhbml6YXRpb25MaXN0XCIsXCJ1c2VPcmdhbml6YXRpb25zXCIsXCJ1c2VTZXNzaW9uXCIsXCJ1c2VTZXNzaW9uTGlzdFwiLFwidXNlU2lnbkluXCIsXCJ1c2VTaWduVXBcIixcInVzZVVzZXJcIixcIndpdGhDbGVya1wiLFwid2l0aFNlc3Npb25cIixcIndpdGhVc2VyXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2NsaWVudC1ib3VuZGFyeS9ob29rcy5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ3JlYXRlT3JnYW5pemF0aW9uXCIsXCJHb29nbGVPbmVUYXBcIixcIk9yZ2FuaXphdGlvbkxpc3RcIixcIk9yZ2FuaXphdGlvblByb2ZpbGVcIixcIk9yZ2FuaXphdGlvblN3aXRjaGVyXCIsXCJTaWduSW5cIixcIlNpZ25JbkJ1dHRvblwiLFwiU2lnbkluV2l0aE1ldGFtYXNrQnV0dG9uXCIsXCJTaWduT3V0QnV0dG9uXCIsXCJTaWduVXBcIixcIlNpZ25VcEJ1dHRvblwiLFwiVXNlckJ1dHRvblwiLFwiVXNlclByb2ZpbGVcIl0gKi8gXCIvd29ya3NwYWNlcy9maW5hbGFnZW50LW1haW4tZmluYWwvbm9kZV9tb2R1bGVzL0BjbGVyay9uZXh0anMvZGlzdC9lc20vY2xpZW50LWJvdW5kYXJ5L3VpQ29tcG9uZW50cy5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.js */ \"(ssr)/./src/app/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw4SUFBeUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8/Y2M1OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi93b3Jrc3BhY2VzL2ZpbmFsYWdlbnQtbWFpbi1maW5hbC9wYWNrYWdlcy93ZWItYWRtaW4vc3JjL2FwcC9wYWdlLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(ssr)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/ApiService */ \"(ssr)/./src/services/ApiService.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(ssr)/./src/utils/index.js\");\n/* harmony import */ var _components_Navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/Navigation */ \"(ssr)/./src/components/Navigation.js\");\n// BahinLink Admin Dashboard\n// ⚠️ CRITICAL: Real-time monitoring with production data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n// Dynamically import RealTimeMap to avoid SSR issues\nconst RealTimeMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(async ()=>{\n     true && /*require.resolve*/(null /* weak dependency, without id */);\n}, {\n    loadableGenerated: {\n        modules: [\n            \"app/page.js -> \" + \"../components/RealTimeMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sx: {\n                height: 400,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n});\nfunction Dashboard() {\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadDashboardData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadDashboardData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            setError(null);\n            // Get real dashboard analytics\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/dashboard\");\n            if (response.success) {\n                setDashboardData(response.data);\n            } else {\n                throw new _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"](response.error?.message || \"Failed to load dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Dashboard data error:\", error);\n            setError(error.message || \"Failed to load dashboard data\");\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadDashboardData();\n    };\n    if (!isLoaded || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading BahinLink Dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    color: \"inherit\",\n                    size: \"small\",\n                    onClick: handleRefresh,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, void 0),\n                children: error\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const { activeAgents = 0, totalAgents = 0, activeShifts = 0, pendingReports = 0, geofenceViolations = 0, clientSatisfaction = 0, recentActivity = [], agentLocations = [], shiftStats = [], alerts = [] } = dashboardData || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            flexGrow: 1,\n            backgroundColor: \"#f5f5f5\",\n            minHeight: \"100vh\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navigation__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sx: {\n                    p: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\",\n                        mb: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"h4\",\n                                component: \"h1\",\n                                fontWeight: \"bold\",\n                                children: \"Dashboard Overview\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                gap: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"text.secondary\",\n                                        children: [\n                                            \"Welcome, \",\n                                            user?.firstName,\n                                            \" \",\n                                            user?.lastName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 170,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: handleRefresh,\n                                        disabled: refreshing,\n                                        children: refreshing ? \"Refreshing...\" : \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        mb: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            color: \"text.secondary\",\n                                                            gutterBottom: true,\n                                                            children: \"Active Agents\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            variant: \"h4\",\n                                                            component: \"div\",\n                                                            children: [\n                                                                activeAgents,\n                                                                \"/\",\n                                                                totalAgents\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    color: \"primary\",\n                                                    sx: {\n                                                        fontSize: 40\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 182,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 181,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            color: \"text.secondary\",\n                                                            gutterBottom: true,\n                                                            children: \"Active Shifts\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            variant: \"h4\",\n                                                            component: \"div\",\n                                                            children: activeShifts\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    color: \"success\",\n                                                    sx: {\n                                                        fontSize: 40\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 199,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            color: \"text.secondary\",\n                                                            gutterBottom: true,\n                                                            children: \"Pending Reports\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            variant: \"h4\",\n                                                            component: \"div\",\n                                                            children: pendingReports\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    color: \"warning\",\n                                                    sx: {\n                                                        fontSize: 40\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 220,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 217,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            justifyContent: \"space-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            color: \"text.secondary\",\n                                                            gutterBottom: true,\n                                                            children: \"Client Satisfaction\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            variant: \"h4\",\n                                                            component: \"div\",\n                                                            children: [\n                                                                clientSatisfaction.toFixed(1),\n                                                                \"/5.0\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    color: \"success\",\n                                                    sx: {\n                                                        fontSize: 40\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 238,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 236,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 235,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 180,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        mb: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h6\",\n                                            gutterBottom: true,\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            display: \"flex\",\n                                            gap: 2,\n                                            flexWrap: \"wrap\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    variant: \"contained\",\n                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 30\n                                                    }, void 0),\n                                                    onClick: ()=>window.location.href = \"/agents\",\n                                                    sx: {\n                                                        textTransform: \"none\"\n                                                    },\n                                                    children: \"Manage Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    variant: \"contained\",\n                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 30\n                                                    }, void 0),\n                                                    onClick: ()=>window.location.href = \"/sites\",\n                                                    sx: {\n                                                        textTransform: \"none\"\n                                                    },\n                                                    children: \"View Sites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    variant: \"contained\",\n                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 30\n                                                    }, void 0),\n                                                    onClick: ()=>window.location.href = \"/shifts\",\n                                                    sx: {\n                                                        textTransform: \"none\"\n                                                    },\n                                                    children: \"Schedule Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    variant: \"contained\",\n                                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 30\n                                                    }, void 0),\n                                                    onClick: ()=>window.location.href = \"/reports\",\n                                                    sx: {\n                                                        textTransform: \"none\"\n                                                    },\n                                                    children: \"View Reports\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 256,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 255,\n                        columnNumber: 7\n                    }, this),\n                    alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        mb: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h6\",\n                                            gutterBottom: true,\n                                            children: \"Active Alerts\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 307,\n                                            columnNumber: 17\n                                        }, this),\n                                        alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                severity: alert.severity,\n                                                sx: {\n                                                    mb: 1\n                                                },\n                                                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    color: \"inherit\",\n                                                    size: \"small\",\n                                                    children: \"View\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 23\n                                                }, void 0),\n                                                children: alert.message\n                                            }, index, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 311,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        mb: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 8,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Real-time Agent Locations\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 400,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeMap, {\n                                                    agentLocations: agentLocations\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 338,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 334,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 333,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 332,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 4,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                dense: true,\n                                                children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                children: [\n                                                                    activity.type === \"clock_in\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        color: \"success\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 56\n                                                                    }, this),\n                                                                    activity.type === \"clock_out\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        color: \"primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 57\n                                                                    }, this),\n                                                                    activity.type === \"report_submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        color: \"info\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 64\n                                                                    }, this),\n                                                                    activity.type === \"geofence_violation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        color: \"error\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 66\n                                                                    }, this),\n                                                                    activity.type === \"location_update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        color: \"primary\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 63\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 354,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                primary: activity.message,\n                                                                secondary: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(activity.timestamp)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 351,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 346,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 345,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 331,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Shift Performance (Last 7 Days)\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                height: 300,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.LineChart, {\n                                                        data: shiftStats,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.XAxis, {\n                                                                dataKey: \"date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.YAxis, {}, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Tooltip, {}, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"completedShifts\",\n                                                                stroke: \"#2196f3\",\n                                                                strokeWidth: 2,\n                                                                name: \"Completed Shifts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"onTimePercentage\",\n                                                                stroke: \"#4caf50\",\n                                                                strokeWidth: 2,\n                                                                name: \"On-time %\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 395,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 377,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 375,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                md: 6,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Current Shifts\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n                                                variant: \"outlined\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                    size: \"small\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        children: \"Agent\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 419,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        children: \"Site\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        children: \"Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 421,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                        children: \"Time\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                            children: dashboardData?.currentShifts?.map((shift)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: shift.agentName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 428,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: shift.siteName\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 429,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                                                                label: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.enumToDisplayString)(shift.status),\n                                                                                color: shift.status === \"IN_PROGRESS\" ? \"success\" : \"default\",\n                                                                                size: \"small\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 430,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: [\n                                                                                (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.startTime),\n                                                                                \" - \",\n                                                                                (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.endTime)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, shift.id, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 427,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 415,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 410,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 409,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                        lineNumber: 374,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/Navigation.js":
/*!**************************************!*\
  !*** ./src/components/Navigation.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Avatar,Box,Button,Chip,Divider,IconButton,ListItemIcon,ListItemText,Menu,MenuItem,Toolbar,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Notifications.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Business.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,Analytics,Assignment,Business,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Web Admin Navigation Component\n// ⚠️ CRITICAL: Real navigation with admin functionality ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Navigation() {\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleMenuOpen = (event)=>{\n        setAnchorEl(event.currentTarget);\n    };\n    const handleMenuClose = ()=>{\n        setAnchorEl(null);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            path: \"/\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/agents\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/sites\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/shifts\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/analytics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/users\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        position: \"static\",\n        sx: {\n            backgroundColor: \"#1976d2\",\n            mb: 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    sx: {\n                        flexGrow: 1\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            sx: {\n                                mr: 1,\n                                fontSize: 28\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            variant: \"h6\",\n                            component: \"div\",\n                            fontWeight: \"bold\",\n                            children: \"BahinLink Admin\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            label: \"LIVE\",\n                            color: \"success\",\n                            size: \"small\",\n                            sx: {\n                                ml: 2,\n                                fontWeight: \"bold\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    sx: {\n                        display: {\n                            xs: \"none\",\n                            md: \"flex\"\n                        },\n                        gap: 1,\n                        mr: 2\n                    },\n                    children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            color: \"inherit\",\n                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {}, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                lineNumber: 90,\n                                columnNumber: 26\n                            }, void 0),\n                            onClick: ()=>router.push(item.path),\n                            sx: {\n                                textTransform: \"none\",\n                                \"&:hover\": {\n                                    backgroundColor: \"rgba(255, 255, 255, 0.1)\"\n                                }\n                            },\n                            children: item.label\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    color: \"inherit\",\n                    sx: {\n                        mr: 1\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            variant: \"body2\",\n                            sx: {\n                                mr: 1,\n                                display: {\n                                    xs: \"none\",\n                                    sm: \"block\"\n                                }\n                            },\n                            children: [\n                                user?.firstName,\n                                \" \",\n                                user?.lastName\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            color: \"inherit\",\n                            onClick: handleMenuOpen,\n                            sx: {\n                                p: 0\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                src: user?.imageUrl,\n                                alt: user?.fullName,\n                                sx: {\n                                    width: 32,\n                                    height: 32\n                                },\n                                children: user?.firstName?.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    anchorEl: anchorEl,\n                    open: Boolean(anchorEl),\n                    onClose: handleMenuClose,\n                    anchorOrigin: {\n                        vertical: \"bottom\",\n                        horizontal: \"right\"\n                    },\n                    transformOrigin: {\n                        vertical: \"top\",\n                        horizontal: \"right\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            disabled: true,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            variant: \"body2\",\n                                            fontWeight: \"bold\",\n                                            children: user?.fullName\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                            lineNumber: 148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: \"text.secondary\",\n                                            children: user?.primaryEmailAddress?.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            onClick: ()=>{\n                                handleMenuClose();\n                                router.push(\"/settings\");\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    children: \"Settings\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            onClick: ()=>{\n                                handleMenuClose();\n                                router.push(\"/sites\");\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    children: \"Manage Sites\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                            onClick: handleSignOut,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_Analytics_Assignment_Business_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Avatar_Box_Button_Chip_Divider_IconButton_ListItemIcon_ListItemText_Menu_MenuItem_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/Navigation.js\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navigation.js\n");

/***/ }),

/***/ "(ssr)/./src/services/ApiService.js":
/*!************************************!*\
  !*** ./src/services/ApiService.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../../node_modules/axios/lib/axios.js\");\n// BahinLink Web Admin API Service\n// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY\n\n// Real production API base URL\nconst API_BASE_URL =  true ? \"http://localhost:3001/api\" : 0;\nclass ApiService {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n        this.setupInterceptors();\n    }\n    /**\n   * Setup axios interceptors for real authentication\n   */ setupInterceptors() {\n        // Request interceptor to add real Clerk token\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.request.use(async (config)=>{\n            try {\n                // For client-side requests, we'll handle auth in the component\n                // This is a simplified version for development\n                config.headers[\"Content-Type\"] = \"application/json\";\n                config.baseURL = this.baseURL;\n                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n                return config;\n            } catch (error) {\n                console.error(\"Request interceptor error:\", error);\n                return config;\n            }\n        }, (error)=>{\n            console.error(\"Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.response.use((response)=>{\n            console.log(`API Response: ${response.status} ${response.config.url}`);\n            return response.data;\n        }, (error)=>{\n            console.error(\"API Error:\", error.response?.data || error.message);\n            if (error.response?.status === 401) {\n                // Redirect to sign-in\n                window.location.href = \"/sign-in\";\n            }\n            return Promise.reject(error.response?.data || error);\n        });\n    }\n    /**\n   * GET request to real API\n   */ async get(endpoint, params = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(endpoint, {\n                params\n            });\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * POST request to real API\n   */ async post(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * PUT request to real API\n   */ async put(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * DELETE request to real API\n   */ async delete(endpoint) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(endpoint);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Dashboard Analytics\n    async getDashboardAnalytics() {\n        return this.get(\"/analytics/dashboard\");\n    }\n    async getPerformanceMetrics(params = {}) {\n        return this.get(\"/analytics/performance\", params);\n    }\n    async getAgentLocations() {\n        return this.get(\"/analytics/locations\");\n    }\n    // User Management\n    async getUsers(params = {}) {\n        return this.get(\"/users\", params);\n    }\n    async getUser(userId) {\n        return this.get(`/users/${userId}`);\n    }\n    async updateUser(userId, data) {\n        return this.put(`/users/${userId}`, data);\n    }\n    // Agent Management\n    async getAgents(params = {}) {\n        return this.get(\"/agents\", params);\n    }\n    async getAgent(agentId) {\n        return this.get(`/agents/${agentId}`);\n    }\n    async updateAgent(agentId, data) {\n        return this.put(`/agents/${agentId}`, data);\n    }\n    async getNearbyAgents(latitude, longitude, radius) {\n        return this.get(\"/agents/nearby\", {\n            latitude,\n            longitude,\n            radius\n        });\n    }\n    // Site Management\n    async getSites(params = {}) {\n        return this.get(\"/sites\", params);\n    }\n    async createSite(data) {\n        return this.post(\"/sites\", data);\n    }\n    async getSite(siteId) {\n        return this.get(`/sites/${siteId}`);\n    }\n    async updateSite(siteId, data) {\n        return this.put(`/sites/${siteId}`, data);\n    }\n    async generateSiteQR(siteId) {\n        return this.post(`/sites/${siteId}/generate-qr`);\n    }\n    // Shift Management\n    async getShifts(params = {}) {\n        return this.get(\"/shifts\", params);\n    }\n    async createShift(data) {\n        return this.post(\"/shifts\", data);\n    }\n    async getShift(shiftId) {\n        return this.get(`/shifts/${shiftId}`);\n    }\n    async updateShift(shiftId, data) {\n        return this.put(`/shifts/${shiftId}`, data);\n    }\n    async assignShift(shiftId, agentId) {\n        return this.put(`/shifts/${shiftId}`, {\n            agentId\n        });\n    }\n    // Time Tracking\n    async getTimeEntries(params = {}) {\n        return this.get(\"/time/entries\", params);\n    }\n    async verifyTimeEntry(timeEntryId, data) {\n        return this.put(`/time/entries/${timeEntryId}/verify`, data);\n    }\n    // Reports Management\n    async getReports(params = {}) {\n        return this.get(\"/reports\", params);\n    }\n    async getReport(reportId) {\n        return this.get(`/reports/${reportId}`);\n    }\n    async approveReport(reportId, data = {}) {\n        return this.post(`/reports/${reportId}/approve`, data);\n    }\n    async rejectReport(reportId, data) {\n        return this.post(`/reports/${reportId}/reject`, data);\n    }\n    // Notifications\n    async getNotifications(params = {}) {\n        return this.get(\"/notifications\", params);\n    }\n    async sendNotification(data) {\n        return this.post(\"/notifications\", data);\n    }\n    async broadcastNotification(data) {\n        return this.post(\"/notifications/broadcast\", data);\n    }\n    // Communications\n    async getMessages(params = {}) {\n        return this.get(\"/communications\", params);\n    }\n    async sendMessage(data) {\n        return this.post(\"/communications\", data);\n    }\n    async getMessageThreads(params = {}) {\n        return this.get(\"/communications/threads\", params);\n    }\n    // Client Management\n    async getClients(params = {}) {\n        return this.get(\"/clients\", params);\n    }\n    async getClientRequests(params = {}) {\n        return this.get(\"/client/requests\", params);\n    }\n    async updateClientRequest(requestId, data) {\n        return this.put(`/client/requests/${requestId}`, data);\n    }\n    // Geofencing\n    async checkGeofence(data) {\n        return this.post(\"/geofence/check\", data);\n    }\n    async getGeofenceViolations(params = {}) {\n        return this.get(\"/geofence/violations\", params);\n    }\n    async resolveGeofenceViolation(violationId, data) {\n        return this.put(`/geofence/violations/${violationId}/resolve`, data);\n    }\n    // File Management\n    async uploadFile(file, type = \"document\") {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const endpoint = `/upload/${type}`;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                baseURL: this.baseURL\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Bulk Operations\n    async bulkUpdateShifts(shiftIds, data) {\n        return this.put(\"/shifts/bulk\", {\n            shiftIds,\n            ...data\n        });\n    }\n    async bulkNotifyAgents(agentIds, notification) {\n        return this.post(\"/notifications/bulk\", {\n            agentIds,\n            ...notification\n        });\n    }\n    // Export Functions\n    async exportReports(params = {}) {\n        return this.get(\"/reports/export\", params);\n    }\n    async exportTimeEntries(params = {}) {\n        return this.get(\"/time/entries/export\", params);\n    }\n    async exportAgentPerformance(params = {}) {\n        return this.get(\"/analytics/performance/export\", params);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new ApiService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ApiService.js\n");

/***/ }),

/***/ "(ssr)/./src/utils/index.js":
/*!****************************!*\
  !*** ./src/utils/index.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDistance: () => (/* binding */ calculateDistance),\n/* harmony export */   calculateShiftProgress: () => (/* binding */ calculateShiftProgress),\n/* harmony export */   capitalize: () => (/* binding */ capitalize),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   enumToDisplayString: () => (/* binding */ enumToDisplayString),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDuration: () => (/* binding */ formatDuration),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatTime: () => (/* binding */ formatTime),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   isWithinShiftHours: () => (/* binding */ isWithinShiftHours)\n/* harmony export */ });\n// BahinLink Web Admin Utilities\n// ⚠️ CRITICAL: Utility functions for web admin dashboard\n/**\n * Format date for display\n * @param {Date|string} date Date to format\n * @param {string} formatString Format string (default: 'PPP')\n * @returns {string} Formatted date string\n */ function formatDate(date, formatString = \"PPP\") {\n    try {\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        if (!dateObj || isNaN(dateObj.getTime())) return \"Invalid Date\";\n        // Simple date formatting\n        return dateObj.toLocaleDateString(\"en-US\", {\n            year: \"numeric\",\n            month: \"long\",\n            day: \"numeric\"\n        });\n    } catch (error) {\n        return \"Invalid Date\";\n    }\n}\n/**\n * Format time for display\n * @param {Date|string} date Date to format\n * @param {string} formatString Format string (default: 'p')\n * @returns {string} Formatted time string\n */ function formatTime(date, formatString = \"p\") {\n    try {\n        const dateObj = typeof date === \"string\" ? new Date(date) : date;\n        if (!dateObj || isNaN(dateObj.getTime())) return \"Invalid Time\";\n        // Simple time formatting\n        return dateObj.toLocaleTimeString(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    } catch (error) {\n        return \"Invalid Time\";\n    }\n}\n/**\n * Convert enum value to display string\n * @param {string} enumValue Enum value\n * @returns {string} Display string\n */ function enumToDisplayString(enumValue) {\n    if (!enumValue) return \"\";\n    return enumValue.split(\"_\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(\" \");\n}\n/**\n * Format duration in hours and minutes\n * @param {number} hours Duration in hours (decimal)\n * @returns {string} Formatted duration string\n */ function formatDuration(hours) {\n    const wholeHours = Math.floor(hours);\n    const minutes = Math.round((hours - wholeHours) * 60);\n    if (wholeHours === 0) {\n        return `${minutes}m`;\n    }\n    if (minutes === 0) {\n        return `${wholeHours}h`;\n    }\n    return `${wholeHours}h ${minutes}m`;\n}\n/**\n * Calculate distance between two GPS coordinates using Haversine formula\n * @param {number} lat1 Latitude of first point\n * @param {number} lon1 Longitude of first point\n * @param {number} lat2 Latitude of second point\n * @param {number} lon2 Longitude of second point\n * @returns {number} Distance in meters\n */ function calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 6371e3; // Earth's radius in meters\n    const φ1 = lat1 * Math.PI / 180;\n    const φ2 = lat2 * Math.PI / 180;\n    const Δφ = (lat2 - lat1) * Math.PI / 180;\n    const Δλ = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) + Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c; // Distance in meters\n}\n/**\n * Capitalize first letter of string\n * @param {string} str String to capitalize\n * @returns {string} Capitalized string\n */ function capitalize(str) {\n    if (!str) return \"\";\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n}\n/**\n * Generate unique ID\n * @returns {string} Unique ID string\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n/**\n * Debounce function\n * @param {Function} func Function to debounce\n * @param {number} wait Wait time in milliseconds\n * @returns {Function} Debounced function\n */ function debounce(func, wait) {\n    let timeout;\n    return function executedFunction(...args) {\n        const later = ()=>{\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}\n/**\n * Format file size for display\n * @param {number} bytes File size in bytes\n * @returns {string} Formatted file size string\n */ function formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n/**\n * Check if current time is within shift hours\n * @param {Date} shiftStart Shift start time\n * @param {Date} shiftEnd Shift end time\n * @param {Date} currentTime Current time (optional, defaults to now)\n * @returns {boolean} True if within shift hours\n */ function isWithinShiftHours(shiftStart, shiftEnd, currentTime = new Date()) {\n    return currentTime >= shiftStart && currentTime <= shiftEnd;\n}\n/**\n * Calculate shift progress percentage\n * @param {Date} shiftStart Shift start time\n * @param {Date} shiftEnd Shift end time\n * @param {Date} currentTime Current time (optional, defaults to now)\n * @returns {number} Progress percentage (0-100)\n */ function calculateShiftProgress(shiftStart, shiftEnd, currentTime = new Date()) {\n    const totalDuration = shiftEnd.getTime() - shiftStart.getTime();\n    const elapsed = currentTime.getTime() - shiftStart.getTime();\n    if (elapsed <= 0) return 0;\n    if (elapsed >= totalDuration) return 100;\n    return Math.round(elapsed / totalDuration * 100);\n}\n/**\n * Validate email address\n * @param {string} email Email to validate\n * @returns {boolean} True if valid email\n */ function isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n/**\n * Validate phone number (basic validation)\n * @param {string} phone Phone number to validate\n * @returns {boolean} True if valid phone number\n */ function isValidPhone(phone) {\n    const phoneRegex = /^\\+?[\\d\\s\\-\\(\\)]{10,}$/;\n    return phoneRegex.test(phone);\n}\n/**\n * Get status color for UI components\n * @param {string} status Status value\n * @returns {string} Color code\n */ function getStatusColor(status) {\n    const statusColors = {\n        ACTIVE: \"#10b981\",\n        INACTIVE: \"#ef4444\",\n        PENDING: \"#f59e0b\",\n        COMPLETED: \"#10b981\",\n        IN_PROGRESS: \"#3b82f6\",\n        CANCELLED: \"#6b7280\",\n        APPROVED: \"#10b981\",\n        REJECTED: \"#ef4444\",\n        SUBMITTED: \"#f59e0b\"\n    };\n    return statusColors[status] || \"#6b7280\";\n}\n/**\n * Format currency for display\n * @param {number} amount Amount to format\n * @param {string} currency Currency code (default: 'XOF')\n * @returns {string} Formatted currency string\n */ function formatCurrency(amount, currency = \"XOF\") {\n    try {\n        return new Intl.NumberFormat(\"fr-SN\", {\n            style: \"currency\",\n            currency: currency\n        }).format(amount);\n    } catch (error) {\n        return `${amount} ${currency}`;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/index.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/node-fetch-native","vendor-chunks/next","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/@swc","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/lodash","vendor-chunks/recharts","vendor-chunks/d3-shape","vendor-chunks/axios","vendor-chunks/d3-scale","vendor-chunks/d3-array","vendor-chunks/d3-format","vendor-chunks/d3-interpolate","vendor-chunks/d3-time","vendor-chunks/asynckit","vendor-chunks/react-smooth","vendor-chunks/math-intrinsics","vendor-chunks/react-transition-group","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/recharts-scale","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/d3-time-format","vendor-chunks/d3-color","vendor-chunks/victory-vendor","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/tiny-invariant","vendor-chunks/internmap","vendor-chunks/fast-equals","vendor-chunks/decimal.js-light","vendor-chunks/d3-path","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/eventemitter3","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();