"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/pvutils";
exports.ids = ["vendor-chunks/pvutils"];
exports.modules = {

/***/ "(rsc)/../../node_modules/pvutils/build/utils.es.js":
/*!****************************************************!*\
  !*** ../../node_modules/pvutils/build/utils.es.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayBufferToString: () => (/* binding */ arrayBufferToString),\n/* harmony export */   bufferToHexCodes: () => (/* binding */ bufferToHexCodes),\n/* harmony export */   checkBufferParams: () => (/* binding */ checkBufferParams),\n/* harmony export */   clearProps: () => (/* binding */ clearProps),\n/* harmony export */   fromBase64: () => (/* binding */ fromBase64),\n/* harmony export */   getParametersValue: () => (/* binding */ getParametersValue),\n/* harmony export */   getUTCDate: () => (/* binding */ getUTCDate),\n/* harmony export */   isEqualBuffer: () => (/* binding */ isEqualBuffer),\n/* harmony export */   nearestPowerOf2: () => (/* binding */ nearestPowerOf2),\n/* harmony export */   padNumber: () => (/* binding */ padNumber),\n/* harmony export */   stringToArrayBuffer: () => (/* binding */ stringToArrayBuffer),\n/* harmony export */   toBase64: () => (/* binding */ toBase64),\n/* harmony export */   utilConcatBuf: () => (/* binding */ utilConcatBuf),\n/* harmony export */   utilConcatView: () => (/* binding */ utilConcatView),\n/* harmony export */   utilDecodeTC: () => (/* binding */ utilDecodeTC),\n/* harmony export */   utilEncodeTC: () => (/* binding */ utilEncodeTC),\n/* harmony export */   utilFromBase: () => (/* binding */ utilFromBase),\n/* harmony export */   utilToBase: () => (/* binding */ utilToBase)\n/* harmony export */ });\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\nfunction getUTCDate(date) {\r\n    return new Date(date.getTime() + (date.getTimezoneOffset() * 60000));\r\n}\r\nfunction getParametersValue(parameters, name, defaultValue) {\r\n    var _a;\r\n    if ((parameters instanceof Object) === false) {\r\n        return defaultValue;\r\n    }\r\n    return (_a = parameters[name]) !== null && _a !== void 0 ? _a : defaultValue;\r\n}\r\nfunction bufferToHexCodes(inputBuffer, inputOffset = 0, inputLength = (inputBuffer.byteLength - inputOffset), insertSpace = false) {\r\n    let result = \"\";\r\n    for (const item of (new Uint8Array(inputBuffer, inputOffset, inputLength))) {\r\n        const str = item.toString(16).toUpperCase();\r\n        if (str.length === 1) {\r\n            result += \"0\";\r\n        }\r\n        result += str;\r\n        if (insertSpace) {\r\n            result += \" \";\r\n        }\r\n    }\r\n    return result.trim();\r\n}\r\nfunction checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength) {\r\n    if (!(inputBuffer instanceof ArrayBuffer)) {\r\n        baseBlock.error = \"Wrong parameter: inputBuffer must be \\\"ArrayBuffer\\\"\";\r\n        return false;\r\n    }\r\n    if (!inputBuffer.byteLength) {\r\n        baseBlock.error = \"Wrong parameter: inputBuffer has zero length\";\r\n        return false;\r\n    }\r\n    if (inputOffset < 0) {\r\n        baseBlock.error = \"Wrong parameter: inputOffset less than zero\";\r\n        return false;\r\n    }\r\n    if (inputLength < 0) {\r\n        baseBlock.error = \"Wrong parameter: inputLength less than zero\";\r\n        return false;\r\n    }\r\n    if ((inputBuffer.byteLength - inputOffset - inputLength) < 0) {\r\n        baseBlock.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\r\n        return false;\r\n    }\r\n    return true;\r\n}\r\nfunction utilFromBase(inputBuffer, inputBase) {\r\n    let result = 0;\r\n    if (inputBuffer.length === 1) {\r\n        return inputBuffer[0];\r\n    }\r\n    for (let i = (inputBuffer.length - 1); i >= 0; i--) {\r\n        result += inputBuffer[(inputBuffer.length - 1) - i] * Math.pow(2, inputBase * i);\r\n    }\r\n    return result;\r\n}\r\nfunction utilToBase(value, base, reserved = (-1)) {\r\n    const internalReserved = reserved;\r\n    let internalValue = value;\r\n    let result = 0;\r\n    let biggest = Math.pow(2, base);\r\n    for (let i = 1; i < 8; i++) {\r\n        if (value < biggest) {\r\n            let retBuf;\r\n            if (internalReserved < 0) {\r\n                retBuf = new ArrayBuffer(i);\r\n                result = i;\r\n            }\r\n            else {\r\n                if (internalReserved < i) {\r\n                    return (new ArrayBuffer(0));\r\n                }\r\n                retBuf = new ArrayBuffer(internalReserved);\r\n                result = internalReserved;\r\n            }\r\n            const retView = new Uint8Array(retBuf);\r\n            for (let j = (i - 1); j >= 0; j--) {\r\n                const basis = Math.pow(2, j * base);\r\n                retView[result - j - 1] = Math.floor(internalValue / basis);\r\n                internalValue -= (retView[result - j - 1]) * basis;\r\n            }\r\n            return retBuf;\r\n        }\r\n        biggest *= Math.pow(2, base);\r\n    }\r\n    return new ArrayBuffer(0);\r\n}\r\nfunction utilConcatBuf(...buffers) {\r\n    let outputLength = 0;\r\n    let prevLength = 0;\r\n    for (const buffer of buffers) {\r\n        outputLength += buffer.byteLength;\r\n    }\r\n    const retBuf = new ArrayBuffer(outputLength);\r\n    const retView = new Uint8Array(retBuf);\r\n    for (const buffer of buffers) {\r\n        retView.set(new Uint8Array(buffer), prevLength);\r\n        prevLength += buffer.byteLength;\r\n    }\r\n    return retBuf;\r\n}\r\nfunction utilConcatView(...views) {\r\n    let outputLength = 0;\r\n    let prevLength = 0;\r\n    for (const view of views) {\r\n        outputLength += view.length;\r\n    }\r\n    const retBuf = new ArrayBuffer(outputLength);\r\n    const retView = new Uint8Array(retBuf);\r\n    for (const view of views) {\r\n        retView.set(view, prevLength);\r\n        prevLength += view.length;\r\n    }\r\n    return retView;\r\n}\r\nfunction utilDecodeTC() {\r\n    const buf = new Uint8Array(this.valueHex);\r\n    if (this.valueHex.byteLength >= 2) {\r\n        const condition1 = (buf[0] === 0xFF) && (buf[1] & 0x80);\r\n        const condition2 = (buf[0] === 0x00) && ((buf[1] & 0x80) === 0x00);\r\n        if (condition1 || condition2) {\r\n            this.warnings.push(\"Needlessly long format\");\r\n        }\r\n    }\r\n    const bigIntBuffer = new ArrayBuffer(this.valueHex.byteLength);\r\n    const bigIntView = new Uint8Array(bigIntBuffer);\r\n    for (let i = 0; i < this.valueHex.byteLength; i++) {\r\n        bigIntView[i] = 0;\r\n    }\r\n    bigIntView[0] = (buf[0] & 0x80);\r\n    const bigInt = utilFromBase(bigIntView, 8);\r\n    const smallIntBuffer = new ArrayBuffer(this.valueHex.byteLength);\r\n    const smallIntView = new Uint8Array(smallIntBuffer);\r\n    for (let j = 0; j < this.valueHex.byteLength; j++) {\r\n        smallIntView[j] = buf[j];\r\n    }\r\n    smallIntView[0] &= 0x7F;\r\n    const smallInt = utilFromBase(smallIntView, 8);\r\n    return (smallInt - bigInt);\r\n}\r\nfunction utilEncodeTC(value) {\r\n    const modValue = (value < 0) ? (value * (-1)) : value;\r\n    let bigInt = 128;\r\n    for (let i = 1; i < 8; i++) {\r\n        if (modValue <= bigInt) {\r\n            if (value < 0) {\r\n                const smallInt = bigInt - modValue;\r\n                const retBuf = utilToBase(smallInt, 8, i);\r\n                const retView = new Uint8Array(retBuf);\r\n                retView[0] |= 0x80;\r\n                return retBuf;\r\n            }\r\n            let retBuf = utilToBase(modValue, 8, i);\r\n            let retView = new Uint8Array(retBuf);\r\n            if (retView[0] & 0x80) {\r\n                const tempBuf = retBuf.slice(0);\r\n                const tempView = new Uint8Array(tempBuf);\r\n                retBuf = new ArrayBuffer(retBuf.byteLength + 1);\r\n                retView = new Uint8Array(retBuf);\r\n                for (let k = 0; k < tempBuf.byteLength; k++) {\r\n                    retView[k + 1] = tempView[k];\r\n                }\r\n                retView[0] = 0x00;\r\n            }\r\n            return retBuf;\r\n        }\r\n        bigInt *= Math.pow(2, 8);\r\n    }\r\n    return (new ArrayBuffer(0));\r\n}\r\nfunction isEqualBuffer(inputBuffer1, inputBuffer2) {\r\n    if (inputBuffer1.byteLength !== inputBuffer2.byteLength) {\r\n        return false;\r\n    }\r\n    const view1 = new Uint8Array(inputBuffer1);\r\n    const view2 = new Uint8Array(inputBuffer2);\r\n    for (let i = 0; i < view1.length; i++) {\r\n        if (view1[i] !== view2[i]) {\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\nfunction padNumber(inputNumber, fullLength) {\r\n    const str = inputNumber.toString(10);\r\n    if (fullLength < str.length) {\r\n        return \"\";\r\n    }\r\n    const dif = fullLength - str.length;\r\n    const padding = new Array(dif);\r\n    for (let i = 0; i < dif; i++) {\r\n        padding[i] = \"0\";\r\n    }\r\n    const paddingString = padding.join(\"\");\r\n    return paddingString.concat(str);\r\n}\r\nconst base64Template = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\r\nconst base64UrlTemplate = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_=\";\r\nfunction toBase64(input, useUrlTemplate = false, skipPadding = false, skipLeadingZeros = false) {\r\n    let i = 0;\r\n    let flag1 = 0;\r\n    let flag2 = 0;\r\n    let output = \"\";\r\n    const template = (useUrlTemplate) ? base64UrlTemplate : base64Template;\r\n    if (skipLeadingZeros) {\r\n        let nonZeroPosition = 0;\r\n        for (let i = 0; i < input.length; i++) {\r\n            if (input.charCodeAt(i) !== 0) {\r\n                nonZeroPosition = i;\r\n                break;\r\n            }\r\n        }\r\n        input = input.slice(nonZeroPosition);\r\n    }\r\n    while (i < input.length) {\r\n        const chr1 = input.charCodeAt(i++);\r\n        if (i >= input.length) {\r\n            flag1 = 1;\r\n        }\r\n        const chr2 = input.charCodeAt(i++);\r\n        if (i >= input.length) {\r\n            flag2 = 1;\r\n        }\r\n        const chr3 = input.charCodeAt(i++);\r\n        const enc1 = chr1 >> 2;\r\n        const enc2 = ((chr1 & 0x03) << 4) | (chr2 >> 4);\r\n        let enc3 = ((chr2 & 0x0F) << 2) | (chr3 >> 6);\r\n        let enc4 = chr3 & 0x3F;\r\n        if (flag1 === 1) {\r\n            enc3 = enc4 = 64;\r\n        }\r\n        else {\r\n            if (flag2 === 1) {\r\n                enc4 = 64;\r\n            }\r\n        }\r\n        if (skipPadding) {\r\n            if (enc3 === 64) {\r\n                output += `${template.charAt(enc1)}${template.charAt(enc2)}`;\r\n            }\r\n            else {\r\n                if (enc4 === 64) {\r\n                    output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}`;\r\n                }\r\n                else {\r\n                    output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}${template.charAt(enc4)}`;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            output += `${template.charAt(enc1)}${template.charAt(enc2)}${template.charAt(enc3)}${template.charAt(enc4)}`;\r\n        }\r\n    }\r\n    return output;\r\n}\r\nfunction fromBase64(input, useUrlTemplate = false, cutTailZeros = false) {\r\n    const template = (useUrlTemplate) ? base64UrlTemplate : base64Template;\r\n    function indexOf(toSearch) {\r\n        for (let i = 0; i < 64; i++) {\r\n            if (template.charAt(i) === toSearch)\r\n                return i;\r\n        }\r\n        return 64;\r\n    }\r\n    function test(incoming) {\r\n        return ((incoming === 64) ? 0x00 : incoming);\r\n    }\r\n    let i = 0;\r\n    let output = \"\";\r\n    while (i < input.length) {\r\n        const enc1 = indexOf(input.charAt(i++));\r\n        const enc2 = (i >= input.length) ? 0x00 : indexOf(input.charAt(i++));\r\n        const enc3 = (i >= input.length) ? 0x00 : indexOf(input.charAt(i++));\r\n        const enc4 = (i >= input.length) ? 0x00 : indexOf(input.charAt(i++));\r\n        const chr1 = (test(enc1) << 2) | (test(enc2) >> 4);\r\n        const chr2 = ((test(enc2) & 0x0F) << 4) | (test(enc3) >> 2);\r\n        const chr3 = ((test(enc3) & 0x03) << 6) | test(enc4);\r\n        output += String.fromCharCode(chr1);\r\n        if (enc3 !== 64) {\r\n            output += String.fromCharCode(chr2);\r\n        }\r\n        if (enc4 !== 64) {\r\n            output += String.fromCharCode(chr3);\r\n        }\r\n    }\r\n    if (cutTailZeros) {\r\n        const outputLength = output.length;\r\n        let nonZeroStart = (-1);\r\n        for (let i = (outputLength - 1); i >= 0; i--) {\r\n            if (output.charCodeAt(i) !== 0) {\r\n                nonZeroStart = i;\r\n                break;\r\n            }\r\n        }\r\n        if (nonZeroStart !== (-1)) {\r\n            output = output.slice(0, nonZeroStart + 1);\r\n        }\r\n        else {\r\n            output = \"\";\r\n        }\r\n    }\r\n    return output;\r\n}\r\nfunction arrayBufferToString(buffer) {\r\n    let resultString = \"\";\r\n    const view = new Uint8Array(buffer);\r\n    for (const element of view) {\r\n        resultString += String.fromCharCode(element);\r\n    }\r\n    return resultString;\r\n}\r\nfunction stringToArrayBuffer(str) {\r\n    const stringLength = str.length;\r\n    const resultBuffer = new ArrayBuffer(stringLength);\r\n    const resultView = new Uint8Array(resultBuffer);\r\n    for (let i = 0; i < stringLength; i++) {\r\n        resultView[i] = str.charCodeAt(i);\r\n    }\r\n    return resultBuffer;\r\n}\r\nconst log2 = Math.log(2);\r\nfunction nearestPowerOf2(length) {\r\n    const base = (Math.log(length) / log2);\r\n    const floor = Math.floor(base);\r\n    const round = Math.round(base);\r\n    return ((floor === round) ? floor : round);\r\n}\r\nfunction clearProps(object, propsArray) {\r\n    for (const prop of propsArray) {\r\n        delete object[prop];\r\n    }\r\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/pvutils/build/utils.es.js\n");

/***/ })

};
;