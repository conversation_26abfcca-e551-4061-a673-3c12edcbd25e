"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/webcrypto-core";
exports.ids = ["vendor-chunks/webcrypto-core"];
exports.modules = {

/***/ "(rsc)/../../node_modules/webcrypto-core/build/webcrypto-core.es.js":
/*!********************************************************************!*\
  !*** ../../node_modules/webcrypto-core/build/webcrypto-core.es.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AesCbcProvider: () => (/* binding */ AesCbcProvider),\n/* harmony export */   AesCmacProvider: () => (/* binding */ AesCmacProvider),\n/* harmony export */   AesCtrProvider: () => (/* binding */ AesCtrProvider),\n/* harmony export */   AesEcbProvider: () => (/* binding */ AesEcbProvider),\n/* harmony export */   AesGcmProvider: () => (/* binding */ AesGcmProvider),\n/* harmony export */   AesKwProvider: () => (/* binding */ AesKwProvider),\n/* harmony export */   AesProvider: () => (/* binding */ AesProvider),\n/* harmony export */   AlgorithmError: () => (/* binding */ AlgorithmError),\n/* harmony export */   BufferSourceConverter: () => (/* reexport safe */ pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter),\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoError: () => (/* binding */ CryptoError),\n/* harmony export */   CryptoKey: () => (/* binding */ CryptoKey),\n/* harmony export */   DesProvider: () => (/* binding */ DesProvider),\n/* harmony export */   EcCurves: () => (/* binding */ EcCurves),\n/* harmony export */   EcUtils: () => (/* binding */ EcUtils),\n/* harmony export */   EcdhEsProvider: () => (/* binding */ EcdhEsProvider),\n/* harmony export */   EcdhProvider: () => (/* binding */ EcdhProvider),\n/* harmony export */   EcdsaProvider: () => (/* binding */ EcdsaProvider),\n/* harmony export */   Ed25519Provider: () => (/* binding */ Ed25519Provider),\n/* harmony export */   EdDsaProvider: () => (/* binding */ EdDsaProvider),\n/* harmony export */   EllipticProvider: () => (/* binding */ EllipticProvider),\n/* harmony export */   HkdfProvider: () => (/* binding */ HkdfProvider),\n/* harmony export */   HmacProvider: () => (/* binding */ HmacProvider),\n/* harmony export */   JwkUtils: () => (/* binding */ JwkUtils),\n/* harmony export */   OperationError: () => (/* binding */ OperationError),\n/* harmony export */   Pbkdf2Provider: () => (/* binding */ Pbkdf2Provider),\n/* harmony export */   PemConverter: () => (/* binding */ PemConverter),\n/* harmony export */   ProviderCrypto: () => (/* binding */ ProviderCrypto),\n/* harmony export */   ProviderStorage: () => (/* binding */ ProviderStorage),\n/* harmony export */   RequiredPropertyError: () => (/* binding */ RequiredPropertyError),\n/* harmony export */   RsaOaepProvider: () => (/* binding */ RsaOaepProvider),\n/* harmony export */   RsaProvider: () => (/* binding */ RsaProvider),\n/* harmony export */   RsaPssProvider: () => (/* binding */ RsaPssProvider),\n/* harmony export */   RsaSsaProvider: () => (/* binding */ RsaSsaProvider),\n/* harmony export */   Shake128Provider: () => (/* binding */ Shake128Provider),\n/* harmony export */   Shake256Provider: () => (/* binding */ Shake256Provider),\n/* harmony export */   ShakeProvider: () => (/* binding */ ShakeProvider),\n/* harmony export */   SubtleCrypto: () => (/* binding */ SubtleCrypto),\n/* harmony export */   UnsupportedOperationError: () => (/* binding */ UnsupportedOperationError),\n/* harmony export */   X25519Provider: () => (/* binding */ X25519Provider),\n/* harmony export */   asn1: () => (/* binding */ index$1),\n/* harmony export */   isJWK: () => (/* binding */ isJWK),\n/* harmony export */   json: () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! tslib */ \"(rsc)/../../node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @peculiar/json-schema */ \"(rsc)/../../node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\n\n\n\n\n\n\n\nclass CryptoError extends Error {\n}\n\nclass AlgorithmError extends CryptoError {\n}\n\nclass UnsupportedOperationError extends CryptoError {\n    constructor(methodName) {\n        super(`Unsupported operation: ${methodName ? `${methodName}` : \"\"}`);\n    }\n}\n\nclass OperationError extends CryptoError {\n}\n\nclass RequiredPropertyError extends CryptoError {\n    constructor(propName) {\n        super(`${propName}: Missing required property`);\n    }\n}\n\nclass PemConverter {\n    static toArrayBuffer(pem) {\n        const base64 = pem\n            .replace(/-{5}(BEGIN|END) .*-{5}/g, \"\")\n            .replace(\"\\r\", \"\")\n            .replace(\"\\n\", \"\");\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64(base64);\n    }\n    static toUint8Array(pem) {\n        const bytes = this.toArrayBuffer(pem);\n        return new Uint8Array(bytes);\n    }\n    static fromBufferSource(buffer, tag) {\n        const base64 = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(buffer);\n        let sliced;\n        let offset = 0;\n        const rows = [];\n        while (offset < base64.length) {\n            sliced = base64.slice(offset, offset + 64);\n            if (sliced.length) {\n                rows.push(sliced);\n            }\n            else {\n                break;\n            }\n            offset += 64;\n        }\n        const upperCaseTag = tag.toUpperCase();\n        return `-----BEGIN ${upperCaseTag}-----\\n${rows.join(\"\\n\")}\\n-----END ${upperCaseTag}-----`;\n    }\n    static isPEM(data) {\n        return /-----BEGIN .+-----[A-Za-z0-9+/+=\\s\\n]+-----END .+-----/i.test(data);\n    }\n    static getTagName(pem) {\n        if (!this.isPEM(pem)) {\n            throw new Error(\"Bad parameter. Incoming data is not right PEM\");\n        }\n        const res = /-----BEGIN (.+)-----/.exec(pem);\n        if (!res) {\n            throw new Error(\"Cannot get tag from PEM\");\n        }\n        return res[1];\n    }\n    static hasTagName(pem, tagName) {\n        const tag = this.getTagName(pem);\n        return tagName.toLowerCase() === tag.toLowerCase();\n    }\n    static isCertificate(pem) {\n        return this.hasTagName(pem, \"certificate\");\n    }\n    static isCertificateRequest(pem) {\n        return this.hasTagName(pem, \"certificate request\");\n    }\n    static isCRL(pem) {\n        return this.hasTagName(pem, \"x509 crl\");\n    }\n    static isPublicKey(pem) {\n        return this.hasTagName(pem, \"public key\");\n    }\n}\n\nfunction isJWK(data) {\n    return typeof data === \"object\" && \"kty\" in data;\n}\n\nclass ProviderCrypto {\n    async digest(...args) {\n        this.checkDigest.apply(this, args);\n        return this.onDigest.apply(this, args);\n    }\n    checkDigest(algorithm, _data) {\n        this.checkAlgorithmName(algorithm);\n    }\n    async onDigest(_algorithm, _data) {\n        throw new UnsupportedOperationError(\"digest\");\n    }\n    async generateKey(...args) {\n        this.checkGenerateKey.apply(this, args);\n        return this.onGenerateKey.apply(this, args);\n    }\n    checkGenerateKey(algorithm, _extractable, keyUsages, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkGenerateKeyParams(algorithm);\n        if (!(keyUsages && keyUsages.length)) {\n            throw new TypeError(`Usages cannot be empty when creating a key.`);\n        }\n        let allowedUsages;\n        if (Array.isArray(this.usages)) {\n            allowedUsages = this.usages;\n        }\n        else {\n            allowedUsages = this.usages.privateKey.concat(this.usages.publicKey);\n        }\n        this.checkKeyUsages(keyUsages, allowedUsages);\n    }\n    checkGenerateKeyParams(_algorithm) {\n    }\n    async onGenerateKey(_algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"generateKey\");\n    }\n    async sign(...args) {\n        this.checkSign.apply(this, args);\n        return this.onSign.apply(this, args);\n    }\n    checkSign(algorithm, key, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"sign\");\n    }\n    async onSign(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"sign\");\n    }\n    async verify(...args) {\n        this.checkVerify.apply(this, args);\n        return this.onVerify.apply(this, args);\n    }\n    checkVerify(algorithm, key, _signature, _data, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, \"verify\");\n    }\n    async onVerify(_algorithm, _key, _signature, _data, ..._args) {\n        throw new UnsupportedOperationError(\"verify\");\n    }\n    async encrypt(...args) {\n        this.checkEncrypt.apply(this, args);\n        return this.onEncrypt.apply(this, args);\n    }\n    checkEncrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"encrypt\" : void 0);\n    }\n    async onEncrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"encrypt\");\n    }\n    async decrypt(...args) {\n        this.checkDecrypt.apply(this, args);\n        return this.onDecrypt.apply(this, args);\n    }\n    checkDecrypt(algorithm, key, _data, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(key, options.keyUsage ? \"decrypt\" : void 0);\n    }\n    async onDecrypt(_algorithm, _key, _data, ..._args) {\n        throw new UnsupportedOperationError(\"decrypt\");\n    }\n    async deriveBits(...args) {\n        this.checkDeriveBits.apply(this, args);\n        return this.onDeriveBits.apply(this, args);\n    }\n    checkDeriveBits(algorithm, baseKey, length, options = {}, ..._args) {\n        this.checkAlgorithmName(algorithm);\n        this.checkAlgorithmParams(algorithm);\n        this.checkCryptoKey(baseKey, options.keyUsage ? \"deriveBits\" : void 0);\n        if (length % 8 !== 0) {\n            throw new OperationError(\"length: Is not multiple of 8\");\n        }\n    }\n    async onDeriveBits(_algorithm, _baseKey, _length, ..._args) {\n        throw new UnsupportedOperationError(\"deriveBits\");\n    }\n    async exportKey(...args) {\n        this.checkExportKey.apply(this, args);\n        return this.onExportKey.apply(this, args);\n    }\n    checkExportKey(format, key, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkCryptoKey(key);\n        if (!key.extractable) {\n            throw new CryptoError(\"key: Is not extractable\");\n        }\n    }\n    async onExportKey(_format, _key, ..._args) {\n        throw new UnsupportedOperationError(\"exportKey\");\n    }\n    async importKey(...args) {\n        this.checkImportKey.apply(this, args);\n        return this.onImportKey.apply(this, args);\n    }\n    checkImportKey(format, keyData, algorithm, _extractable, keyUsages, ..._args) {\n        this.checkKeyFormat(format);\n        this.checkKeyData(format, keyData);\n        this.checkAlgorithmName(algorithm);\n        this.checkImportParams(algorithm);\n        if (Array.isArray(this.usages)) {\n            this.checkKeyUsages(keyUsages, this.usages);\n        }\n    }\n    async onImportKey(_format, _keyData, _algorithm, _extractable, _keyUsages, ..._args) {\n        throw new UnsupportedOperationError(\"importKey\");\n    }\n    checkAlgorithmName(algorithm) {\n        if (algorithm.name.toLowerCase() !== this.name.toLowerCase()) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n    }\n    checkAlgorithmParams(_algorithm) {\n    }\n    checkDerivedKeyParams(_algorithm) {\n    }\n    checkKeyUsages(usages, allowed) {\n        for (const usage of usages) {\n            if (allowed.indexOf(usage) === -1) {\n                throw new TypeError(\"Cannot create a key using the specified key usages\");\n            }\n        }\n    }\n    checkCryptoKey(key, keyUsage) {\n        this.checkAlgorithmName(key.algorithm);\n        if (keyUsage && key.usages.indexOf(keyUsage) === -1) {\n            throw new CryptoError(`key does not match that of operation`);\n        }\n    }\n    checkRequiredProperty(data, propName) {\n        if (!(propName in data)) {\n            throw new RequiredPropertyError(propName);\n        }\n    }\n    checkHashAlgorithm(algorithm, hashAlgorithms) {\n        for (const item of hashAlgorithms) {\n            if (item.toLowerCase() === algorithm.name.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`hash: Must be one of ${hashAlgorithms.join(\", \")}`);\n    }\n    checkImportParams(_algorithm) {\n    }\n    checkKeyFormat(format) {\n        switch (format) {\n            case \"raw\":\n            case \"pkcs8\":\n            case \"spki\":\n            case \"jwk\":\n                break;\n            default:\n                throw new TypeError(\"format: Is invalid value. Must be 'jwk', 'raw', 'spki', or 'pkcs8'\");\n        }\n    }\n    checkKeyData(format, keyData) {\n        if (!keyData) {\n            throw new TypeError(\"keyData: Cannot be empty on empty on key importing\");\n        }\n        if (format === \"jwk\") {\n            if (!isJWK(keyData)) {\n                throw new TypeError(\"keyData: Is not JsonWebToken\");\n            }\n        }\n        else if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n            throw new TypeError(\"keyData: Is not ArrayBufferView or ArrayBuffer\");\n        }\n    }\n    prepareData(data) {\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n    }\n}\n\nclass AesProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        switch (algorithm.length) {\n            case 128:\n            case 192:\n            case 256:\n                break;\n            default:\n                throw new TypeError(\"length: Must be 128, 192, or 256\");\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\n\nclass AesCbcProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CBC\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n    }\n}\n\nclass AesCmacProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CMAC\";\n        this.usages = [\"sign\", \"verify\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\n\nclass AesCtrProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-CTR\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"counter\");\n        if (!(algorithm.counter instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.counter))) {\n            throw new TypeError(\"counter: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.counter.byteLength !== 16) {\n            throw new TypeError(\"iv: Must have length 16 bytes\");\n        }\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (algorithm.length < 1) {\n            throw new OperationError(\"length: Must be more than 0\");\n        }\n    }\n}\n\nclass AesEcbProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-ECB\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n}\n\nclass AesGcmProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-GCM\";\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        var _a;\n        this.checkRequiredProperty(algorithm, \"iv\");\n        if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n            throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        if (algorithm.iv.byteLength < 1) {\n            throw new OperationError(\"iv: Must have length more than 0 and less than 2^64 - 1\");\n        }\n        (_a = algorithm.tagLength) !== null && _a !== void 0 ? _a : (algorithm.tagLength = 128);\n        switch (algorithm.tagLength) {\n            case 32:\n            case 64:\n            case 96:\n            case 104:\n            case 112:\n            case 120:\n            case 128:\n                break;\n            default:\n                throw new OperationError(\"tagLength: Must be one of 32, 64, 96, 104, 112, 120 or 128\");\n        }\n    }\n}\n\nclass AesKwProvider extends AesProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"AES-KW\";\n        this.usages = [\"wrapKey\", \"unwrapKey\"];\n    }\n}\n\nclass DesProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.usages = [\"encrypt\", \"decrypt\", \"wrapKey\", \"unwrapKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        if (this.ivSize) {\n            this.checkRequiredProperty(algorithm, \"iv\");\n            if (!(algorithm.iv instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.iv))) {\n                throw new TypeError(\"iv: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n            }\n            if (algorithm.iv.byteLength !== this.ivSize) {\n                throw new TypeError(`iv: Must have length ${this.ivSize} bytes`);\n            }\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"length\");\n        if (typeof algorithm.length !== \"number\") {\n            throw new TypeError(\"length: Is not of type Number\");\n        }\n        if (algorithm.length !== this.keySizeBits) {\n            throw new OperationError(`algorithm.length: Must be ${this.keySizeBits}`);\n        }\n    }\n    checkDerivedKeyParams(algorithm) {\n        this.checkGenerateKeyParams(algorithm);\n    }\n}\n\nclass RsaProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\n        }\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64(algorithm.publicExponent);\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\n        }\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\n        if (algorithm.modulusLength % 8\n            || algorithm.modulusLength < 256\n            || algorithm.modulusLength > 16384) {\n            throw new TypeError(\"The modulus length must be a multiple of 8 bits and >= 256 and <= 16384\");\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nclass RsaSsaProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSASSA-PKCS1-v1_5\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n}\n\nclass RsaPssProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSA-PSS\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"saltLength\");\n        if (typeof algorithm.saltLength !== \"number\") {\n            throw new TypeError(\"saltLength: Is not a Number\");\n        }\n        if (algorithm.saltLength < 0) {\n            throw new RangeError(\"saltLength: Must be positive number\");\n        }\n    }\n}\n\nclass RsaOaepProvider extends RsaProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"RSA-OAEP\";\n        this.usages = {\n            privateKey: [\"decrypt\", \"unwrapKey\"],\n            publicKey: [\"encrypt\", \"wrapKey\"],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        if (algorithm.label\n            && !(algorithm.label instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.label))) {\n            throw new TypeError(\"label: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n}\n\nclass EllipticProvider extends ProviderCrypto {\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"namedCurve\");\n        this.checkNamedCurve(algorithm.namedCurve);\n    }\n    checkNamedCurve(namedCurve) {\n        for (const item of this.namedCurves) {\n            if (item.toLowerCase() === namedCurve.toLowerCase()) {\n                return;\n            }\n        }\n        throw new OperationError(`namedCurve: Must be one of ${this.namedCurves.join(\", \")}`);\n    }\n}\n\nclass EcdsaProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDSA\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n        this.namedCurves = [\"P-256\", \"P-384\", \"P-521\", \"K-256\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nconst KEY_TYPES = [\"secret\", \"private\", \"public\"];\nclass CryptoKey {\n    static create(algorithm, type, extractable, usages) {\n        const key = new this();\n        key.algorithm = algorithm;\n        key.type = type;\n        key.extractable = extractable;\n        key.usages = usages;\n        return key;\n    }\n    static isKeyType(data) {\n        return KEY_TYPES.indexOf(data) !== -1;\n    }\n    get [Symbol.toStringTag]() {\n        return \"CryptoKey\";\n    }\n}\n\nclass EcdhProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDH\";\n        this.usages = {\n            privateKey: [\"deriveBits\", \"deriveKey\"],\n            publicKey: [],\n        };\n        this.namedCurves = [\"P-256\", \"P-384\", \"P-521\", \"K-256\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n        if (!(algorithm.public instanceof CryptoKey)) {\n            throw new TypeError(\"public: Is not a CryptoKey\");\n        }\n        if (algorithm.public.type !== \"public\") {\n            throw new OperationError(\"public: Is not a public key\");\n        }\n        if (algorithm.public.algorithm.name !== this.name) {\n            throw new OperationError(`public: Is not ${this.name} key`);\n        }\n    }\n}\n\nclass EcdhEsProvider extends EcdhProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"ECDH-ES\";\n        this.namedCurves = [\"X25519\", \"X448\"];\n    }\n}\n\nclass EdDsaProvider extends EllipticProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"EdDSA\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n        this.namedCurves = [\"Ed25519\", \"Ed448\"];\n    }\n}\n\nlet ObjectIdentifier = class ObjectIdentifier {\n    constructor(value) {\n        if (value) {\n            this.value = value;\n        }\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier })\n], ObjectIdentifier.prototype, \"value\", void 0);\nObjectIdentifier = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], ObjectIdentifier);\n\nclass AlgorithmIdentifier {\n    constructor(params) {\n        Object.assign(this, params);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier,\n    })\n], AlgorithmIdentifier.prototype, \"algorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({\n        type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any,\n        optional: true,\n    })\n], AlgorithmIdentifier.prototype, \"parameters\", void 0);\n\nclass PrivateKeyInfo {\n    constructor() {\n        this.version = 0;\n        this.privateKeyAlgorithm = new AlgorithmIdentifier();\n        this.privateKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer })\n], PrivateKeyInfo.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: AlgorithmIdentifier })\n], PrivateKeyInfo.prototype, \"privateKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], PrivateKeyInfo.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], PrivateKeyInfo.prototype, \"attributes\", void 0);\n\nclass PublicKeyInfo {\n    constructor() {\n        this.publicKeyAlgorithm = new AlgorithmIdentifier();\n        this.publicKey = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: AlgorithmIdentifier })\n], PublicKeyInfo.prototype, \"publicKeyAlgorithm\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString })\n], PublicKeyInfo.prototype, \"publicKey\", void 0);\n\nconst JsonBase64UrlArrayBufferConverter = {\n    fromJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(value),\n    toJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(new Uint8Array(value)),\n};\n\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value) => {\n        const valueHex = value.valueBlock.valueHex;\n        return !(new Uint8Array(valueHex)[0])\n            ? value.valueBlock.valueHex.slice(1)\n            : value.valueBlock.valueHex;\n    },\n    toASN: (value) => {\n        const valueHex = new Uint8Array(value)[0] > 127\n            ? (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([0]).buffer, value)\n            : value;\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex });\n    },\n};\n\nvar index$3 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AsnIntegerArrayBufferConverter: AsnIntegerArrayBufferConverter,\n  JsonBase64UrlArrayBufferConverter: JsonBase64UrlArrayBufferConverter\n});\n\nclass RsaPrivateKey {\n    constructor() {\n        this.version = 0;\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n        this.privateExponent = new ArrayBuffer(0);\n        this.prime1 = new ArrayBuffer(0);\n        this.prime2 = new ArrayBuffer(0);\n        this.exponent1 = new ArrayBuffer(0);\n        this.exponent2 = new ArrayBuffer(0);\n        this.coefficient = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter })\n], RsaPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"n\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"e\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"publicExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"d\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"privateExponent\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"p\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"prime1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"q\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"prime2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"dp\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"exponent1\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"dq\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"exponent2\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"qi\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPrivateKey.prototype, \"coefficient\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], RsaPrivateKey.prototype, \"otherPrimeInfos\", void 0);\n\nclass RsaPublicKey {\n    constructor() {\n        this.modulus = new ArrayBuffer(0);\n        this.publicExponent = new ArrayBuffer(0);\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"n\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPublicKey.prototype, \"modulus\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerArrayBufferConverter }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ name: \"e\", converter: JsonBase64UrlArrayBufferConverter })\n], RsaPublicKey.prototype, \"publicExponent\", void 0);\n\nlet EcPublicKey = class EcPublicKey {\n    constructor(value) {\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        let bytes = new Uint8Array(this.value);\n        if (bytes[0] !== 0x04) {\n            throw new CryptoError(\"Wrong ECPoint. Current version supports only Uncompressed (0x04) point\");\n        }\n        bytes = new Uint8Array(this.value.slice(1));\n        const size = bytes.length / 2;\n        const offset = 0;\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset, offset + size)),\n            y: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(bytes.buffer.slice(offset + size, offset + size + size)),\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        if (!(\"y\" in json)) {\n            throw new Error(\"y: Missing required property\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.y);\n        const value = (0,pvtsutils__WEBPACK_IMPORTED_MODULE_0__.combine)(new Uint8Array([0x04]).buffer, x, y);\n        this.value = new Uint8Array(value).buffer;\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EcPublicKey.prototype, \"value\", void 0);\nEcPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EcPublicKey);\n\nclass EcPrivateKey {\n    constructor() {\n        this.version = 1;\n        this.privateKey = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!(\"d\" in json)) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.privateKey = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        if (\"x\" in json) {\n            const publicKey = new EcPublicKey();\n            publicKey.fromJSON(json);\n            const asn = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnSerializer.toASN(publicKey);\n            if (\"valueHex\" in asn.valueBlock) {\n                this.publicKey = asn.valueBlock.valueHex;\n            }\n        }\n        return this;\n    }\n    toJSON() {\n        const jwk = {};\n        jwk.d = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.privateKey);\n        if (this.publicKey) {\n            Object.assign(jwk, new EcPublicKey(this.publicKey).toJSON());\n        }\n        return jwk;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnIntegerConverter })\n], EcPrivateKey.prototype, \"version\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EcPrivateKey.prototype, \"privateKey\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 0, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any, optional: true })\n], EcPrivateKey.prototype, \"parameters\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 1, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString, optional: true })\n], EcPrivateKey.prototype, \"publicKey\", void 0);\n\nconst AsnIntegerWithoutPaddingConverter = {\n    fromASN: (value) => {\n        const bytes = new Uint8Array(value.valueBlock.valueHex);\n        return (bytes[0] === 0)\n            ? bytes.buffer.slice(1)\n            : bytes.buffer;\n    },\n    toASN: (value) => {\n        const bytes = new Uint8Array(value);\n        if (bytes[0] > 127) {\n            const newValue = new Uint8Array(bytes.length + 1);\n            newValue.set(bytes, 1);\n            return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex: newValue.buffer });\n        }\n        return new asn1js__WEBPACK_IMPORTED_MODULE_3__.Integer({ valueHex: value });\n    },\n};\n\nvar index$2 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AsnIntegerWithoutPaddingConverter: AsnIntegerWithoutPaddingConverter\n});\n\nclass EcUtils {\n    static decodePoint(data, pointSize) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if ((view.length === 0) || (view[0] !== 4)) {\n            throw new Error(\"Only uncompressed point format supported\");\n        }\n        const n = (view.length - 1) / 2;\n        if (n !== (Math.ceil(pointSize / 8))) {\n            throw new Error(\"Point does not match field size\");\n        }\n        const xb = view.slice(1, n + 1);\n        const yb = view.slice(n + 1, n + 1 + n);\n        return { x: xb, y: yb };\n    }\n    static encodePoint(point, pointSize) {\n        const size = Math.ceil(pointSize / 8);\n        if (point.x.byteLength !== size || point.y.byteLength !== size) {\n            throw new Error(\"X,Y coordinates don't match point size criteria\");\n        }\n        const x = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.x);\n        const y = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(point.y);\n        const res = new Uint8Array(size * 2 + 1);\n        res[0] = 4;\n        res.set(x, 1);\n        res.set(y, size + 1);\n        return res;\n    }\n    static getSize(pointSize) {\n        return Math.ceil(pointSize / 8);\n    }\n    static encodeSignature(signature, pointSize) {\n        const size = this.getSize(pointSize);\n        const r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.r);\n        const s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(signature.s);\n        const res = new Uint8Array(size * 2);\n        res.set(this.padStart(r, size));\n        res.set(this.padStart(s, size), size);\n        return res;\n    }\n    static decodeSignature(data, pointSize) {\n        const size = this.getSize(pointSize);\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n        if (view.length !== (size * 2)) {\n            throw new Error(\"Incorrect size of the signature\");\n        }\n        const r = view.slice(0, size);\n        const s = view.slice(size);\n        return {\n            r: this.trimStart(r),\n            s: this.trimStart(s),\n        };\n    }\n    static trimStart(data) {\n        let i = 0;\n        while ((i < data.length - 1) && (data[i] === 0)) {\n            i++;\n        }\n        if (i === 0) {\n            return data;\n        }\n        return data.slice(i, data.length);\n    }\n    static padStart(data, size) {\n        if (size === data.length) {\n            return data;\n        }\n        const res = new Uint8Array(size);\n        res.set(data, size - data.length);\n        return res;\n    }\n}\n\nclass EcDsaSignature {\n    constructor() {\n        this.r = new ArrayBuffer(0);\n        this.s = new ArrayBuffer(0);\n    }\n    static fromWebCryptoSignature(value) {\n        const pointSize = value.byteLength / 2;\n        const point = EcUtils.decodeSignature(value, pointSize * 8);\n        const ecSignature = new EcDsaSignature();\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.r);\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(point.s);\n        return ecSignature;\n    }\n    toWebCryptoSignature(pointSize) {\n        if (!pointSize) {\n            const maxPointLength = Math.max(this.r.byteLength, this.s.byteLength);\n            if (maxPointLength <= 32) {\n                pointSize = 256;\n            }\n            else if (maxPointLength <= 48) {\n                pointSize = 384;\n            }\n            else {\n                pointSize = 521;\n            }\n        }\n        const signature = EcUtils.encodeSignature(this, pointSize);\n        return signature.buffer;\n    }\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerWithoutPaddingConverter })\n], EcDsaSignature.prototype, \"r\", void 0);\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer, converter: AsnIntegerWithoutPaddingConverter })\n], EcDsaSignature.prototype, \"s\", void 0);\n\nclass OneAsymmetricKey extends PrivateKeyInfo {\n}\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ context: 1, implicit: true, type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString, optional: true })\n], OneAsymmetricKey.prototype, \"publicKey\", void 0);\n\nlet EdPrivateKey = class EdPrivateKey {\n    constructor() {\n        this.value = new ArrayBuffer(0);\n    }\n    fromJSON(json) {\n        if (!json.d) {\n            throw new Error(\"d: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.d);\n        return this;\n    }\n    toJSON() {\n        const jwk = {\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value),\n        };\n        return jwk;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString })\n], EdPrivateKey.prototype, \"value\", void 0);\nEdPrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EdPrivateKey);\n\nlet EdPublicKey = class EdPublicKey {\n    constructor(value) {\n        this.value = new ArrayBuffer(0);\n        if (value) {\n            this.value = value;\n        }\n    }\n    toJSON() {\n        const json = {\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBase64Url(this.value),\n        };\n        return json;\n    }\n    fromJSON(json) {\n        if (!(\"x\" in json)) {\n            throw new Error(\"x: Missing required property\");\n        }\n        this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBase64Url(json.x);\n        return this;\n    }\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString })\n], EdPublicKey.prototype, \"value\", void 0);\nEdPublicKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], EdPublicKey);\n\nlet CurvePrivateKey = class CurvePrivateKey {\n};\n(0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnProp)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString }),\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_2__.JsonPropTypes.String, converter: JsonBase64UrlArrayBufferConverter })\n], CurvePrivateKey.prototype, \"d\", void 0);\nCurvePrivateKey = (0,tslib__WEBPACK_IMPORTED_MODULE_4__.__decorate)([\n    (0,_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnType)({ type: _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice })\n], CurvePrivateKey);\n\nconst idSecp256r1 = \"1.2.840.10045.3.1.7\";\nconst idEllipticCurve = \"*********\";\nconst idSecp384r1 = `${idEllipticCurve}.34`;\nconst idSecp521r1 = `${idEllipticCurve}.35`;\nconst idSecp256k1 = `${idEllipticCurve}.10`;\nconst idVersionOne = \"********.*******.1\";\nconst idBrainpoolP160r1 = `${idVersionOne}.1`;\nconst idBrainpoolP160t1 = `${idVersionOne}.2`;\nconst idBrainpoolP192r1 = `${idVersionOne}.3`;\nconst idBrainpoolP192t1 = `${idVersionOne}.4`;\nconst idBrainpoolP224r1 = `${idVersionOne}.5`;\nconst idBrainpoolP224t1 = `${idVersionOne}.6`;\nconst idBrainpoolP256r1 = `${idVersionOne}.7`;\nconst idBrainpoolP256t1 = `${idVersionOne}.8`;\nconst idBrainpoolP320r1 = `${idVersionOne}.9`;\nconst idBrainpoolP320t1 = `${idVersionOne}.10`;\nconst idBrainpoolP384r1 = `${idVersionOne}.11`;\nconst idBrainpoolP384t1 = `${idVersionOne}.12`;\nconst idBrainpoolP512r1 = `${idVersionOne}.13`;\nconst idBrainpoolP512t1 = `${idVersionOne}.14`;\nconst idX25519 = \"***********\";\nconst idX448 = \"***********\";\nconst idEd25519 = \"***********\";\nconst idEd448 = \"***********\";\n\nvar index$1 = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  AlgorithmIdentifier: AlgorithmIdentifier,\n  get CurvePrivateKey () { return CurvePrivateKey; },\n  EcDsaSignature: EcDsaSignature,\n  EcPrivateKey: EcPrivateKey,\n  get EcPublicKey () { return EcPublicKey; },\n  get EdPrivateKey () { return EdPrivateKey; },\n  get EdPublicKey () { return EdPublicKey; },\n  get ObjectIdentifier () { return ObjectIdentifier; },\n  OneAsymmetricKey: OneAsymmetricKey,\n  PrivateKeyInfo: PrivateKeyInfo,\n  PublicKeyInfo: PublicKeyInfo,\n  RsaPrivateKey: RsaPrivateKey,\n  RsaPublicKey: RsaPublicKey,\n  converters: index$2,\n  idBrainpoolP160r1: idBrainpoolP160r1,\n  idBrainpoolP160t1: idBrainpoolP160t1,\n  idBrainpoolP192r1: idBrainpoolP192r1,\n  idBrainpoolP192t1: idBrainpoolP192t1,\n  idBrainpoolP224r1: idBrainpoolP224r1,\n  idBrainpoolP224t1: idBrainpoolP224t1,\n  idBrainpoolP256r1: idBrainpoolP256r1,\n  idBrainpoolP256t1: idBrainpoolP256t1,\n  idBrainpoolP320r1: idBrainpoolP320r1,\n  idBrainpoolP320t1: idBrainpoolP320t1,\n  idBrainpoolP384r1: idBrainpoolP384r1,\n  idBrainpoolP384t1: idBrainpoolP384t1,\n  idBrainpoolP512r1: idBrainpoolP512r1,\n  idBrainpoolP512t1: idBrainpoolP512t1,\n  idEd25519: idEd25519,\n  idEd448: idEd448,\n  idEllipticCurve: idEllipticCurve,\n  idSecp256k1: idSecp256k1,\n  idSecp256r1: idSecp256r1,\n  idSecp384r1: idSecp384r1,\n  idSecp521r1: idSecp521r1,\n  idVersionOne: idVersionOne,\n  idX25519: idX25519,\n  idX448: idX448\n});\n\nclass EcCurves {\n    constructor() { }\n    static register(item) {\n        const oid = new ObjectIdentifier();\n        oid.value = item.id;\n        const raw = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_1__.AsnConvert.serialize(oid);\n        this.items.push({\n            ...item,\n            raw,\n        });\n        this.names.push(item.name);\n    }\n    static find(nameOrId) {\n        nameOrId = nameOrId.toUpperCase();\n        for (const item of this.items) {\n            if (item.name.toUpperCase() === nameOrId || item.id.toUpperCase() === nameOrId) {\n                return item;\n            }\n        }\n        return null;\n    }\n    static get(nameOrId) {\n        const res = this.find(nameOrId);\n        if (!res) {\n            throw new Error(`Unsupported EC named curve '${nameOrId}'`);\n        }\n        return res;\n    }\n}\nEcCurves.items = [];\nEcCurves.names = [];\nEcCurves.register({ name: \"P-256\", id: idSecp256r1, size: 256 });\nEcCurves.register({ name: \"P-384\", id: idSecp384r1, size: 384 });\nEcCurves.register({ name: \"P-521\", id: idSecp521r1, size: 521 });\nEcCurves.register({ name: \"K-256\", id: idSecp256k1, size: 256 });\nEcCurves.register({ name: \"brainpoolP160r1\", id: idBrainpoolP160r1, size: 160 });\nEcCurves.register({ name: \"brainpoolP160t1\", id: idBrainpoolP160t1, size: 160 });\nEcCurves.register({ name: \"brainpoolP192r1\", id: idBrainpoolP192r1, size: 192 });\nEcCurves.register({ name: \"brainpoolP192t1\", id: idBrainpoolP192t1, size: 192 });\nEcCurves.register({ name: \"brainpoolP224r1\", id: idBrainpoolP224r1, size: 224 });\nEcCurves.register({ name: \"brainpoolP224t1\", id: idBrainpoolP224t1, size: 224 });\nEcCurves.register({ name: \"brainpoolP256r1\", id: idBrainpoolP256r1, size: 256 });\nEcCurves.register({ name: \"brainpoolP256t1\", id: idBrainpoolP256t1, size: 256 });\nEcCurves.register({ name: \"brainpoolP320r1\", id: idBrainpoolP320r1, size: 320 });\nEcCurves.register({ name: \"brainpoolP320t1\", id: idBrainpoolP320t1, size: 320 });\nEcCurves.register({ name: \"brainpoolP384r1\", id: idBrainpoolP384r1, size: 384 });\nEcCurves.register({ name: \"brainpoolP384t1\", id: idBrainpoolP384t1, size: 384 });\nEcCurves.register({ name: \"brainpoolP512r1\", id: idBrainpoolP512r1, size: 512 });\nEcCurves.register({ name: \"brainpoolP512t1\", id: idBrainpoolP512t1, size: 512 });\n\nclass X25519Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"X25519\";\n        this.usages = {\n            privateKey: [\"deriveKey\", \"deriveBits\"],\n            publicKey: [],\n        };\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"public\");\n    }\n}\n\nclass Ed25519Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"Ed25519\";\n        this.usages = {\n            privateKey: [\"sign\"],\n            publicKey: [\"verify\"],\n        };\n    }\n}\n\nclass HmacProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"HMAC\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"sign\", \"verify\"];\n    }\n    getDefaultLength(algName) {\n        switch (algName.toUpperCase()) {\n            case \"SHA-1\":\n            case \"SHA-256\":\n            case \"SHA-384\":\n            case \"SHA-512\":\n                return 512;\n            default:\n                throw new Error(`Unknown algorithm name '${algName}'`);\n        }\n    }\n    checkGenerateKeyParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        if (\"length\" in algorithm) {\n            if (typeof algorithm.length !== \"number\") {\n                throw new TypeError(\"length: Is not a Number\");\n            }\n            if (algorithm.length < 1) {\n                throw new RangeError(\"length: Number is out of range\");\n            }\n        }\n    }\n    checkImportParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n    }\n}\n\nclass Pbkdf2Provider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"PBKDF2\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"deriveBits\", \"deriveKey\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!(algorithm.salt instanceof ArrayBuffer || ArrayBuffer.isView(algorithm.salt))) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"iterations\");\n        if (typeof algorithm.iterations !== \"number\") {\n            throw new TypeError(\"iterations: Is not a Number\");\n        }\n        if (algorithm.iterations < 1) {\n            throw new TypeError(\"iterations: Is less than 1\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\n\nclass HkdfProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.name = \"HKDF\";\n        this.hashAlgorithms = [\"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\"];\n        this.usages = [\"deriveKey\", \"deriveBits\"];\n    }\n    checkAlgorithmParams(algorithm) {\n        this.checkRequiredProperty(algorithm, \"hash\");\n        this.checkHashAlgorithm(algorithm.hash, this.hashAlgorithms);\n        this.checkRequiredProperty(algorithm, \"salt\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.salt)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n        this.checkRequiredProperty(algorithm, \"info\");\n        if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(algorithm.info)) {\n            throw new TypeError(\"salt: Is not of type '(ArrayBuffer or ArrayBufferView)'\");\n        }\n    }\n    checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args) {\n        super.checkImportKey(format, keyData, algorithm, extractable, keyUsages, ...args);\n        if (extractable) {\n            throw new SyntaxError(\"extractable: Must be 'false'\");\n        }\n    }\n}\n\nclass ShakeProvider extends ProviderCrypto {\n    constructor() {\n        super(...arguments);\n        this.usages = [];\n        this.defaultLength = 0;\n    }\n    digest(...args) {\n        args[0] = { length: this.defaultLength, ...args[0] };\n        return super.digest.apply(this, args);\n    }\n    checkDigest(algorithm, data) {\n        super.checkDigest(algorithm, data);\n        const length = algorithm.length || 0;\n        if (typeof length !== \"number\") {\n            throw new TypeError(\"length: Is not a Number\");\n        }\n        if (length < 0) {\n            throw new TypeError(\"length: Is negative\");\n        }\n    }\n}\n\nclass Shake128Provider extends ShakeProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"shake128\";\n        this.defaultLength = 16;\n    }\n}\n\nclass Shake256Provider extends ShakeProvider {\n    constructor() {\n        super(...arguments);\n        this.name = \"shake256\";\n        this.defaultLength = 32;\n    }\n}\n\nclass Crypto {\n    get [Symbol.toStringTag]() {\n        return \"Crypto\";\n    }\n    randomUUID() {\n        const b = this.getRandomValues(new Uint8Array(16));\n        b[6] = (b[6] & 0x0f) | 0x40;\n        b[8] = (b[8] & 0x3f) | 0x80;\n        const uuid = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(b).toLowerCase();\n        return `${uuid.substring(0, 8)}-${uuid.substring(8, 12)}-${uuid.substring(12, 16)}-${uuid.substring(16, 20)}-${uuid.substring(20)}`;\n    }\n}\n\nclass ProviderStorage {\n    constructor() {\n        this.items = {};\n    }\n    get(algorithmName) {\n        return this.items[algorithmName.toLowerCase()] || null;\n    }\n    set(provider) {\n        this.items[provider.name.toLowerCase()] = provider;\n    }\n    removeAt(algorithmName) {\n        const provider = this.get(algorithmName.toLowerCase());\n        if (provider) {\n            delete this.items[algorithmName];\n        }\n        return provider;\n    }\n    has(name) {\n        return !!this.get(name);\n    }\n    get length() {\n        return Object.keys(this.items).length;\n    }\n    get algorithms() {\n        const algorithms = [];\n        for (const key in this.items) {\n            const provider = this.items[key];\n            algorithms.push(provider.name);\n        }\n        return algorithms.sort();\n    }\n}\n\nconst keyFormatMap = {\n    \"jwk\": [\"private\", \"public\", \"secret\"],\n    \"pkcs8\": [\"private\"],\n    \"spki\": [\"public\"],\n    \"raw\": [\"secret\", \"public\"]\n};\nconst sourceBufferKeyFormats = [\"pkcs8\", \"spki\", \"raw\"];\nclass SubtleCrypto {\n    constructor() {\n        this.providers = new ProviderStorage();\n    }\n    static isHashedAlgorithm(data) {\n        return data\n            && typeof data === \"object\"\n            && \"name\" in data\n            && \"hash\" in data\n            ? true\n            : false;\n    }\n    get [Symbol.toStringTag]() {\n        return \"SubtleCrypto\";\n    }\n    async digest(...args) {\n        this.checkRequiredArguments(args, 2, \"digest\");\n        const [algorithm, data, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.digest(preparedAlgorithm, preparedData, ...params);\n        return result;\n    }\n    async generateKey(...args) {\n        this.checkRequiredArguments(args, 3, \"generateKey\");\n        const [algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.generateKey({ ...preparedAlgorithm, name: provider.name }, extractable, keyUsages, ...params);\n        return result;\n    }\n    async sign(...args) {\n        this.checkRequiredArguments(args, 3, \"sign\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.sign({ ...preparedAlgorithm, name: provider.name }, key, preparedData, ...params);\n        return result;\n    }\n    async verify(...args) {\n        this.checkRequiredArguments(args, 4, \"verify\");\n        const [algorithm, key, signature, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const preparedSignature = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.verify({ ...preparedAlgorithm, name: provider.name }, key, preparedSignature, preparedData, ...params);\n        return result;\n    }\n    async encrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"encrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.encrypt({ ...preparedAlgorithm, name: provider.name }, key, preparedData, { keyUsage: true }, ...params);\n        return result;\n    }\n    async decrypt(...args) {\n        this.checkRequiredArguments(args, 3, \"decrypt\");\n        const [algorithm, key, data, ...params] = args;\n        this.checkCryptoKey(key);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(data);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.decrypt({ ...preparedAlgorithm, name: provider.name }, key, preparedData, { keyUsage: true }, ...params);\n        return result;\n    }\n    async deriveBits(...args) {\n        this.checkRequiredArguments(args, 3, \"deriveBits\");\n        const [algorithm, baseKey, length, ...params] = args;\n        this.checkCryptoKey(baseKey);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        const result = await provider.deriveBits({ ...preparedAlgorithm, name: provider.name }, baseKey, length, { keyUsage: true }, ...params);\n        return result;\n    }\n    async deriveKey(...args) {\n        this.checkRequiredArguments(args, 5, \"deriveKey\");\n        const [algorithm, baseKey, derivedKeyType, extractable, keyUsages, ...params] = args;\n        const preparedDerivedKeyType = this.prepareAlgorithm(derivedKeyType);\n        const importProvider = this.getProvider(preparedDerivedKeyType.name);\n        importProvider.checkDerivedKeyParams(preparedDerivedKeyType);\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        provider.checkCryptoKey(baseKey, \"deriveKey\");\n        const derivedBits = await provider.deriveBits({ ...preparedAlgorithm, name: provider.name }, baseKey, derivedKeyType.length || 512, { keyUsage: false }, ...params);\n        return this.importKey(\"raw\", derivedBits, derivedKeyType, extractable, keyUsages, ...params);\n    }\n    async exportKey(...args) {\n        this.checkRequiredArguments(args, 2, \"exportKey\");\n        const [format, key, ...params] = args;\n        this.checkCryptoKey(key);\n        if (!keyFormatMap[format]) {\n            throw new TypeError(\"Invalid keyFormat argument\");\n        }\n        if (!keyFormatMap[format].includes(key.type)) {\n            throw new DOMException(\"The key is not of the expected type\");\n        }\n        const provider = this.getProvider(key.algorithm.name);\n        const result = await provider.exportKey(format, key, ...params);\n        return result;\n    }\n    async importKey(...args) {\n        this.checkRequiredArguments(args, 5, \"importKey\");\n        const [format, keyData, algorithm, extractable, keyUsages, ...params] = args;\n        const preparedAlgorithm = this.prepareAlgorithm(algorithm);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        if (format === \"jwk\") {\n            if (typeof keyData !== \"object\" || !keyData.kty) {\n                throw new TypeError(\"Key data must be an object for JWK import\");\n            }\n        }\n        else if (sourceBufferKeyFormats.includes(format)) {\n            if (!pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.isBufferSource(keyData)) {\n                throw new TypeError(\"Key data must be a BufferSource for non-JWK formats\");\n            }\n        }\n        else {\n            throw new TypeError(\"The provided value is not of type '(ArrayBuffer or ArrayBufferView or JsonWebKey)'\");\n        }\n        return provider.importKey(format, keyData, { ...preparedAlgorithm, name: provider.name }, extractable, keyUsages, ...params);\n    }\n    async wrapKey(format, key, wrappingKey, wrapAlgorithm, ...args) {\n        let keyData = await this.exportKey(format, key, ...args);\n        if (format === \"jwk\") {\n            const json = JSON.stringify(keyData);\n            keyData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(json);\n        }\n        const preparedAlgorithm = this.prepareAlgorithm(wrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(keyData);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        return provider.encrypt({ ...preparedAlgorithm, name: provider.name }, wrappingKey, preparedData, { keyUsage: false }, ...args);\n    }\n    async unwrapKey(format, wrappedKey, unwrappingKey, unwrapAlgorithm, unwrappedKeyAlgorithm, extractable, keyUsages, ...args) {\n        const preparedAlgorithm = this.prepareAlgorithm(unwrapAlgorithm);\n        const preparedData = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(wrappedKey);\n        const provider = this.getProvider(preparedAlgorithm.name);\n        let keyData = await provider.decrypt({ ...preparedAlgorithm, name: provider.name }, unwrappingKey, preparedData, { keyUsage: false }, ...args);\n        if (format === \"jwk\") {\n            try {\n                keyData = JSON.parse(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(keyData));\n            }\n            catch (e) {\n                const error = new TypeError(\"wrappedKey: Is not a JSON\");\n                error.internal = e;\n                throw error;\n            }\n        }\n        return this.importKey(format, keyData, unwrappedKeyAlgorithm, extractable, keyUsages, ...args);\n    }\n    checkRequiredArguments(args, size, methodName) {\n        if (args.length < size) {\n            throw new TypeError(`Failed to execute '${methodName}' on 'SubtleCrypto': ${size} arguments required, but only ${args.length} present`);\n        }\n    }\n    prepareAlgorithm(algorithm) {\n        if (typeof algorithm === \"string\") {\n            return {\n                name: algorithm,\n            };\n        }\n        if (SubtleCrypto.isHashedAlgorithm(algorithm)) {\n            const preparedAlgorithm = { ...algorithm };\n            preparedAlgorithm.hash = this.prepareAlgorithm(algorithm.hash);\n            return preparedAlgorithm;\n        }\n        return { ...algorithm };\n    }\n    getProvider(name) {\n        const provider = this.providers.get(name);\n        if (!provider) {\n            throw new AlgorithmError(\"Unrecognized name\");\n        }\n        return provider;\n    }\n    checkCryptoKey(key) {\n        if (!(key instanceof CryptoKey)) {\n            throw new TypeError(`Key is not of type 'CryptoKey'`);\n        }\n    }\n}\n\nvar index = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  converters: index$3\n});\n\nconst REQUIRED_FIELDS = [\"crv\", \"e\", \"k\", \"kty\", \"n\", \"x\", \"y\"];\nclass JwkUtils {\n    static async thumbprint(hash, jwk, crypto) {\n        const data = this.format(jwk, true);\n        return crypto.subtle.digest(hash, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromBinary(JSON.stringify(data)));\n    }\n    static format(jwk, remove = false) {\n        let res = Object.entries(jwk);\n        if (remove) {\n            res = res.filter(o => REQUIRED_FIELDS.includes(o[0]));\n        }\n        res = res.sort(([keyA], [keyB]) => keyA > keyB ? 1 : keyA < keyB ? -1 : 0);\n        return Object.fromEntries(res);\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/webcrypto-core/build/webcrypto-core.es.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs":
/*!**************************************************************************!*\
  !*** ../../node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __addDisposableResource: () => (/* binding */ __addDisposableResource),\n/* harmony export */   __assign: () => (/* binding */ __assign),\n/* harmony export */   __asyncDelegator: () => (/* binding */ __asyncDelegator),\n/* harmony export */   __asyncGenerator: () => (/* binding */ __asyncGenerator),\n/* harmony export */   __asyncValues: () => (/* binding */ __asyncValues),\n/* harmony export */   __await: () => (/* binding */ __await),\n/* harmony export */   __awaiter: () => (/* binding */ __awaiter),\n/* harmony export */   __classPrivateFieldGet: () => (/* binding */ __classPrivateFieldGet),\n/* harmony export */   __classPrivateFieldIn: () => (/* binding */ __classPrivateFieldIn),\n/* harmony export */   __classPrivateFieldSet: () => (/* binding */ __classPrivateFieldSet),\n/* harmony export */   __createBinding: () => (/* binding */ __createBinding),\n/* harmony export */   __decorate: () => (/* binding */ __decorate),\n/* harmony export */   __disposeResources: () => (/* binding */ __disposeResources),\n/* harmony export */   __esDecorate: () => (/* binding */ __esDecorate),\n/* harmony export */   __exportStar: () => (/* binding */ __exportStar),\n/* harmony export */   __extends: () => (/* binding */ __extends),\n/* harmony export */   __generator: () => (/* binding */ __generator),\n/* harmony export */   __importDefault: () => (/* binding */ __importDefault),\n/* harmony export */   __importStar: () => (/* binding */ __importStar),\n/* harmony export */   __makeTemplateObject: () => (/* binding */ __makeTemplateObject),\n/* harmony export */   __metadata: () => (/* binding */ __metadata),\n/* harmony export */   __param: () => (/* binding */ __param),\n/* harmony export */   __propKey: () => (/* binding */ __propKey),\n/* harmony export */   __read: () => (/* binding */ __read),\n/* harmony export */   __rest: () => (/* binding */ __rest),\n/* harmony export */   __rewriteRelativeImportExtension: () => (/* binding */ __rewriteRelativeImportExtension),\n/* harmony export */   __runInitializers: () => (/* binding */ __runInitializers),\n/* harmony export */   __setFunctionName: () => (/* binding */ __setFunctionName),\n/* harmony export */   __spread: () => (/* binding */ __spread),\n/* harmony export */   __spreadArray: () => (/* binding */ __spreadArray),\n/* harmony export */   __spreadArrays: () => (/* binding */ __spreadArrays),\n/* harmony export */   __values: () => (/* binding */ __values),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/webcrypto-core/node_modules/tslib/tslib.es6.mjs\n");

/***/ })

};
;