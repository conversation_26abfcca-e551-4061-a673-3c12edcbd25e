"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@peculiar";
exports.ids = ["vendor-chunks/@peculiar"];
exports.modules = {

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/convert.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/convert.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnConvert: () => (/* binding */ AsnConvert)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parser */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./serializer */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n\n\n\n\nclass AsnConvert {\n    static serialize(obj) {\n        return _serializer__WEBPACK_IMPORTED_MODULE_3__.AsnSerializer.serialize(obj);\n    }\n    static parse(data, target) {\n        return _parser__WEBPACK_IMPORTED_MODULE_2__.AsnParser.parse(data, target);\n    }\n    static toString(data) {\n        const buf = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(data)\n            ? pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(data)\n            : AsnConvert.serialize(data);\n        const asn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(buf);\n        if (asn.offset === -1) {\n            throw new Error(`Cannot decode ASN.1 data. ${asn.result.error}`);\n        }\n        return asn.result.toString();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvY29udmVydC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFpQztBQUNpQjtBQUNiO0FBQ1E7QUFDdEM7QUFDUDtBQUNBLGVBQWUsc0RBQWE7QUFDNUI7QUFDQTtBQUNBLGVBQWUsOENBQVM7QUFDeEI7QUFDQTtBQUNBLG9CQUFvQiw0REFBcUI7QUFDekMsY0FBYyw0REFBcUI7QUFDbkM7QUFDQSxvQkFBb0IsMkNBQWM7QUFDbEM7QUFDQSx5REFBeUQsaUJBQWlCO0FBQzFFO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvY29udmVydC5qcz9jMjJiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIGFzbjFqcyBmcm9tIFwiYXNuMWpzXCI7XG5pbXBvcnQgeyBCdWZmZXJTb3VyY2VDb252ZXJ0ZXIgfSBmcm9tIFwicHZ0c3V0aWxzXCI7XG5pbXBvcnQgeyBBc25QYXJzZXIgfSBmcm9tIFwiLi9wYXJzZXJcIjtcbmltcG9ydCB7IEFzblNlcmlhbGl6ZXIgfSBmcm9tIFwiLi9zZXJpYWxpemVyXCI7XG5leHBvcnQgY2xhc3MgQXNuQ29udmVydCB7XG4gICAgc3RhdGljIHNlcmlhbGl6ZShvYmopIHtcbiAgICAgICAgcmV0dXJuIEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKG9iaik7XG4gICAgfVxuICAgIHN0YXRpYyBwYXJzZShkYXRhLCB0YXJnZXQpIHtcbiAgICAgICAgcmV0dXJuIEFzblBhcnNlci5wYXJzZShkYXRhLCB0YXJnZXQpO1xuICAgIH1cbiAgICBzdGF0aWMgdG9TdHJpbmcoZGF0YSkge1xuICAgICAgICBjb25zdCBidWYgPSBCdWZmZXJTb3VyY2VDb252ZXJ0ZXIuaXNCdWZmZXJTb3VyY2UoZGF0YSlcbiAgICAgICAgICAgID8gQnVmZmVyU291cmNlQ29udmVydGVyLnRvQXJyYXlCdWZmZXIoZGF0YSlcbiAgICAgICAgICAgIDogQXNuQ29udmVydC5zZXJpYWxpemUoZGF0YSk7XG4gICAgICAgIGNvbnN0IGFzbiA9IGFzbjFqcy5mcm9tQkVSKGJ1Zik7XG4gICAgICAgIGlmIChhc24ub2Zmc2V0ID09PSAtMSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZGVjb2RlIEFTTi4xIGRhdGEuICR7YXNuLnJlc3VsdC5lcnJvcn1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gYXNuLnJlc3VsdC50b1N0cmluZygpO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/convert.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* binding */ AsnAnyConverter),\n/* harmony export */   AsnBitStringConverter: () => (/* binding */ AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* binding */ AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* binding */ AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* binding */ AsnCharacterStringConverter),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* binding */ AsnConstructedOctetStringConverter),\n/* harmony export */   AsnEnumeratedConverter: () => (/* binding */ AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* binding */ AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* binding */ AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* binding */ AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* binding */ AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* binding */ AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* binding */ AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* binding */ AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* binding */ AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* binding */ AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* binding */ AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* binding */ AsnOctetStringConverter),\n/* harmony export */   AsnPrintableStringConverter: () => (/* binding */ AsnPrintableStringConverter),\n/* harmony export */   AsnTeletexStringConverter: () => (/* binding */ AsnTeletexStringConverter),\n/* harmony export */   AsnUTCTimeConverter: () => (/* binding */ AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* binding */ AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* binding */ AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* binding */ AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* binding */ AsnVisibleStringConverter),\n/* harmony export */   defaultConverter: () => (/* binding */ defaultConverter)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types/index */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n\n\n\nconst AsnAnyConverter = {\n    fromASN: (value) => value instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null ? null : value.valueBeforeDecodeView,\n    toASN: (value) => {\n        if (value === null) {\n            return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n        }\n        const schema = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(value);\n        if (schema.result.error) {\n            throw new Error(schema.result.error);\n        }\n        return schema.result;\n    },\n};\nconst AsnIntegerConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView.byteLength >= 4\n        ? value.valueBlock.toString()\n        : value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ value: +value }),\n};\nconst AsnEnumeratedConverter = {\n    fromASN: (value) => value.valueBlock.valueDec,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Enumerated({ value }),\n};\nconst AsnIntegerArrayBufferConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer({ valueHex: value }),\n};\nconst AsnIntegerBigIntConverter = {\n    fromASN: (value) => value.toBigInt(),\n    toASN: (value) => asn1js__WEBPACK_IMPORTED_MODULE_0__.Integer.fromBigInt(value),\n};\nconst AsnBitStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ valueHex: value }),\n};\nconst AsnObjectIdentifierConverter = {\n    fromASN: (value) => value.valueBlock.toString(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.ObjectIdentifier({ value }),\n};\nconst AsnBooleanConverter = {\n    fromASN: (value) => value.valueBlock.value,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.Boolean({ value }),\n};\nconst AsnOctetStringConverter = {\n    fromASN: (value) => value.valueBlock.valueHexView,\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: value }),\n};\nconst AsnConstructedOctetStringConverter = {\n    fromASN: (value) => new _types_index__WEBPACK_IMPORTED_MODULE_2__.OctetString(value.getValue()),\n    toASN: (value) => value.toASN(),\n};\nfunction createStringConverter(Asn1Type) {\n    return {\n        fromASN: (value) => value.valueBlock.value,\n        toASN: (value) => new Asn1Type({ value }),\n    };\n}\nconst AsnUtf8StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.Utf8String);\nconst AsnBmpStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.BmpString);\nconst AsnUniversalStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.UniversalString);\nconst AsnNumericStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.NumericString);\nconst AsnPrintableStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.PrintableString);\nconst AsnTeletexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.TeletexString);\nconst AsnVideotexStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VideotexString);\nconst AsnIA5StringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.IA5String);\nconst AsnGraphicStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GraphicString);\nconst AsnVisibleStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.VisibleString);\nconst AsnGeneralStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralString);\nconst AsnCharacterStringConverter = createStringConverter(asn1js__WEBPACK_IMPORTED_MODULE_0__.CharacterString);\nconst AsnUTCTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.UTCTime({ valueDate: value }),\n};\nconst AsnGeneralizedTimeConverter = {\n    fromASN: (value) => value.toDate(),\n    toASN: (value) => new asn1js__WEBPACK_IMPORTED_MODULE_0__.GeneralizedTime({ valueDate: value }),\n};\nconst AsnNullConverter = {\n    fromASN: () => null,\n    toASN: () => {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Null();\n    },\n};\nfunction defaultConverter(type) {\n    switch (type) {\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Any:\n            return AsnAnyConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BitString:\n            return AsnBitStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.BmpString:\n            return AsnBmpStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Boolean:\n            return AsnBooleanConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.CharacterString:\n            return AsnCharacterStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Enumerated:\n            return AsnEnumeratedConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralString:\n            return AsnGeneralStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GeneralizedTime:\n            return AsnGeneralizedTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.GraphicString:\n            return AsnGraphicStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.IA5String:\n            return AsnIA5StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Integer:\n            return AsnIntegerConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Null:\n            return AsnNullConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.NumericString:\n            return AsnNumericStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.ObjectIdentifier:\n            return AsnObjectIdentifierConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.OctetString:\n            return AsnOctetStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.PrintableString:\n            return AsnPrintableStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.TeletexString:\n            return AsnTeletexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UTCTime:\n            return AsnUTCTimeConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.UniversalString:\n            return AsnUniversalStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.Utf8String:\n            return AsnUtf8StringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VideotexString:\n            return AsnVideotexStringConverter;\n        case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes.VisibleString:\n            return AsnVisibleStringConverter;\n        default:\n            return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/decorators.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/decorators.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnChoiceType: () => (/* binding */ AsnChoiceType),\n/* harmony export */   AsnProp: () => (/* binding */ AsnProp),\n/* harmony export */   AsnSequenceType: () => (/* binding */ AsnSequenceType),\n/* harmony export */   AsnSetType: () => (/* binding */ AsnSetType),\n/* harmony export */   AsnType: () => (/* binding */ AsnType)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./storage */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\nconst AsnType = (options) => (target) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target);\n    }\n    Object.assign(schema, options);\n};\nconst AsnChoiceType = () => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice });\nconst AsnSetType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set, ...options });\nconst AsnSequenceType = (options) => AsnType({ type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence, ...options });\nconst AsnProp = (options) => (target, propertyKey) => {\n    let schema;\n    if (!_storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.has(target.constructor)) {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.createDefault(target.constructor);\n        _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.set(target.constructor, schema);\n    }\n    else {\n        schema = _storage__WEBPACK_IMPORTED_MODULE_2__.schemaStorage.get(target.constructor);\n    }\n    const copyOptions = Object.assign({}, options);\n    if (typeof copyOptions.type === \"number\" && !copyOptions.converter) {\n        const defaultConverter = _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter(options.type);\n        if (!defaultConverter) {\n            throw new Error(`Cannot get default converter for property '${propertyKey}' of ${target.constructor.name}`);\n        }\n        copyOptions.converter = defaultConverter;\n    }\n    schema.items[propertyKey] = copyOptions;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnPropTypes: () => (/* binding */ AsnPropTypes),\n/* harmony export */   AsnTypeTypes: () => (/* binding */ AsnTypeTypes)\n/* harmony export */ });\nvar AsnTypeTypes;\n(function (AsnTypeTypes) {\n    AsnTypeTypes[AsnTypeTypes[\"Sequence\"] = 0] = \"Sequence\";\n    AsnTypeTypes[AsnTypeTypes[\"Set\"] = 1] = \"Set\";\n    AsnTypeTypes[AsnTypeTypes[\"Choice\"] = 2] = \"Choice\";\n})(AsnTypeTypes || (AsnTypeTypes = {}));\nvar AsnPropTypes;\n(function (AsnPropTypes) {\n    AsnPropTypes[AsnPropTypes[\"Any\"] = 1] = \"Any\";\n    AsnPropTypes[AsnPropTypes[\"Boolean\"] = 2] = \"Boolean\";\n    AsnPropTypes[AsnPropTypes[\"OctetString\"] = 3] = \"OctetString\";\n    AsnPropTypes[AsnPropTypes[\"BitString\"] = 4] = \"BitString\";\n    AsnPropTypes[AsnPropTypes[\"Integer\"] = 5] = \"Integer\";\n    AsnPropTypes[AsnPropTypes[\"Enumerated\"] = 6] = \"Enumerated\";\n    AsnPropTypes[AsnPropTypes[\"ObjectIdentifier\"] = 7] = \"ObjectIdentifier\";\n    AsnPropTypes[AsnPropTypes[\"Utf8String\"] = 8] = \"Utf8String\";\n    AsnPropTypes[AsnPropTypes[\"BmpString\"] = 9] = \"BmpString\";\n    AsnPropTypes[AsnPropTypes[\"UniversalString\"] = 10] = \"UniversalString\";\n    AsnPropTypes[AsnPropTypes[\"NumericString\"] = 11] = \"NumericString\";\n    AsnPropTypes[AsnPropTypes[\"PrintableString\"] = 12] = \"PrintableString\";\n    AsnPropTypes[AsnPropTypes[\"TeletexString\"] = 13] = \"TeletexString\";\n    AsnPropTypes[AsnPropTypes[\"VideotexString\"] = 14] = \"VideotexString\";\n    AsnPropTypes[AsnPropTypes[\"IA5String\"] = 15] = \"IA5String\";\n    AsnPropTypes[AsnPropTypes[\"GraphicString\"] = 16] = \"GraphicString\";\n    AsnPropTypes[AsnPropTypes[\"VisibleString\"] = 17] = \"VisibleString\";\n    AsnPropTypes[AsnPropTypes[\"GeneralString\"] = 18] = \"GeneralString\";\n    AsnPropTypes[AsnPropTypes[\"CharacterString\"] = 19] = \"CharacterString\";\n    AsnPropTypes[AsnPropTypes[\"UTCTime\"] = 20] = \"UTCTime\";\n    AsnPropTypes[AsnPropTypes[\"GeneralizedTime\"] = 21] = \"GeneralizedTime\";\n    AsnPropTypes[AsnPropTypes[\"DATE\"] = 22] = \"DATE\";\n    AsnPropTypes[AsnPropTypes[\"TimeOfDay\"] = 23] = \"TimeOfDay\";\n    AsnPropTypes[AsnPropTypes[\"DateTime\"] = 24] = \"DateTime\";\n    AsnPropTypes[AsnPropTypes[\"Duration\"] = 25] = \"Duration\";\n    AsnPropTypes[AsnPropTypes[\"TIME\"] = 26] = \"TIME\";\n    AsnPropTypes[AsnPropTypes[\"Null\"] = 27] = \"Null\";\n})(AsnPropTypes || (AsnPropTypes = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js":
/*!*****************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _schema_validation__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaValidationError)\n/* harmony export */ });\n/* harmony import */ var _schema_validation__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema_validation */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\");\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvZXJyb3JzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvZXJyb3JzL2luZGV4LmpzPzgwNmMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vc2NoZW1hX3ZhbGlkYXRpb25cIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js":
/*!*****************************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaValidationError: () => (/* binding */ AsnSchemaValidationError)\n/* harmony export */ });\nclass AsnSchemaValidationError extends Error {\n    constructor() {\n        super(...arguments);\n        this.schemas = [];\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvZXJyb3JzL3NjaGVtYV92YWxpZGF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8uLi8uLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9lcnJvcnMvc2NoZW1hX3ZhbGlkYXRpb24uanM/NmNlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgQXNuU2NoZW1hVmFsaWRhdGlvbkVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xuICAgICAgICB0aGlzLnNjaGVtYXMgPSBbXTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/schema_validation.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isArrayEqual: () => (/* binding */ isArrayEqual),\n/* harmony export */   isConvertible: () => (/* binding */ isConvertible),\n/* harmony export */   isTypeOfArray: () => (/* binding */ isTypeOfArray)\n/* harmony export */ });\nfunction isConvertible(target) {\n    if (typeof target === \"function\" && target.prototype) {\n        if (target.prototype.toASN && target.prototype.fromASN) {\n            return true;\n        }\n        else {\n            return isConvertible(target.prototype);\n        }\n    }\n    else {\n        return !!(target && typeof target === \"object\" && \"toASN\" in target && \"fromASN\" in target);\n    }\n}\nfunction isTypeOfArray(target) {\n    var _a;\n    if (target) {\n        const proto = Object.getPrototypeOf(target);\n        if (((_a = proto === null || proto === void 0 ? void 0 : proto.prototype) === null || _a === void 0 ? void 0 : _a.constructor) === Array) {\n            return true;\n        }\n        return isTypeOfArray(proto);\n    }\n    return false;\n}\nfunction isArrayEqual(bytes1, bytes2) {\n    if (!(bytes1 && bytes2)) {\n        return false;\n    }\n    if (bytes1.byteLength !== bytes2.byteLength) {\n        return false;\n    }\n    const b1 = new Uint8Array(bytes1);\n    const b2 = new Uint8Array(bytes2);\n    for (let i = 0; i < bytes1.byteLength; i++) {\n        if (b1[i] !== b2[i]) {\n            return false;\n        }\n    }\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvaGVscGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix1QkFBdUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvaGVscGVyLmpzP2YxOWYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGlzQ29udmVydGlibGUodGFyZ2V0KSB7XG4gICAgaWYgKHR5cGVvZiB0YXJnZXQgPT09IFwiZnVuY3Rpb25cIiAmJiB0YXJnZXQucHJvdG90eXBlKSB7XG4gICAgICAgIGlmICh0YXJnZXQucHJvdG90eXBlLnRvQVNOICYmIHRhcmdldC5wcm90b3R5cGUuZnJvbUFTTikge1xuICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICByZXR1cm4gaXNDb252ZXJ0aWJsZSh0YXJnZXQucHJvdG90eXBlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuICEhKHRhcmdldCAmJiB0eXBlb2YgdGFyZ2V0ID09PSBcIm9iamVjdFwiICYmIFwidG9BU05cIiBpbiB0YXJnZXQgJiYgXCJmcm9tQVNOXCIgaW4gdGFyZ2V0KTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gaXNUeXBlT2ZBcnJheSh0YXJnZXQpIHtcbiAgICB2YXIgX2E7XG4gICAgaWYgKHRhcmdldCkge1xuICAgICAgICBjb25zdCBwcm90byA9IE9iamVjdC5nZXRQcm90b3R5cGVPZih0YXJnZXQpO1xuICAgICAgICBpZiAoKChfYSA9IHByb3RvID09PSBudWxsIHx8IHByb3RvID09PSB2b2lkIDAgPyB2b2lkIDAgOiBwcm90by5wcm90b3R5cGUpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jb25zdHJ1Y3RvcikgPT09IEFycmF5KSB7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gaXNUeXBlT2ZBcnJheShwcm90byk7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBpc0FycmF5RXF1YWwoYnl0ZXMxLCBieXRlczIpIHtcbiAgICBpZiAoIShieXRlczEgJiYgYnl0ZXMyKSkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGlmIChieXRlczEuYnl0ZUxlbmd0aCAhPT0gYnl0ZXMyLmJ5dGVMZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBjb25zdCBiMSA9IG5ldyBVaW50OEFycmF5KGJ5dGVzMSk7XG4gICAgY29uc3QgYjIgPSBuZXcgVWludDhBcnJheShieXRlczIpO1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgYnl0ZXMxLmJ5dGVMZW5ndGg7IGkrKykge1xuICAgICAgICBpZiAoYjFbaV0gIT09IGIyW2ldKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/index.js":
/*!**********************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnAnyConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnAnyConverter),\n/* harmony export */   AsnArray: () => (/* reexport safe */ _objects__WEBPACK_IMPORTED_MODULE_7__.AsnArray),\n/* harmony export */   AsnBitStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBitStringConverter),\n/* harmony export */   AsnBmpStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBmpStringConverter),\n/* harmony export */   AsnBooleanConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnBooleanConverter),\n/* harmony export */   AsnCharacterStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnCharacterStringConverter),\n/* harmony export */   AsnChoiceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnChoiceType),\n/* harmony export */   AsnConstructedOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnConstructedOctetStringConverter),\n/* harmony export */   AsnConvert: () => (/* reexport safe */ _convert__WEBPACK_IMPORTED_MODULE_8__.AsnConvert),\n/* harmony export */   AsnEnumeratedConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnEnumeratedConverter),\n/* harmony export */   AsnGeneralStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralStringConverter),\n/* harmony export */   AsnGeneralizedTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGeneralizedTimeConverter),\n/* harmony export */   AsnGraphicStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnGraphicStringConverter),\n/* harmony export */   AsnIA5StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIA5StringConverter),\n/* harmony export */   AsnIntegerArrayBufferConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerArrayBufferConverter),\n/* harmony export */   AsnIntegerBigIntConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerBigIntConverter),\n/* harmony export */   AsnIntegerConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnIntegerConverter),\n/* harmony export */   AsnNullConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNullConverter),\n/* harmony export */   AsnNumericStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnNumericStringConverter),\n/* harmony export */   AsnObjectIdentifierConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnObjectIdentifierConverter),\n/* harmony export */   AsnOctetStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnOctetStringConverter),\n/* harmony export */   AsnParser: () => (/* reexport safe */ _parser__WEBPACK_IMPORTED_MODULE_4__.AsnParser),\n/* harmony export */   AsnPrintableStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnPrintableStringConverter),\n/* harmony export */   AsnProp: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnProp),\n/* harmony export */   AsnPropTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnPropTypes),\n/* harmony export */   AsnSchemaValidationError: () => (/* reexport safe */ _errors__WEBPACK_IMPORTED_MODULE_6__.AsnSchemaValidationError),\n/* harmony export */   AsnSequenceType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSequenceType),\n/* harmony export */   AsnSerializer: () => (/* reexport safe */ _serializer__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer),\n/* harmony export */   AsnSetType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnSetType),\n/* harmony export */   AsnTeletexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnTeletexStringConverter),\n/* harmony export */   AsnType: () => (/* reexport safe */ _decorators__WEBPACK_IMPORTED_MODULE_2__.AsnType),\n/* harmony export */   AsnTypeTypes: () => (/* reexport safe */ _enums__WEBPACK_IMPORTED_MODULE_3__.AsnTypeTypes),\n/* harmony export */   AsnUTCTimeConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUTCTimeConverter),\n/* harmony export */   AsnUniversalStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUniversalStringConverter),\n/* harmony export */   AsnUtf8StringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnUtf8StringConverter),\n/* harmony export */   AsnVideotexStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVideotexStringConverter),\n/* harmony export */   AsnVisibleStringConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.AsnVisibleStringConverter),\n/* harmony export */   BitString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _types_index__WEBPACK_IMPORTED_MODULE_1__.OctetString),\n/* harmony export */   defaultConverter: () => (/* reexport safe */ _converters__WEBPACK_IMPORTED_MODULE_0__.defaultConverter)\n/* harmony export */ });\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./converters */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _types_index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/index */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\");\n/* harmony import */ var _decorators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./decorators */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/decorators.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./parser */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/parser.js\");\n/* harmony import */ var _serializer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./serializer */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./errors */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _objects__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./objects */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/objects.js\");\n/* harmony import */ var _convert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./convert */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/convert.js\");\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE2QjtBQUNDO0FBQzhEO0FBQ3ZDO0FBQ2hCO0FBQ1E7QUFDcEI7QUFDQztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvaW5kZXguanM/ZmEyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9jb252ZXJ0ZXJzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90eXBlcy9pbmRleFwiO1xuZXhwb3J0IHsgQXNuUHJvcCwgQXNuVHlwZSwgQXNuQ2hvaWNlVHlwZSwgQXNuU2VxdWVuY2VUeXBlLCBBc25TZXRUeXBlIH0gZnJvbSBcIi4vZGVjb3JhdG9yc1wiO1xuZXhwb3J0IHsgQXNuVHlwZVR5cGVzLCBBc25Qcm9wVHlwZXMgfSBmcm9tIFwiLi9lbnVtc1wiO1xuZXhwb3J0IHsgQXNuUGFyc2VyIH0gZnJvbSBcIi4vcGFyc2VyXCI7XG5leHBvcnQgeyBBc25TZXJpYWxpemVyIH0gZnJvbSBcIi4vc2VyaWFsaXplclwiO1xuZXhwb3J0ICogZnJvbSBcIi4vZXJyb3JzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9vYmplY3RzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9jb252ZXJ0XCI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/objects.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/objects.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnArray: () => (/* binding */ AsnArray)\n/* harmony export */ });\nclass AsnArray extends Array {\n    constructor(items = []) {\n        if (typeof items === \"number\") {\n            super(items);\n        }\n        else {\n            super();\n            for (const item of items) {\n                this.push(item);\n            }\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvb2JqZWN0cy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8uLi8uLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9vYmplY3RzLmpzPzc5ZGUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNsYXNzIEFzbkFycmF5IGV4dGVuZHMgQXJyYXkge1xuICAgIGNvbnN0cnVjdG9yKGl0ZW1zID0gW10pIHtcbiAgICAgICAgaWYgKHR5cGVvZiBpdGVtcyA9PT0gXCJudW1iZXJcIikge1xuICAgICAgICAgICAgc3VwZXIoaXRlbXMpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgc3VwZXIoKTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgaXRlbSBvZiBpdGVtcykge1xuICAgICAgICAgICAgICAgIHRoaXMucHVzaChpdGVtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/objects.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/parser.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/parser.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnParser: () => (/* binding */ AsnParser)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./converters */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _errors__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./errors */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/errors/index.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./helper */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./storage */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\n\nclass AsnParser {\n    static parse(data, target) {\n        const asn1Parsed = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(data);\n        if (asn1Parsed.result.error) {\n            throw new Error(asn1Parsed.result.error);\n        }\n        const res = this.fromASN(asn1Parsed.result, target);\n        return res;\n    }\n    static fromASN(asn1Schema, target) {\n        try {\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(target)) {\n                const value = new target();\n                return value.fromASN(asn1Schema);\n            }\n            const schema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(target);\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.cache(target);\n            let targetSchema = schema.schema;\n            const choiceResult = this.handleChoiceTypes(asn1Schema, schema, target, targetSchema);\n            if (choiceResult === null || choiceResult === void 0 ? void 0 : choiceResult.result) {\n                return choiceResult.result;\n            }\n            if (choiceResult === null || choiceResult === void 0 ? void 0 : choiceResult.targetSchema) {\n                targetSchema = choiceResult.targetSchema;\n            }\n            const sequenceResult = this.handleSequenceTypes(asn1Schema, schema, target, targetSchema);\n            if (sequenceResult && \"isManualMapping\" in sequenceResult) {\n                return sequenceResult.result;\n            }\n            const asn1ComparedSchema = sequenceResult;\n            const res = new target();\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isTypeOfArray)(target)) {\n                return this.handleArrayTypes(asn1Schema, schema, target);\n            }\n            this.processSchemaItems(schema, asn1ComparedSchema, res);\n            return res;\n        }\n        catch (error) {\n            if (error instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError) {\n                error.schemas.push(target.name);\n            }\n            throw error;\n        }\n    }\n    static handleChoiceTypes(asn1Schema, schema, target, targetSchema) {\n        if (asn1Schema.constructor === asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed &&\n            schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice &&\n            asn1Schema.idBlock.tagClass === 3) {\n            for (const key in schema.items) {\n                const schemaItem = schema.items[key];\n                if (schemaItem.context === asn1Schema.idBlock.tagNumber && schemaItem.implicit) {\n                    if (typeof schemaItem.type === \"function\" &&\n                        _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(schemaItem.type)) {\n                        const fieldSchema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItem.type);\n                        if (fieldSchema && fieldSchema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n                            const newSeq = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence();\n                            if (\"value\" in asn1Schema.valueBlock &&\n                                Array.isArray(asn1Schema.valueBlock.value) &&\n                                \"value\" in newSeq.valueBlock) {\n                                newSeq.valueBlock.value = asn1Schema.valueBlock.value;\n                                const fieldValue = this.fromASN(newSeq, schemaItem.type);\n                                const res = new target();\n                                res[key] = fieldValue;\n                                return { result: res };\n                            }\n                        }\n                    }\n                }\n            }\n        }\n        else if (asn1Schema.constructor === asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed &&\n            schema.type !== _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n            const newTargetSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                idBlock: {\n                    tagClass: 3,\n                    tagNumber: asn1Schema.idBlock.tagNumber,\n                },\n                value: schema.schema.valueBlock.value,\n            });\n            for (const key in schema.items) {\n                delete asn1Schema[key];\n            }\n            return { targetSchema: newTargetSchema };\n        }\n        return null;\n    }\n    static handleSequenceTypes(asn1Schema, schema, target, targetSchema) {\n        if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n            const optionalChoiceFields = Object.keys(schema.items).filter((key) => {\n                const item = schema.items[key];\n                return (item.optional &&\n                    typeof item.type === \"function\" &&\n                    _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(item.type) &&\n                    _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(item.type).type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice);\n            });\n            if (optionalChoiceFields.length > 0 &&\n                \"value\" in asn1Schema.valueBlock &&\n                Array.isArray(asn1Schema.valueBlock.value) &&\n                target.name === \"CertReqMsg\") {\n                return this.handleManualMapping(asn1Schema, schema, target);\n            }\n            const asn1ComparedSchema = asn1js__WEBPACK_IMPORTED_MODULE_0__.compareSchema({}, asn1Schema, targetSchema);\n            if (!asn1ComparedSchema.verified) {\n                throw new _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError(`Data does not match to ${target.name} ASN1 schema. ${asn1ComparedSchema.result.error}`);\n            }\n            return asn1ComparedSchema;\n        }\n        else {\n            const asn1ComparedSchema = asn1js__WEBPACK_IMPORTED_MODULE_0__.compareSchema({}, asn1Schema, targetSchema);\n            if (!asn1ComparedSchema.verified) {\n                throw new _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError(`Data does not match to ${target.name} ASN1 schema. ${asn1ComparedSchema.result.error}`);\n            }\n            return asn1ComparedSchema;\n        }\n    }\n    static handleManualMapping(asn1Schema, schema, target) {\n        const res = new target();\n        const asn1Elements = asn1Schema.valueBlock.value;\n        const schemaKeys = Object.keys(schema.items);\n        let asn1Index = 0;\n        for (let i = 0; i < schemaKeys.length; i++) {\n            const key = schemaKeys[i];\n            const schemaItem = schema.items[key];\n            if (asn1Index >= asn1Elements.length)\n                break;\n            if (schemaItem.repeated) {\n                res[key] = this.processRepeatedField(asn1Elements, asn1Index, schemaItem);\n                break;\n            }\n            else if (typeof schemaItem.type === \"number\") {\n                res[key] = this.processPrimitiveField(asn1Elements[asn1Index], schemaItem);\n                asn1Index++;\n            }\n            else if (this.isOptionalChoiceField(schemaItem)) {\n                const result = this.processOptionalChoiceField(asn1Elements[asn1Index], schemaItem);\n                if (result.processed) {\n                    res[key] = result.value;\n                    asn1Index++;\n                }\n            }\n            else {\n                res[key] = this.fromASN(asn1Elements[asn1Index], schemaItem.type);\n                asn1Index++;\n            }\n        }\n        return { result: res, verified: true, isManualMapping: true };\n    }\n    static processRepeatedField(asn1Elements, asn1Index, schemaItem) {\n        let elementsToProcess = asn1Elements.slice(asn1Index);\n        if (elementsToProcess.length === 1 && elementsToProcess[0].constructor.name === \"Sequence\") {\n            const seq = elementsToProcess[0];\n            if (seq.valueBlock && seq.valueBlock.value && Array.isArray(seq.valueBlock.value)) {\n                elementsToProcess = seq.valueBlock.value;\n            }\n        }\n        if (typeof schemaItem.type === \"number\") {\n            const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(schemaItem.type);\n            if (!converter)\n                throw new Error(`No converter for ASN.1 type ${schemaItem.type}`);\n            return elementsToProcess\n                .filter((el) => el && el.valueBlock)\n                .map((el) => {\n                try {\n                    return converter.fromASN(el);\n                }\n                catch {\n                    return undefined;\n                }\n            })\n                .filter((v) => v !== undefined);\n        }\n        else {\n            return elementsToProcess\n                .filter((el) => el && el.valueBlock)\n                .map((el) => {\n                try {\n                    return this.fromASN(el, schemaItem.type);\n                }\n                catch {\n                    return undefined;\n                }\n            })\n                .filter((v) => v !== undefined);\n        }\n    }\n    static processPrimitiveField(asn1Element, schemaItem) {\n        const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(schemaItem.type);\n        if (!converter)\n            throw new Error(`No converter for ASN.1 type ${schemaItem.type}`);\n        return converter.fromASN(asn1Element);\n    }\n    static isOptionalChoiceField(schemaItem) {\n        return (schemaItem.optional &&\n            typeof schemaItem.type === \"function\" &&\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.has(schemaItem.type) &&\n            _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItem.type).type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice);\n    }\n    static processOptionalChoiceField(asn1Element, schemaItem) {\n        try {\n            const value = this.fromASN(asn1Element, schemaItem.type);\n            return { processed: true, value };\n        }\n        catch (err) {\n            if (err instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError &&\n                /Wrong values for Choice type/.test(err.message)) {\n                return { processed: false };\n            }\n            throw err;\n        }\n    }\n    static handleArrayTypes(asn1Schema, schema, target) {\n        if (!(\"value\" in asn1Schema.valueBlock && Array.isArray(asn1Schema.valueBlock.value))) {\n            throw new Error(`Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.`);\n        }\n        const itemType = schema.itemType;\n        if (typeof itemType === \"number\") {\n            const converter = _converters__WEBPACK_IMPORTED_MODULE_2__.defaultConverter(itemType);\n            if (!converter) {\n                throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n            }\n            return target.from(asn1Schema.valueBlock.value, (element) => converter.fromASN(element));\n        }\n        else {\n            return target.from(asn1Schema.valueBlock.value, (element) => this.fromASN(element, itemType));\n        }\n    }\n    static processSchemaItems(schema, asn1ComparedSchema, res) {\n        for (const key in schema.items) {\n            const asn1SchemaValue = asn1ComparedSchema.result[key];\n            if (!asn1SchemaValue) {\n                continue;\n            }\n            const schemaItem = schema.items[key];\n            const schemaItemType = schemaItem.type;\n            if (typeof schemaItemType === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                res[key] = this.processPrimitiveSchemaItem(asn1SchemaValue, schemaItem, schemaItemType);\n            }\n            else {\n                res[key] = this.processComplexSchemaItem(asn1SchemaValue, schemaItem, schemaItemType);\n            }\n        }\n    }\n    static processPrimitiveSchemaItem(asn1SchemaValue, schemaItem, schemaItemType) {\n        var _a;\n        const converter = (_a = schemaItem.converter) !== null && _a !== void 0 ? _a : ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)\n            ? new schemaItemType()\n            : null);\n        if (!converter) {\n            throw new Error(\"Converter is empty\");\n        }\n        if (schemaItem.repeated) {\n            return this.processRepeatedPrimitiveItem(asn1SchemaValue, schemaItem, converter);\n        }\n        else {\n            return this.processSinglePrimitiveItem(asn1SchemaValue, schemaItem, schemaItemType, converter);\n        }\n    }\n    static processRepeatedPrimitiveItem(asn1SchemaValue, schemaItem, converter) {\n        if (schemaItem.implicit) {\n            const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n            const newItem = new Container();\n            newItem.valueBlock = asn1SchemaValue.valueBlock;\n            const newItemAsn = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false));\n            if (newItemAsn.offset === -1) {\n                throw new Error(`Cannot parse the child item. ${newItemAsn.result.error}`);\n            }\n            if (!(\"value\" in newItemAsn.result.valueBlock &&\n                Array.isArray(newItemAsn.result.valueBlock.value))) {\n                throw new Error(\"Cannot get items from the ASN.1 parsed value. ASN.1 object is not constructed.\");\n            }\n            const value = newItemAsn.result.valueBlock.value;\n            return Array.from(value, (element) => converter.fromASN(element));\n        }\n        else {\n            return Array.from(asn1SchemaValue, (element) => converter.fromASN(element));\n        }\n    }\n    static processSinglePrimitiveItem(asn1SchemaValue, schemaItem, schemaItemType, converter) {\n        let value = asn1SchemaValue;\n        if (schemaItem.implicit) {\n            let newItem;\n            if ((0,_helper__WEBPACK_IMPORTED_MODULE_4__.isConvertible)(schemaItemType)) {\n                newItem = new schemaItemType().toSchema(\"\");\n            }\n            else {\n                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[schemaItemType];\n                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                if (!Asn1Type) {\n                    throw new Error(`Cannot get '${Asn1TypeName}' class from asn1js module`);\n                }\n                newItem = new Asn1Type();\n            }\n            newItem.valueBlock = value.valueBlock;\n            value = asn1js__WEBPACK_IMPORTED_MODULE_0__.fromBER(newItem.toBER(false)).result;\n        }\n        return converter.fromASN(value);\n    }\n    static processComplexSchemaItem(asn1SchemaValue, schemaItem, schemaItemType) {\n        if (schemaItem.repeated) {\n            if (!Array.isArray(asn1SchemaValue)) {\n                throw new Error(\"Cannot get list of items from the ASN.1 parsed value. ASN.1 value should be iterable.\");\n            }\n            return Array.from(asn1SchemaValue, (element) => this.fromASN(element, schemaItemType));\n        }\n        else {\n            const valueToProcess = this.handleImplicitTagging(asn1SchemaValue, schemaItem, schemaItemType);\n            if (this.isOptionalChoiceField(schemaItem)) {\n                try {\n                    return this.fromASN(valueToProcess, schemaItemType);\n                }\n                catch (err) {\n                    if (err instanceof _errors__WEBPACK_IMPORTED_MODULE_3__.AsnSchemaValidationError &&\n                        /Wrong values for Choice type/.test(err.message)) {\n                        return undefined;\n                    }\n                    throw err;\n                }\n            }\n            else {\n                return this.fromASN(valueToProcess, schemaItemType);\n            }\n        }\n    }\n    static handleImplicitTagging(asn1SchemaValue, schemaItem, schemaItemType) {\n        if (schemaItem.implicit && typeof schemaItem.context === \"number\") {\n            const schema = _storage__WEBPACK_IMPORTED_MODULE_5__.schemaStorage.get(schemaItemType);\n            if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence) {\n                const newSeq = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence();\n                if (\"value\" in asn1SchemaValue.valueBlock &&\n                    Array.isArray(asn1SchemaValue.valueBlock.value) &&\n                    \"value\" in newSeq.valueBlock) {\n                    newSeq.valueBlock.value = asn1SchemaValue.valueBlock.value;\n                    return newSeq;\n                }\n            }\n            else if (schema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set) {\n                const newSet = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set();\n                if (\"value\" in asn1SchemaValue.valueBlock &&\n                    Array.isArray(asn1SchemaValue.valueBlock.value) &&\n                    \"value\" in newSet.valueBlock) {\n                    newSet.valueBlock.value = asn1SchemaValue.valueBlock.value;\n                    return newSet;\n                }\n            }\n        }\n        return asn1SchemaValue;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/parser.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/schema.js":
/*!***********************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/schema.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSchemaStorage: () => (/* binding */ AsnSchemaStorage)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helper */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n\n\n\nclass AsnSchemaStorage {\n    constructor() {\n        this.items = new WeakMap();\n    }\n    has(target) {\n        return this.items.has(target);\n    }\n    get(target, checkSchema = false) {\n        const schema = this.items.get(target);\n        if (!schema) {\n            throw new Error(`Cannot get schema for '${target.prototype.constructor.name}' target`);\n        }\n        if (checkSchema && !schema.schema) {\n            throw new Error(`Schema '${target.prototype.constructor.name}' doesn't contain ASN.1 schema. Call 'AsnSchemaStorage.cache'.`);\n        }\n        return schema;\n    }\n    cache(target) {\n        const schema = this.get(target);\n        if (!schema.schema) {\n            schema.schema = this.create(target, true);\n        }\n    }\n    createDefault(target) {\n        const schema = { type: _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence, items: {} };\n        const parentSchema = this.findParentSchema(target);\n        if (parentSchema) {\n            Object.assign(schema, parentSchema);\n            schema.items = Object.assign({}, schema.items, parentSchema.items);\n        }\n        return schema;\n    }\n    create(target, useNames) {\n        const schema = this.items.get(target) || this.createDefault(target);\n        const asn1Value = [];\n        for (const key in schema.items) {\n            const item = schema.items[key];\n            const name = useNames ? key : \"\";\n            let asn1Item;\n            if (typeof item.type === \"number\") {\n                const Asn1TypeName = _enums__WEBPACK_IMPORTED_MODULE_1__.AsnPropTypes[item.type];\n                const Asn1Type = asn1js__WEBPACK_IMPORTED_MODULE_0__[Asn1TypeName];\n                if (!Asn1Type) {\n                    throw new Error(`Cannot get ASN1 class by name '${Asn1TypeName}'`);\n                }\n                asn1Item = new Asn1Type({ name });\n            }\n            else if ((0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                const instance = new item.type();\n                asn1Item = instance.toSchema(name);\n            }\n            else if (item.optional) {\n                const itemSchema = this.get(item.type);\n                if (itemSchema.type === _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice) {\n                    asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n                }\n                else {\n                    asn1Item = this.create(item.type, false);\n                    asn1Item.name = name;\n                }\n            }\n            else {\n                asn1Item = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Any({ name });\n            }\n            const optional = !!item.optional || item.defaultValue !== undefined;\n            if (item.repeated) {\n                asn1Item.name = \"\";\n                const Container = item.repeated === \"set\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Set : asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence;\n                asn1Item = new Container({\n                    name: \"\",\n                    value: [new asn1js__WEBPACK_IMPORTED_MODULE_0__.Repeated({ name, value: asn1Item })],\n                });\n            }\n            if (item.context !== null && item.context !== undefined) {\n                if (item.implicit) {\n                    if (typeof item.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_2__.isConvertible)(item.type)) {\n                        const Container = item.repeated ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed : asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive;\n                        asn1Value.push(new Container({ name, optional, idBlock: { tagClass: 3, tagNumber: item.context } }));\n                    }\n                    else {\n                        this.cache(item.type);\n                        const isRepeated = !!item.repeated;\n                        let value = !isRepeated ? this.get(item.type, true).schema : asn1Item;\n                        value =\n                            \"valueBlock\" in value\n                                ? value.valueBlock.value\n                                :\n                                    value.value;\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            name: !isRepeated ? name : \"\",\n                            optional,\n                            idBlock: { tagClass: 3, tagNumber: item.context },\n                            value: value,\n                        }));\n                    }\n                }\n                else {\n                    asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                        optional,\n                        idBlock: { tagClass: 3, tagNumber: item.context },\n                        value: [asn1Item],\n                    }));\n                }\n            }\n            else {\n                asn1Item.optional = optional;\n                asn1Value.push(asn1Item);\n            }\n        }\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Sequence:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Set:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value, name: \"\" });\n            case _enums__WEBPACK_IMPORTED_MODULE_1__.AsnTypeTypes.Choice:\n                return new asn1js__WEBPACK_IMPORTED_MODULE_0__.Choice({ value: asn1Value, name: \"\" });\n            default:\n                throw new Error(`Unsupported ASN1 type in use`);\n        }\n    }\n    set(target, schema) {\n        this.items.set(target, schema);\n        return this;\n    }\n    findParentSchema(target) {\n        const parent = Object.getPrototypeOf(target);\n        if (parent) {\n            const schema = this.items.get(parent);\n            return schema || this.findParentSchema(parent);\n        }\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDb0I7QUFDWjtBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCxrQ0FBa0M7QUFDeEY7QUFDQTtBQUNBLHVDQUF1QyxrQ0FBa0M7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsTUFBTSxnREFBWTtBQUMzQztBQUNBO0FBQ0E7QUFDQSwyQ0FBMkM7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFDQUFxQyxnREFBWTtBQUNqRCxpQ0FBaUMsbUNBQU07QUFDdkM7QUFDQSxzRUFBc0UsYUFBYTtBQUNuRjtBQUNBLDBDQUEwQyxNQUFNO0FBQ2hEO0FBQ0EscUJBQXFCLHNEQUFhO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsZ0RBQVk7QUFDcEQsbUNBQW1DLHVDQUFVLEdBQUcsTUFBTTtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix1Q0FBVSxHQUFHLE1BQU07QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsdUNBQVUsR0FBRyw0Q0FBZTtBQUN4RjtBQUNBO0FBQ0EsZ0NBQWdDLDRDQUFlLEdBQUcsdUJBQXVCO0FBQ3pFLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsc0RBQWE7QUFDdEUsMERBQTBELCtDQUFrQixHQUFHLDZDQUFnQjtBQUMvRix1REFBdUQsMkJBQTJCLHdDQUF3QztBQUMxSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQywrQ0FBa0I7QUFDN0Q7QUFDQTtBQUNBLHVDQUF1QyxzQ0FBc0M7QUFDN0U7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLCtDQUFrQjtBQUN6RDtBQUNBLG1DQUFtQyxzQ0FBc0M7QUFDekU7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixnREFBWTtBQUM3QiwyQkFBMkIsNENBQWUsR0FBRyw0QkFBNEI7QUFDekUsaUJBQWlCLGdEQUFZO0FBQzdCLDJCQUEyQix1Q0FBVSxHQUFHLDRCQUE0QjtBQUNwRSxpQkFBaUIsZ0RBQVk7QUFDN0IsMkJBQTJCLDBDQUFhLEdBQUcsNEJBQTRCO0FBQ3ZFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8uLi8uLi9ub2RlX21vZHVsZXMvQHBlY3VsaWFyL2FzbjEtc2NoZW1hL2J1aWxkL2VzMjAxNS9zY2hlbWEuanM/M2FlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBhc24xanMgZnJvbSBcImFzbjFqc1wiO1xuaW1wb3J0IHsgQXNuUHJvcFR5cGVzLCBBc25UeXBlVHlwZXMgfSBmcm9tIFwiLi9lbnVtc1wiO1xuaW1wb3J0IHsgaXNDb252ZXJ0aWJsZSB9IGZyb20gXCIuL2hlbHBlclwiO1xuZXhwb3J0IGNsYXNzIEFzblNjaGVtYVN0b3JhZ2Uge1xuICAgIGNvbnN0cnVjdG9yKCkge1xuICAgICAgICB0aGlzLml0ZW1zID0gbmV3IFdlYWtNYXAoKTtcbiAgICB9XG4gICAgaGFzKHRhcmdldCkge1xuICAgICAgICByZXR1cm4gdGhpcy5pdGVtcy5oYXModGFyZ2V0KTtcbiAgICB9XG4gICAgZ2V0KHRhcmdldCwgY2hlY2tTY2hlbWEgPSBmYWxzZSkge1xuICAgICAgICBjb25zdCBzY2hlbWEgPSB0aGlzLml0ZW1zLmdldCh0YXJnZXQpO1xuICAgICAgICBpZiAoIXNjaGVtYSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZ2V0IHNjaGVtYSBmb3IgJyR7dGFyZ2V0LnByb3RvdHlwZS5jb25zdHJ1Y3Rvci5uYW1lfScgdGFyZ2V0YCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGNoZWNrU2NoZW1hICYmICFzY2hlbWEuc2NoZW1hKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFNjaGVtYSAnJHt0YXJnZXQucHJvdG90eXBlLmNvbnN0cnVjdG9yLm5hbWV9JyBkb2Vzbid0IGNvbnRhaW4gQVNOLjEgc2NoZW1hLiBDYWxsICdBc25TY2hlbWFTdG9yYWdlLmNhY2hlJy5gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2NoZW1hO1xuICAgIH1cbiAgICBjYWNoZSh0YXJnZXQpIHtcbiAgICAgICAgY29uc3Qgc2NoZW1hID0gdGhpcy5nZXQodGFyZ2V0KTtcbiAgICAgICAgaWYgKCFzY2hlbWEuc2NoZW1hKSB7XG4gICAgICAgICAgICBzY2hlbWEuc2NoZW1hID0gdGhpcy5jcmVhdGUodGFyZ2V0LCB0cnVlKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBjcmVhdGVEZWZhdWx0KHRhcmdldCkge1xuICAgICAgICBjb25zdCBzY2hlbWEgPSB7IHR5cGU6IEFzblR5cGVUeXBlcy5TZXF1ZW5jZSwgaXRlbXM6IHt9IH07XG4gICAgICAgIGNvbnN0IHBhcmVudFNjaGVtYSA9IHRoaXMuZmluZFBhcmVudFNjaGVtYSh0YXJnZXQpO1xuICAgICAgICBpZiAocGFyZW50U2NoZW1hKSB7XG4gICAgICAgICAgICBPYmplY3QuYXNzaWduKHNjaGVtYSwgcGFyZW50U2NoZW1hKTtcbiAgICAgICAgICAgIHNjaGVtYS5pdGVtcyA9IE9iamVjdC5hc3NpZ24oe30sIHNjaGVtYS5pdGVtcywgcGFyZW50U2NoZW1hLml0ZW1zKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2NoZW1hO1xuICAgIH1cbiAgICBjcmVhdGUodGFyZ2V0LCB1c2VOYW1lcykge1xuICAgICAgICBjb25zdCBzY2hlbWEgPSB0aGlzLml0ZW1zLmdldCh0YXJnZXQpIHx8IHRoaXMuY3JlYXRlRGVmYXVsdCh0YXJnZXQpO1xuICAgICAgICBjb25zdCBhc24xVmFsdWUgPSBbXTtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gc2NoZW1hLml0ZW1zKSB7XG4gICAgICAgICAgICBjb25zdCBpdGVtID0gc2NoZW1hLml0ZW1zW2tleV07XG4gICAgICAgICAgICBjb25zdCBuYW1lID0gdXNlTmFtZXMgPyBrZXkgOiBcIlwiO1xuICAgICAgICAgICAgbGV0IGFzbjFJdGVtO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtLnR5cGUgPT09IFwibnVtYmVyXCIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBBc24xVHlwZU5hbWUgPSBBc25Qcm9wVHlwZXNbaXRlbS50eXBlXTtcbiAgICAgICAgICAgICAgICBjb25zdCBBc24xVHlwZSA9IGFzbjFqc1tBc24xVHlwZU5hbWVdO1xuICAgICAgICAgICAgICAgIGlmICghQXNuMVR5cGUpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBDYW5ub3QgZ2V0IEFTTjEgY2xhc3MgYnkgbmFtZSAnJHtBc24xVHlwZU5hbWV9J2ApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBhc24xSXRlbSA9IG5ldyBBc24xVHlwZSh7IG5hbWUgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChpc0NvbnZlcnRpYmxlKGl0ZW0udHlwZSkpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBpbnN0YW5jZSA9IG5ldyBpdGVtLnR5cGUoKTtcbiAgICAgICAgICAgICAgICBhc24xSXRlbSA9IGluc3RhbmNlLnRvU2NoZW1hKG5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAoaXRlbS5vcHRpb25hbCkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGl0ZW1TY2hlbWEgPSB0aGlzLmdldChpdGVtLnR5cGUpO1xuICAgICAgICAgICAgICAgIGlmIChpdGVtU2NoZW1hLnR5cGUgPT09IEFzblR5cGVUeXBlcy5DaG9pY2UpIHtcbiAgICAgICAgICAgICAgICAgICAgYXNuMUl0ZW0gPSBuZXcgYXNuMWpzLkFueSh7IG5hbWUgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBhc24xSXRlbSA9IHRoaXMuY3JlYXRlKGl0ZW0udHlwZSwgZmFsc2UpO1xuICAgICAgICAgICAgICAgICAgICBhc24xSXRlbS5uYW1lID0gbmFtZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBhc24xSXRlbSA9IG5ldyBhc24xanMuQW55KHsgbmFtZSB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IG9wdGlvbmFsID0gISFpdGVtLm9wdGlvbmFsIHx8IGl0ZW0uZGVmYXVsdFZhbHVlICE9PSB1bmRlZmluZWQ7XG4gICAgICAgICAgICBpZiAoaXRlbS5yZXBlYXRlZCkge1xuICAgICAgICAgICAgICAgIGFzbjFJdGVtLm5hbWUgPSBcIlwiO1xuICAgICAgICAgICAgICAgIGNvbnN0IENvbnRhaW5lciA9IGl0ZW0ucmVwZWF0ZWQgPT09IFwic2V0XCIgPyBhc24xanMuU2V0IDogYXNuMWpzLlNlcXVlbmNlO1xuICAgICAgICAgICAgICAgIGFzbjFJdGVtID0gbmV3IENvbnRhaW5lcih7XG4gICAgICAgICAgICAgICAgICAgIG5hbWU6IFwiXCIsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBbbmV3IGFzbjFqcy5SZXBlYXRlZCh7IG5hbWUsIHZhbHVlOiBhc24xSXRlbSB9KV0sXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXRlbS5jb250ZXh0ICE9PSBudWxsICYmIGl0ZW0uY29udGV4dCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgaWYgKGl0ZW0uaW1wbGljaXQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBpdGVtLnR5cGUgPT09IFwibnVtYmVyXCIgfHwgaXNDb252ZXJ0aWJsZShpdGVtLnR5cGUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBDb250YWluZXIgPSBpdGVtLnJlcGVhdGVkID8gYXNuMWpzLkNvbnN0cnVjdGVkIDogYXNuMWpzLlByaW1pdGl2ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzbjFWYWx1ZS5wdXNoKG5ldyBDb250YWluZXIoeyBuYW1lLCBvcHRpb25hbCwgaWRCbG9jazogeyB0YWdDbGFzczogMywgdGFnTnVtYmVyOiBpdGVtLmNvbnRleHQgfSB9KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzLmNhY2hlKGl0ZW0udHlwZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1JlcGVhdGVkID0gISFpdGVtLnJlcGVhdGVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgbGV0IHZhbHVlID0gIWlzUmVwZWF0ZWQgPyB0aGlzLmdldChpdGVtLnR5cGUsIHRydWUpLnNjaGVtYSA6IGFzbjFJdGVtO1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWUgPVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFwidmFsdWVCbG9ja1wiIGluIHZhbHVlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gdmFsdWUudmFsdWVCbG9jay52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZS52YWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFzbjFWYWx1ZS5wdXNoKG5ldyBhc24xanMuQ29uc3RydWN0ZWQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU6ICFpc1JlcGVhdGVkID8gbmFtZSA6IFwiXCIsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uYWwsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWRCbG9jazogeyB0YWdDbGFzczogMywgdGFnTnVtYmVyOiBpdGVtLmNvbnRleHQgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGFzbjFWYWx1ZS5wdXNoKG5ldyBhc24xanMuQ29uc3RydWN0ZWQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgb3B0aW9uYWwsXG4gICAgICAgICAgICAgICAgICAgICAgICBpZEJsb2NrOiB7IHRhZ0NsYXNzOiAzLCB0YWdOdW1iZXI6IGl0ZW0uY29udGV4dCB9LFxuICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWU6IFthc24xSXRlbV0sXG4gICAgICAgICAgICAgICAgICAgIH0pKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBhc24xSXRlbS5vcHRpb25hbCA9IG9wdGlvbmFsO1xuICAgICAgICAgICAgICAgIGFzbjFWYWx1ZS5wdXNoKGFzbjFJdGVtKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBzd2l0Y2ggKHNjaGVtYS50eXBlKSB7XG4gICAgICAgICAgICBjYXNlIEFzblR5cGVUeXBlcy5TZXF1ZW5jZTpcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IGFzbjFqcy5TZXF1ZW5jZSh7IHZhbHVlOiBhc24xVmFsdWUsIG5hbWU6IFwiXCIgfSk7XG4gICAgICAgICAgICBjYXNlIEFzblR5cGVUeXBlcy5TZXQ6XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBhc24xanMuU2V0KHsgdmFsdWU6IGFzbjFWYWx1ZSwgbmFtZTogXCJcIiB9KTtcbiAgICAgICAgICAgIGNhc2UgQXNuVHlwZVR5cGVzLkNob2ljZTpcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IGFzbjFqcy5DaG9pY2UoeyB2YWx1ZTogYXNuMVZhbHVlLCBuYW1lOiBcIlwiIH0pO1xuICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuc3VwcG9ydGVkIEFTTjEgdHlwZSBpbiB1c2VgKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBzZXQodGFyZ2V0LCBzY2hlbWEpIHtcbiAgICAgICAgdGhpcy5pdGVtcy5zZXQodGFyZ2V0LCBzY2hlbWEpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9XG4gICAgZmluZFBhcmVudFNjaGVtYSh0YXJnZXQpIHtcbiAgICAgICAgY29uc3QgcGFyZW50ID0gT2JqZWN0LmdldFByb3RvdHlwZU9mKHRhcmdldCk7XG4gICAgICAgIGlmIChwYXJlbnQpIHtcbiAgICAgICAgICAgIGNvbnN0IHNjaGVtYSA9IHRoaXMuaXRlbXMuZ2V0KHBhcmVudCk7XG4gICAgICAgICAgICByZXR1cm4gc2NoZW1hIHx8IHRoaXMuZmluZFBhcmVudFNjaGVtYShwYXJlbnQpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/schema.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/serializer.js":
/*!***************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/serializer.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AsnSerializer: () => (/* binding */ AsnSerializer)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var _converters__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./converters */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/converters.js\");\n/* harmony import */ var _enums__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./enums */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/enums.js\");\n/* harmony import */ var _helper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./helper */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/helper.js\");\n/* harmony import */ var _storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./storage */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js\");\n\n\n\n\n\nclass AsnSerializer {\n    static serialize(obj) {\n        if (obj instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BaseBlock) {\n            return obj.toBER(false);\n        }\n        return this.toASN(obj).toBER(false);\n    }\n    static toASN(obj) {\n        if (obj && typeof obj === \"object\" && (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(obj)) {\n            return obj.toASN();\n        }\n        if (!(obj && typeof obj === \"object\")) {\n            throw new TypeError(\"Parameter 1 should be type of Object.\");\n        }\n        const target = obj.constructor;\n        const schema = _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.get(target);\n        _storage__WEBPACK_IMPORTED_MODULE_4__.schemaStorage.cache(target);\n        let asn1Value = [];\n        if (schema.itemType) {\n            if (!Array.isArray(obj)) {\n                throw new TypeError(\"Parameter 1 should be type of Array.\");\n            }\n            if (typeof schema.itemType === \"number\") {\n                const converter = _converters__WEBPACK_IMPORTED_MODULE_1__.defaultConverter(schema.itemType);\n                if (!converter) {\n                    throw new Error(`Cannot get default converter for array item of ${target.name} ASN1 schema`);\n                }\n                asn1Value = obj.map((o) => converter.toASN(o));\n            }\n            else {\n                asn1Value = obj.map((o) => this.toAsnItem({ type: schema.itemType }, \"[]\", target, o));\n            }\n        }\n        else {\n            for (const key in schema.items) {\n                const schemaItem = schema.items[key];\n                const objProp = obj[key];\n                if (objProp === undefined ||\n                    schemaItem.defaultValue === objProp ||\n                    (typeof schemaItem.defaultValue === \"object\" &&\n                        typeof objProp === \"object\" &&\n                        (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isArrayEqual)(this.serialize(schemaItem.defaultValue), this.serialize(objProp)))) {\n                    continue;\n                }\n                const asn1Item = AsnSerializer.toAsnItem(schemaItem, key, target, objProp);\n                if (typeof schemaItem.context === \"number\") {\n                    if (schemaItem.implicit) {\n                        if (!schemaItem.repeated &&\n                            (typeof schemaItem.type === \"number\" || (0,_helper__WEBPACK_IMPORTED_MODULE_3__.isConvertible)(schemaItem.type))) {\n                            const value = {};\n                            value.valueHex =\n                                asn1Item instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.Null\n                                    ? asn1Item.valueBeforeDecodeView\n                                    : asn1Item.valueBlock.toBER();\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Primitive({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                ...value,\n                            }));\n                        }\n                        else {\n                            asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                                optional: schemaItem.optional,\n                                idBlock: {\n                                    tagClass: 3,\n                                    tagNumber: schemaItem.context,\n                                },\n                                value: asn1Item.valueBlock.value,\n                            }));\n                        }\n                    }\n                    else {\n                        asn1Value.push(new asn1js__WEBPACK_IMPORTED_MODULE_0__.Constructed({\n                            optional: schemaItem.optional,\n                            idBlock: {\n                                tagClass: 3,\n                                tagNumber: schemaItem.context,\n                            },\n                            value: [asn1Item],\n                        }));\n                    }\n                }\n                else if (schemaItem.repeated) {\n                    asn1Value = asn1Value.concat(asn1Item);\n                }\n                else {\n                    asn1Value.push(asn1Item);\n                }\n            }\n        }\n        let asnSchema;\n        switch (schema.type) {\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Sequence:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Set:\n                asnSchema = new asn1js__WEBPACK_IMPORTED_MODULE_0__.Set({ value: asn1Value });\n                break;\n            case _enums__WEBPACK_IMPORTED_MODULE_2__.AsnTypeTypes.Choice:\n                if (!asn1Value[0]) {\n                    throw new Error(`Schema '${target.name}' has wrong data. Choice cannot be empty.`);\n                }\n                asnSchema = asn1Value[0];\n                break;\n        }\n        return asnSchema;\n    }\n    static toAsnItem(schemaItem, key, target, objProp) {\n        let asn1Item;\n        if (typeof schemaItem.type === \"number\") {\n            const converter = schemaItem.converter;\n            if (!converter) {\n                throw new Error(`Property '${key}' doesn't have converter for type ${_enums__WEBPACK_IMPORTED_MODULE_2__.AsnPropTypes[schemaItem.type]} in schema '${target.name}'`);\n            }\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => converter.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = converter.toASN(objProp);\n            }\n        }\n        else {\n            if (schemaItem.repeated) {\n                if (!Array.isArray(objProp)) {\n                    throw new TypeError(\"Parameter 'objProp' should be type of Array.\");\n                }\n                const items = Array.from(objProp, (element) => this.toASN(element));\n                const Container = schemaItem.repeated === \"sequence\" ? asn1js__WEBPACK_IMPORTED_MODULE_0__.Sequence : asn1js__WEBPACK_IMPORTED_MODULE_0__.Set;\n                asn1Item = new Container({\n                    value: items,\n                });\n            }\n            else {\n                asn1Item = this.toASN(objProp);\n            }\n        }\n        return asn1Item;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/serializer.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js":
/*!************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js ***!
  \************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   schemaStorage: () => (/* binding */ schemaStorage)\n/* harmony export */ });\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./schema */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/schema.js\");\n\nconst schemaStorage = new _schema__WEBPACK_IMPORTED_MODULE_0__.AsnSchemaStorage();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvc3RvcmFnZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUNyQywwQkFBMEIscURBQWdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvc3RvcmFnZS5qcz84MTIxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFzblNjaGVtYVN0b3JhZ2UgfSBmcm9tIFwiLi9zY2hlbWFcIjtcbmV4cG9ydCBjb25zdCBzY2hlbWFTdG9yYWdlID0gbmV3IEFzblNjaGVtYVN0b3JhZ2UoKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/storage.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js":
/*!*********************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* binding */ BitString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n\n\nclass BitString {\n    constructor(params, unusedBits = 0) {\n        this.unusedBits = 0;\n        this.value = new ArrayBuffer(0);\n        if (params) {\n            if (typeof params === \"number\") {\n                this.fromNumber(params);\n            }\n            else if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(params)) {\n                this.unusedBits = unusedBits;\n                this.value = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(params);\n            }\n            else {\n                throw TypeError(\"Unsupported type of 'params' argument for BitString\");\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 BitString\");\n        }\n        this.unusedBits = asn.valueBlock.unusedBits;\n        this.value = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ unusedBits: this.unusedBits, valueHex: this.value });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.BitString({ name });\n    }\n    toNumber() {\n        let res = \"\";\n        const uintArray = new Uint8Array(this.value);\n        for (const octet of uintArray) {\n            res += octet.toString(2).padStart(8, \"0\");\n        }\n        res = res.split(\"\").reverse().join(\"\");\n        if (this.unusedBits) {\n            res = res.slice(this.unusedBits).padStart(this.unusedBits, \"0\");\n        }\n        return parseInt(res, 2);\n    }\n    fromNumber(value) {\n        let bits = value.toString(2);\n        const octetSize = (bits.length + 7) >> 3;\n        this.unusedBits = (octetSize << 3) - bits.length;\n        const octets = new Uint8Array(octetSize);\n        bits = bits\n            .padStart(octetSize << 3, \"0\")\n            .split(\"\")\n            .reverse()\n            .join(\"\");\n        let index = 0;\n        while (index < octetSize) {\n            octets[index] = parseInt(bits.slice(index << 3, (index << 3) + 8), 2);\n            index++;\n        }\n        this.value = octets.buffer;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/index.js":
/*!****************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/types/index.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BitString: () => (/* reexport safe */ _bit_string__WEBPACK_IMPORTED_MODULE_0__.BitString),\n/* harmony export */   OctetString: () => (/* reexport safe */ _octet_string__WEBPACK_IMPORTED_MODULE_1__.OctetString)\n/* harmony export */ });\n/* harmony import */ var _bit_string__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bit_string */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/bit_string.js\");\n/* harmony import */ var _octet_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./octet_string */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvdHlwZXMvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE2QjtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci9hc24xLXNjaGVtYS9idWlsZC9lczIwMTUvdHlwZXMvaW5kZXguanM/MzJiOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiLi9iaXRfc3RyaW5nXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9vY3RldF9zdHJpbmdcIjtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/index.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OctetString: () => (/* binding */ OctetString)\n/* harmony export */ });\n/* harmony import */ var asn1js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! asn1js */ \"(rsc)/../../node_modules/asn1js/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n\n\nclass OctetString {\n    get byteLength() {\n        return this.buffer.byteLength;\n    }\n    get byteOffset() {\n        return 0;\n    }\n    constructor(param) {\n        if (typeof param === \"number\") {\n            this.buffer = new ArrayBuffer(param);\n        }\n        else {\n            if (pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.isBufferSource(param)) {\n                this.buffer = pvtsutils__WEBPACK_IMPORTED_MODULE_1__.BufferSourceConverter.toArrayBuffer(param);\n            }\n            else if (Array.isArray(param)) {\n                this.buffer = new Uint8Array(param);\n            }\n            else {\n                this.buffer = new ArrayBuffer(0);\n            }\n        }\n    }\n    fromASN(asn) {\n        if (!(asn instanceof asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString)) {\n            throw new TypeError(\"Argument 'asn' is not instance of ASN.1 OctetString\");\n        }\n        this.buffer = asn.valueBlock.valueHex;\n        return this;\n    }\n    toASN() {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ valueHex: this.buffer });\n    }\n    toSchema(name) {\n        return new asn1js__WEBPACK_IMPORTED_MODULE_0__.OctetString({ name });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/types/octet_string.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/json-schema/build/index.es.js":
/*!******************************************************************!*\
  !*** ../../node_modules/@peculiar/json-schema/build/index.es.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonError: () => (/* binding */ JsonError),\n/* harmony export */   JsonParser: () => (/* binding */ JsonParser),\n/* harmony export */   JsonProp: () => (/* binding */ JsonProp),\n/* harmony export */   JsonPropTypes: () => (/* binding */ JsonPropTypes),\n/* harmony export */   JsonSerializer: () => (/* binding */ JsonSerializer),\n/* harmony export */   KeyError: () => (/* binding */ KeyError),\n/* harmony export */   ParserError: () => (/* binding */ ParserError),\n/* harmony export */   SerializerError: () => (/* binding */ SerializerError),\n/* harmony export */   TransformError: () => (/* binding */ TransformError),\n/* harmony export */   ValidationError: () => (/* binding */ ValidationError)\n/* harmony export */ });\n/**\n * Copyright (c) 2020, Peculiar Ventures, All rights reserved.\n */\n\nclass JsonError extends Error {\r\n    constructor(message, innerError) {\r\n        super(innerError\r\n            ? `${message}. See the inner exception for more details.`\r\n            : message);\r\n        this.message = message;\r\n        this.innerError = innerError;\r\n    }\r\n}\n\nclass TransformError extends JsonError {\r\n    constructor(schema, message, innerError) {\r\n        super(message, innerError);\r\n        this.schema = schema;\r\n    }\r\n}\n\nclass ParserError extends TransformError {\r\n    constructor(schema, message, innerError) {\r\n        super(schema, `JSON doesn't match to '${schema.target.name}' schema. ${message}`, innerError);\r\n    }\r\n}\n\nclass ValidationError extends JsonError {\r\n}\n\nclass SerializerError extends JsonError {\r\n    constructor(schemaName, message, innerError) {\r\n        super(`Cannot serialize by '${schemaName}' schema. ${message}`, innerError);\r\n        this.schemaName = schemaName;\r\n    }\r\n}\n\nclass KeyError extends ParserError {\r\n    constructor(schema, keys, errors = {}) {\r\n        super(schema, \"Some keys doesn't match to schema\");\r\n        this.keys = keys;\r\n        this.errors = errors;\r\n    }\r\n}\n\nvar JsonPropTypes;\r\n(function (JsonPropTypes) {\r\n    JsonPropTypes[JsonPropTypes[\"Any\"] = 0] = \"Any\";\r\n    JsonPropTypes[JsonPropTypes[\"Boolean\"] = 1] = \"Boolean\";\r\n    JsonPropTypes[JsonPropTypes[\"Number\"] = 2] = \"Number\";\r\n    JsonPropTypes[JsonPropTypes[\"String\"] = 3] = \"String\";\r\n})(JsonPropTypes || (JsonPropTypes = {}));\n\nfunction checkType(value, type) {\r\n    switch (type) {\r\n        case JsonPropTypes.Boolean:\r\n            return typeof value === \"boolean\";\r\n        case JsonPropTypes.Number:\r\n            return typeof value === \"number\";\r\n        case JsonPropTypes.String:\r\n            return typeof value === \"string\";\r\n    }\r\n    return true;\r\n}\r\nfunction throwIfTypeIsWrong(value, type) {\r\n    if (!checkType(value, type)) {\r\n        throw new TypeError(`Value must be ${JsonPropTypes[type]}`);\r\n    }\r\n}\r\nfunction isConvertible(target) {\r\n    if (target && target.prototype) {\r\n        if (target.prototype.toJSON && target.prototype.fromJSON) {\r\n            return true;\r\n        }\r\n        else {\r\n            return isConvertible(target.prototype);\r\n        }\r\n    }\r\n    else {\r\n        return !!(target && target.toJSON && target.fromJSON);\r\n    }\r\n}\n\nclass JsonSchemaStorage {\r\n    constructor() {\r\n        this.items = new Map();\r\n    }\r\n    has(target) {\r\n        return this.items.has(target) || !!this.findParentSchema(target);\r\n    }\r\n    get(target) {\r\n        const schema = this.items.get(target) || this.findParentSchema(target);\r\n        if (!schema) {\r\n            throw new Error(\"Cannot get schema for current target\");\r\n        }\r\n        return schema;\r\n    }\r\n    create(target) {\r\n        const schema = { names: {} };\r\n        const parentSchema = this.findParentSchema(target);\r\n        if (parentSchema) {\r\n            Object.assign(schema, parentSchema);\r\n            schema.names = {};\r\n            for (const name in parentSchema.names) {\r\n                schema.names[name] = Object.assign({}, parentSchema.names[name]);\r\n            }\r\n        }\r\n        schema.target = target;\r\n        return schema;\r\n    }\r\n    set(target, schema) {\r\n        this.items.set(target, schema);\r\n        return this;\r\n    }\r\n    findParentSchema(target) {\r\n        const parent = target.__proto__;\r\n        if (parent) {\r\n            const schema = this.items.get(parent);\r\n            return schema || this.findParentSchema(parent);\r\n        }\r\n        return null;\r\n    }\r\n}\n\nconst DEFAULT_SCHEMA = \"default\";\r\nconst schemaStorage = new JsonSchemaStorage();\n\nclass PatternValidation {\r\n    constructor(pattern) {\r\n        this.pattern = new RegExp(pattern);\r\n    }\r\n    validate(value) {\r\n        const pattern = new RegExp(this.pattern.source, this.pattern.flags);\r\n        if (typeof value !== \"string\") {\r\n            throw new ValidationError(\"Incoming value must be string\");\r\n        }\r\n        if (!pattern.exec(value)) {\r\n            throw new ValidationError(`Value doesn't match to pattern '${pattern.toString()}'`);\r\n        }\r\n    }\r\n}\n\nclass InclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min <= value && value <= this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason [${min},${max}]`);\r\n        }\r\n    }\r\n}\n\nclass ExclusiveValidation {\r\n    constructor(min = Number.MIN_VALUE, max = Number.MAX_VALUE) {\r\n        this.min = min;\r\n        this.max = max;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.Number);\r\n        if (!(this.min < value && value < this.max)) {\r\n            const min = this.min === Number.MIN_VALUE ? \"MIN\" : this.min;\r\n            const max = this.max === Number.MAX_VALUE ? \"MAX\" : this.max;\r\n            throw new ValidationError(`Value doesn't match to diapason (${min},${max})`);\r\n        }\r\n    }\r\n}\n\nclass LengthValidation {\r\n    constructor(length, minLength, maxLength) {\r\n        this.length = length;\r\n        this.minLength = minLength;\r\n        this.maxLength = maxLength;\r\n    }\r\n    validate(value) {\r\n        if (this.length !== undefined) {\r\n            if (value.length !== this.length) {\r\n                throw new ValidationError(`Value length must be exactly ${this.length}.`);\r\n            }\r\n            return;\r\n        }\r\n        if (this.minLength !== undefined) {\r\n            if (value.length < this.minLength) {\r\n                throw new ValidationError(`Value length must be more than ${this.minLength}.`);\r\n            }\r\n        }\r\n        if (this.maxLength !== undefined) {\r\n            if (value.length > this.maxLength) {\r\n                throw new ValidationError(`Value length must be less than ${this.maxLength}.`);\r\n            }\r\n        }\r\n    }\r\n}\n\nclass EnumerationValidation {\r\n    constructor(enumeration) {\r\n        this.enumeration = enumeration;\r\n    }\r\n    validate(value) {\r\n        throwIfTypeIsWrong(value, JsonPropTypes.String);\r\n        if (!this.enumeration.includes(value)) {\r\n            throw new ValidationError(`Value must be one of ${this.enumeration.map((v) => `'${v}'`).join(\", \")}`);\r\n        }\r\n    }\r\n}\n\nclass JsonTransform {\r\n    static checkValues(data, schemaItem) {\r\n        const values = Array.isArray(data) ? data : [data];\r\n        for (const value of values) {\r\n            for (const validation of schemaItem.validations) {\r\n                if (validation instanceof LengthValidation && schemaItem.repeated) {\r\n                    validation.validate(data);\r\n                }\r\n                else {\r\n                    validation.validate(value);\r\n                }\r\n            }\r\n        }\r\n    }\r\n    static checkTypes(value, schemaItem) {\r\n        if (schemaItem.repeated && !Array.isArray(value)) {\r\n            throw new TypeError(\"Value must be Array\");\r\n        }\r\n        if (typeof schemaItem.type === \"number\") {\r\n            const values = Array.isArray(value) ? value : [value];\r\n            for (const v of values) {\r\n                throwIfTypeIsWrong(v, schemaItem.type);\r\n            }\r\n        }\r\n    }\r\n    static getSchemaByName(schema, name = DEFAULT_SCHEMA) {\r\n        return { ...schema.names[DEFAULT_SCHEMA], ...schema.names[name] };\r\n    }\r\n}\n\nclass JsonSerializer extends JsonTransform {\r\n    static serialize(obj, options, replacer, space) {\r\n        const json = this.toJSON(obj, options);\r\n        return JSON.stringify(json, replacer, space);\r\n    }\r\n    static toJSON(obj, options = {}) {\r\n        let res;\r\n        let targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        if (isConvertible(obj)) {\r\n            return obj.toJSON();\r\n        }\r\n        if (Array.isArray(obj)) {\r\n            res = [];\r\n            for (const item of obj) {\r\n                res.push(this.toJSON(item, options));\r\n            }\r\n        }\r\n        else if (typeof obj === \"object\") {\r\n            if (targetSchema && !schemaStorage.has(targetSchema)) {\r\n                throw new JsonError(\"Cannot get schema for `targetSchema` param\");\r\n            }\r\n            targetSchema = (targetSchema || obj.constructor);\r\n            if (schemaStorage.has(targetSchema)) {\r\n                const schema = schemaStorage.get(targetSchema);\r\n                res = {};\r\n                const namedSchema = this.getSchemaByName(schema, schemaName);\r\n                for (const key in namedSchema) {\r\n                    try {\r\n                        const item = namedSchema[key];\r\n                        const objItem = obj[key];\r\n                        let value;\r\n                        if ((item.optional && objItem === undefined)\r\n                            || (item.defaultValue !== undefined && objItem === item.defaultValue)) {\r\n                            continue;\r\n                        }\r\n                        if (!item.optional && objItem === undefined) {\r\n                            throw new SerializerError(targetSchema.name, `Property '${key}' is required.`);\r\n                        }\r\n                        if (typeof item.type === \"number\") {\r\n                            if (item.converter) {\r\n                                if (item.repeated) {\r\n                                    value = objItem.map((el) => item.converter.toJSON(el, obj));\r\n                                }\r\n                                else {\r\n                                    value = item.converter.toJSON(objItem, obj);\r\n                                }\r\n                            }\r\n                            else {\r\n                                value = objItem;\r\n                            }\r\n                        }\r\n                        else {\r\n                            if (item.repeated) {\r\n                                value = objItem.map((el) => this.toJSON(el, { schemaName }));\r\n                            }\r\n                            else {\r\n                                value = this.toJSON(objItem, { schemaName });\r\n                            }\r\n                        }\r\n                        this.checkTypes(value, item);\r\n                        this.checkValues(value, item);\r\n                        res[item.name || key] = value;\r\n                    }\r\n                    catch (e) {\r\n                        if (e instanceof SerializerError) {\r\n                            throw e;\r\n                        }\r\n                        else {\r\n                            throw new SerializerError(schema.target.name, `Property '${key}' is wrong. ${e.message}`, e);\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                res = {};\r\n                for (const key in obj) {\r\n                    res[key] = this.toJSON(obj[key], { schemaName });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            res = obj;\r\n        }\r\n        return res;\r\n    }\r\n}\n\nclass JsonParser extends JsonTransform {\r\n    static parse(data, options) {\r\n        const obj = JSON.parse(data);\r\n        return this.fromJSON(obj, options);\r\n    }\r\n    static fromJSON(target, options) {\r\n        const targetSchema = options.targetSchema;\r\n        const schemaName = options.schemaName || DEFAULT_SCHEMA;\r\n        const obj = new targetSchema();\r\n        if (isConvertible(obj)) {\r\n            return obj.fromJSON(target);\r\n        }\r\n        const schema = schemaStorage.get(targetSchema);\r\n        const namedSchema = this.getSchemaByName(schema, schemaName);\r\n        const keyErrors = {};\r\n        if (options.strictProperty && !Array.isArray(target)) {\r\n            JsonParser.checkStrictProperty(target, namedSchema, schema);\r\n        }\r\n        for (const key in namedSchema) {\r\n            try {\r\n                const item = namedSchema[key];\r\n                const name = item.name || key;\r\n                const value = target[name];\r\n                if (value === undefined && (item.optional || item.defaultValue !== undefined)) {\r\n                    continue;\r\n                }\r\n                if (!item.optional && value === undefined) {\r\n                    throw new ParserError(schema, `Property '${name}' is required.`);\r\n                }\r\n                this.checkTypes(value, item);\r\n                this.checkValues(value, item);\r\n                if (typeof (item.type) === \"number\") {\r\n                    if (item.converter) {\r\n                        if (item.repeated) {\r\n                            obj[key] = value.map((el) => item.converter.fromJSON(el, obj));\r\n                        }\r\n                        else {\r\n                            obj[key] = item.converter.fromJSON(value, obj);\r\n                        }\r\n                    }\r\n                    else {\r\n                        obj[key] = value;\r\n                    }\r\n                }\r\n                else {\r\n                    const newOptions = {\r\n                        ...options,\r\n                        targetSchema: item.type,\r\n                        schemaName,\r\n                    };\r\n                    if (item.repeated) {\r\n                        obj[key] = value.map((el) => this.fromJSON(el, newOptions));\r\n                    }\r\n                    else {\r\n                        obj[key] = this.fromJSON(value, newOptions);\r\n                    }\r\n                }\r\n            }\r\n            catch (e) {\r\n                if (!(e instanceof ParserError)) {\r\n                    e = new ParserError(schema, `Property '${key}' is wrong. ${e.message}`, e);\r\n                }\r\n                if (options.strictAllKeys) {\r\n                    keyErrors[key] = e;\r\n                }\r\n                else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        const keys = Object.keys(keyErrors);\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys, keyErrors);\r\n        }\r\n        return obj;\r\n    }\r\n    static checkStrictProperty(target, namedSchema, schema) {\r\n        const jsonProps = Object.keys(target);\r\n        const schemaProps = Object.keys(namedSchema);\r\n        const keys = [];\r\n        for (const key of jsonProps) {\r\n            if (schemaProps.indexOf(key) === -1) {\r\n                keys.push(key);\r\n            }\r\n        }\r\n        if (keys.length) {\r\n            throw new KeyError(schema, keys);\r\n        }\r\n    }\r\n}\n\nfunction getValidations(item) {\r\n    const validations = [];\r\n    if (item.pattern) {\r\n        validations.push(new PatternValidation(item.pattern));\r\n    }\r\n    if (item.type === JsonPropTypes.Number || item.type === JsonPropTypes.Any) {\r\n        if (item.minInclusive !== undefined || item.maxInclusive !== undefined) {\r\n            validations.push(new InclusiveValidation(item.minInclusive, item.maxInclusive));\r\n        }\r\n        if (item.minExclusive !== undefined || item.maxExclusive !== undefined) {\r\n            validations.push(new ExclusiveValidation(item.minExclusive, item.maxExclusive));\r\n        }\r\n        if (item.enumeration !== undefined) {\r\n            validations.push(new EnumerationValidation(item.enumeration));\r\n        }\r\n    }\r\n    if (item.type === JsonPropTypes.String || item.repeated || item.type === JsonPropTypes.Any) {\r\n        if (item.length !== undefined || item.minLength !== undefined || item.maxLength !== undefined) {\r\n            validations.push(new LengthValidation(item.length, item.minLength, item.maxLength));\r\n        }\r\n    }\r\n    return validations;\r\n}\r\nconst JsonProp = (options = {}) => (target, propertyKey) => {\r\n    const errorMessage = `Cannot set type for ${propertyKey} property of ${target.constructor.name} schema`;\r\n    let schema;\r\n    if (!schemaStorage.has(target.constructor)) {\r\n        schema = schemaStorage.create(target.constructor);\r\n        schemaStorage.set(target.constructor, schema);\r\n    }\r\n    else {\r\n        schema = schemaStorage.get(target.constructor);\r\n        if (schema.target !== target.constructor) {\r\n            schema = schemaStorage.create(target.constructor);\r\n            schemaStorage.set(target.constructor, schema);\r\n        }\r\n    }\r\n    const defaultSchema = {\r\n        type: JsonPropTypes.Any,\r\n        validations: [],\r\n    };\r\n    const copyOptions = Object.assign(defaultSchema, options);\r\n    copyOptions.validations = getValidations(copyOptions);\r\n    if (typeof copyOptions.type !== \"number\") {\r\n        if (!schemaStorage.has(copyOptions.type) && !isConvertible(copyOptions.type)) {\r\n            throw new Error(`${errorMessage}. Assigning type doesn't have schema.`);\r\n        }\r\n    }\r\n    let schemaNames;\r\n    if (Array.isArray(options.schema)) {\r\n        schemaNames = options.schema;\r\n    }\r\n    else {\r\n        schemaNames = [options.schema || DEFAULT_SCHEMA];\r\n    }\r\n    for (const schemaName of schemaNames) {\r\n        if (!schema.names[schemaName]) {\r\n            schema.names[schemaName] = {};\r\n        }\r\n        const namedSchema = schema.names[schemaName];\r\n        namedSchema[propertyKey] = copyOptions;\r\n    }\r\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/json-schema/build/index.es.js\n");

/***/ }),

/***/ "(rsc)/../../node_modules/@peculiar/webcrypto/build/webcrypto.es.js":
/*!********************************************************************!*\
  !*** ../../node_modules/@peculiar/webcrypto/build/webcrypto.es.js ***!
  \********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Crypto: () => (/* binding */ Crypto),\n/* harmony export */   CryptoKey: () => (/* reexport safe */ webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey)\n/* harmony export */ });\n/* harmony import */ var webcrypto_core__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! webcrypto-core */ \"(rsc)/../../node_modules/webcrypto-core/build/webcrypto-core.es.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! process */ \"process\");\n/* harmony import */ var process__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(process__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! tslib */ \"(rsc)/../../node_modules/tslib/tslib.es6.js\");\n/* harmony import */ var _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @peculiar/json-schema */ \"(rsc)/../../node_modules/@peculiar/json-schema/build/index.es.js\");\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @peculiar/asn1-schema */ \"(rsc)/../../node_modules/@peculiar/asn1-schema/build/es2015/index.js\");\n/*!\n Copyright (c) Peculiar Ventures, LLC\n*/\n\n\n\n\n\n\n\n\n\n\n\n\nconst JsonBase64UrlConverter = {\r\n    fromJSON: (value) => Buffer.from(pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(value)),\r\n    toJSON: (value) => pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(value),\r\n};\n\nclass CryptoKey extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.data = Buffer.alloc(0);\r\n        this.algorithm = { name: \"\" };\r\n        this.extractable = false;\r\n        this.type = \"secret\";\r\n        this.usages = [];\r\n        this.kty = \"oct\";\r\n        this.alg = \"\";\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"ext\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.Boolean, optional: true })\r\n], CryptoKey.prototype, \"extractable\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"key_ops\", type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String, repeated: true, optional: true })\r\n], CryptoKey.prototype, \"usages\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String })\r\n], CryptoKey.prototype, \"kty\", void 0);\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ type: _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonPropTypes.String, optional: true })\r\n], CryptoKey.prototype, \"alg\", void 0);\n\nclass SymmetricKey extends CryptoKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.kty = \"oct\";\r\n        this.type = \"secret\";\r\n    }\r\n}\n\nclass AsymmetricKey extends CryptoKey {\r\n}\n\nclass AesCryptoKey extends SymmetricKey {\r\n    get alg() {\r\n        switch (this.algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return `A${this.algorithm.length}CBC`;\r\n            case \"AES-CTR\":\r\n                return `A${this.algorithm.length}CTR`;\r\n            case \"AES-GCM\":\r\n                return `A${this.algorithm.length}GCM`;\r\n            case \"AES-KW\":\r\n                return `A${this.algorithm.length}KW`;\r\n            case \"AES-CMAC\":\r\n                return `A${this.algorithm.length}CMAC`;\r\n            case \"AES-ECB\":\r\n                return `A${this.algorithm.length}ECB`;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\r\n        }\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], AesCryptoKey.prototype, \"data\", void 0);\n\nconst keyStorage = new WeakMap();\r\nfunction getCryptoKey(key) {\r\n    const res = keyStorage.get(key);\r\n    if (!res) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Cannot get CryptoKey from secure storage\");\r\n    }\r\n    return res;\r\n}\r\nfunction setCryptoKey(value) {\r\n    const key = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoKey.create(value.algorithm, value.type, value.extractable, value.usages);\r\n    Object.freeze(key);\r\n    keyStorage.set(key, value);\r\n    return key;\r\n}\n\nclass AesCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const key = new AesCryptoKey();\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(algorithm.length >> 3);\r\n        return key;\r\n    }\r\n    static async exportKey(format, key) {\r\n        if (!(key instanceof AesCryptoKey)) {\r\n            throw new Error(\"key: Is not AesCryptoKey\");\r\n        }\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"raw\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: AesCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new AesCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = algorithm;\r\n        key.algorithm.length = key.data.length << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        switch (key.algorithm.length) {\r\n            case 128:\r\n            case 192:\r\n            case 256:\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Is wrong key length\");\r\n        }\r\n        return key;\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return this.encryptAesCBC(algorithm, key, Buffer.from(data));\r\n            case \"AES-CTR\":\r\n                return this.encryptAesCTR(algorithm, key, Buffer.from(data));\r\n            case \"AES-GCM\":\r\n                return this.encryptAesGCM(algorithm, key, Buffer.from(data));\r\n            case \"AES-KW\":\r\n                return this.encryptAesKW(algorithm, key, Buffer.from(data));\r\n            case \"AES-ECB\":\r\n                return this.encryptAesECB(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        if (!(key instanceof AesCryptoKey)) {\r\n            throw new Error(\"key: Is not AesCryptoKey\");\r\n        }\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"AES-CBC\":\r\n                return this.decryptAesCBC(algorithm, key, Buffer.from(data));\r\n            case \"AES-CTR\":\r\n                return this.decryptAesCTR(algorithm, key, Buffer.from(data));\r\n            case \"AES-GCM\":\r\n                return this.decryptAesGCM(algorithm, key, Buffer.from(data));\r\n            case \"AES-KW\":\r\n                return this.decryptAesKW(algorithm, key, Buffer.from(data));\r\n            case \"AES-ECB\":\r\n                return this.decryptAesECB(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encryptAesCBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesCBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesCTR(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-ctr`, key.data, Buffer.from(algorithm.counter));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesCTR(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-ctr`, key.data, new Uint8Array(algorithm.counter));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesGCM(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-gcm`, key.data, Buffer.from(algorithm.iv), {\r\n            authTagLength: (algorithm.tagLength || 128) >> 3,\r\n        });\r\n        if (algorithm.additionalData) {\r\n            cipher.setAAD(Buffer.from(algorithm.additionalData));\r\n        }\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final(), cipher.getAuthTag()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesGCM(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-gcm`, key.data, new Uint8Array(algorithm.iv));\r\n        const tagLength = (algorithm.tagLength || 128) >> 3;\r\n        const enc = data.slice(0, data.length - tagLength);\r\n        const tag = data.slice(data.length - tagLength);\r\n        if (algorithm.additionalData) {\r\n            decipher.setAAD(Buffer.from(algorithm.additionalData));\r\n        }\r\n        decipher.setAuthTag(tag);\r\n        let dec = decipher.update(enc);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesKW(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        return new Uint8Array(enc).buffer;\r\n    }\r\n    static async decryptAesKW(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`id-aes${key.algorithm.length}-wrap`, key.data, this.AES_KW_IV);\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptAesECB(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptAesECB(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`aes-${key.algorithm.length}-ecb`, key.data, new Uint8Array(0));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n}\r\nAesCrypto.AES_KW_IV = Buffer.from(\"A6A6A6A6A6A6A6A6\", \"hex\");\n\nclass AesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCbcProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nconst zero = Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);\r\nconst rb = Buffer.from([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 135]);\r\nconst blockSize = 16;\r\nfunction bitShiftLeft(buffer) {\r\n    const shifted = Buffer.alloc(buffer.length);\r\n    const last = buffer.length - 1;\r\n    for (let index = 0; index < last; index++) {\r\n        shifted[index] = buffer[index] << 1;\r\n        if (buffer[index + 1] & 0x80) {\r\n            shifted[index] += 0x01;\r\n        }\r\n    }\r\n    shifted[last] = buffer[last] << 1;\r\n    return shifted;\r\n}\r\nfunction xor(a, b) {\r\n    const length = Math.min(a.length, b.length);\r\n    const output = Buffer.alloc(length);\r\n    for (let index = 0; index < length; index++) {\r\n        output[index] = a[index] ^ b[index];\r\n    }\r\n    return output;\r\n}\r\nfunction aes(key, message) {\r\n    const cipher = crypto__WEBPACK_IMPORTED_MODULE_1__.createCipheriv(`aes${key.length << 3}`, key, zero);\r\n    const result = cipher.update(message);\r\n    cipher.final();\r\n    return result;\r\n}\r\nfunction getMessageBlock(message, blockIndex) {\r\n    const block = Buffer.alloc(blockSize);\r\n    const start = blockIndex * blockSize;\r\n    const end = start + blockSize;\r\n    message.copy(block, 0, start, end);\r\n    return block;\r\n}\r\nfunction getPaddedMessageBlock(message, blockIndex) {\r\n    const block = Buffer.alloc(blockSize);\r\n    const start = blockIndex * blockSize;\r\n    const end = message.length;\r\n    block.fill(0);\r\n    message.copy(block, 0, start, end);\r\n    block[end - start] = 0x80;\r\n    return block;\r\n}\r\nfunction generateSubkeys(key) {\r\n    const l = aes(key, zero);\r\n    let subkey1 = bitShiftLeft(l);\r\n    if (l[0] & 0x80) {\r\n        subkey1 = xor(subkey1, rb);\r\n    }\r\n    let subkey2 = bitShiftLeft(subkey1);\r\n    if (subkey1[0] & 0x80) {\r\n        subkey2 = xor(subkey2, rb);\r\n    }\r\n    return { subkey1, subkey2 };\r\n}\r\nfunction aesCmac(key, message) {\r\n    const subkeys = generateSubkeys(key);\r\n    let blockCount = Math.ceil(message.length / blockSize);\r\n    let lastBlockCompleteFlag;\r\n    let lastBlock;\r\n    if (blockCount === 0) {\r\n        blockCount = 1;\r\n        lastBlockCompleteFlag = false;\r\n    }\r\n    else {\r\n        lastBlockCompleteFlag = (message.length % blockSize === 0);\r\n    }\r\n    const lastBlockIndex = blockCount - 1;\r\n    if (lastBlockCompleteFlag) {\r\n        lastBlock = xor(getMessageBlock(message, lastBlockIndex), subkeys.subkey1);\r\n    }\r\n    else {\r\n        lastBlock = xor(getPaddedMessageBlock(message, lastBlockIndex), subkeys.subkey2);\r\n    }\r\n    let x = zero;\r\n    let y;\r\n    for (let index = 0; index < lastBlockIndex; index++) {\r\n        y = xor(x, getMessageBlock(message, index));\r\n        x = aes(key, y);\r\n    }\r\n    y = xor(lastBlock, x);\r\n    return aes(key, y);\r\n}\r\nclass AesCmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCmacProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        const result = aesCmac(getCryptoKey(key).data, Buffer.from(data));\r\n        return new Uint8Array(result).buffer;\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        const signature2 = await this.sign(algorithm, key, data);\r\n        return Buffer.from(signature).compare(Buffer.from(signature2)) === 0;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesCtrProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesCtrProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesGcmProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesGcmProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesKwProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesKwProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass AesEcbProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AesEcbProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await AesCrypto.generateKey({\r\n            name: this.name,\r\n            length: algorithm.length,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return AesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return AesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return AesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const res = await AesCrypto.importKey(format, keyData, { name: algorithm.name }, extractable, keyUsages);\r\n        return setCryptoKey(res);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof AesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a AesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass DesCryptoKey extends SymmetricKey {\r\n    get alg() {\r\n        switch (this.algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return `DES-CBC`;\r\n            case \"DES-EDE3-CBC\":\r\n                return `3DES-CBC`;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.AlgorithmError(\"Unsupported algorithm name\");\r\n        }\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], DesCryptoKey.prototype, \"data\", void 0);\n\nclass DesCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const key = new DesCryptoKey();\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(algorithm.length >> 3);\r\n        return key;\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"raw\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: DesCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new DesCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = algorithm;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return this.encryptDesCBC(algorithm, key, Buffer.from(data));\r\n            case \"DES-EDE3-CBC\":\r\n                return this.encryptDesEDE3CBC(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        if (!(key instanceof DesCryptoKey)) {\r\n            throw new Error(\"key: Is not DesCryptoKey\");\r\n        }\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"DES-CBC\":\r\n                return this.decryptDesCBC(algorithm, key, Buffer.from(data));\r\n            case \"DES-EDE3-CBC\":\r\n                return this.decryptDesEDE3CBC(algorithm, key, Buffer.from(data));\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encryptDesCBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptDesCBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`des-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    static async encryptDesEDE3CBC(algorithm, key, data) {\r\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createCipheriv(`des-ede3-cbc`, key.data, Buffer.from(algorithm.iv));\r\n        let enc = cipher.update(data);\r\n        enc = Buffer.concat([enc, cipher.final()]);\r\n        const res = new Uint8Array(enc).buffer;\r\n        return res;\r\n    }\r\n    static async decryptDesEDE3CBC(algorithm, key, data) {\r\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_1___default().createDecipheriv(`des-ede3-cbc`, key.data, new Uint8Array(algorithm.iv));\r\n        let dec = decipher.update(data);\r\n        dec = Buffer.concat([dec, decipher.final()]);\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n}\n\nclass DesCbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.keySizeBits = 64;\r\n        this.ivSize = 8;\r\n        this.name = \"DES-CBC\";\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.generateKey({\r\n            name: this.name,\r\n            length: this.keySizeBits,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return DesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\r\n        if (key.data.length !== (this.keySizeBits >> 3)) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\r\n        }\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass DesEde3CbcProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.DesProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.keySizeBits = 192;\r\n        this.ivSize = 8;\r\n        this.name = \"DES-EDE3-CBC\";\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.generateKey({\r\n            name: this.name,\r\n            length: this.keySizeBits,\r\n        }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        return DesCrypto.encrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        return DesCrypto.decrypt(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return DesCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await DesCrypto.importKey(format, keyData, { name: this.name, length: this.keySizeBits }, extractable, keyUsages);\r\n        if (key.data.length !== (this.keySizeBits >> 3)) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"keyData: Wrong key size\");\r\n        }\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof DesCryptoKey)) {\r\n            throw new TypeError(\"key: Is not a DesCryptoKey\");\r\n        }\r\n    }\r\n}\n\nfunction getJwkAlgorithm(algorithm) {\r\n    switch (algorithm.name.toUpperCase()) {\r\n        case \"RSA-OAEP\": {\r\n            const mdSize = /(\\d+)$/.exec(algorithm.hash.name)[1];\r\n            return `RSA-OAEP${mdSize !== \"1\" ? `-${mdSize}` : \"\"}`;\r\n        }\r\n        case \"RSASSA-PKCS1-V1_5\":\r\n            return `RS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\r\n        case \"RSA-PSS\":\r\n            return `PS${/(\\d+)$/.exec(algorithm.hash.name)[1]}`;\r\n        case \"RSA-PKCS1\":\r\n            return `RS1`;\r\n        default:\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n    }\r\n}\n\nclass RsaPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"RSA\",\r\n            alg: getJwkAlgorithm(this.algorithm),\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = null;\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n    }\r\n}\n\nclass RsaPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"RSA\",\r\n            alg: getJwkAlgorithm(this.algorithm),\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = null;\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n    }\r\n}\n\nclass RsaCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new RsaPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new RsaPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const publicExponent = Buffer.concat([\r\n            Buffer.alloc(4 - algorithm.publicExponent.byteLength, 0),\r\n            Buffer.from(algorithm.publicExponent),\r\n        ]).readInt32BE(0);\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(\"rsa\", {\r\n            modulusLength: algorithm.modulusLength,\r\n            publicExponent,\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey });\r\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.publicKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPublicKey);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.RsaPrivateKey);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-PSS\":\r\n            case \"RSASSA-PKCS1-V1_5\":\r\n                return this.signRsa(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-PSS\":\r\n            case \"RSASSA-PKCS1-V1_5\":\r\n                return this.verifySSA(algorithm, key, data, signature);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async encrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-OAEP\":\r\n                return this.encryptOAEP(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static async decrypt(algorithm, key, data) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"RSA-OAEP\":\r\n                return this.decryptOAEP(algorithm, key, data);\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm: Is not recognized\");\r\n        }\r\n    }\r\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = null;\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new RsaPrivateKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\r\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.113549.1.1.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = null;\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new RsaPublicKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.algorithm.publicExponent = new Uint8Array(asnKey.publicExponent);\r\n        key.algorithm.modulusLength = asnKey.modulus.byteLength << 3;\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static getCryptoAlgorithm(alg) {\r\n        switch (alg.hash.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return \"RSA-SHA1\";\r\n            case \"SHA-256\":\r\n                return \"RSA-SHA256\";\r\n            case \"SHA-384\":\r\n                return \"RSA-SHA384\";\r\n            case \"SHA-512\":\r\n                return \"RSA-SHA512\";\r\n            case \"SHA3-256\":\r\n                return \"RSA-SHA3-256\";\r\n            case \"SHA3-384\":\r\n                return \"RSA-SHA3-384\";\r\n            case \"SHA3-512\":\r\n                return \"RSA-SHA3-512\";\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"algorithm.hash: Is not recognized\");\r\n        }\r\n    }\r\n    static signRsa(algorithm, key, data) {\r\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createSign(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\r\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_PSS_PADDING;\r\n            options.saltLength = algorithm.saltLength;\r\n        }\r\n        const signature = signer.sign(options);\r\n        return new Uint8Array(signature).buffer;\r\n    }\r\n    static verifySSA(algorithm, key, data, signature) {\r\n        const cryptoAlg = this.getCryptoAlgorithm(key.algorithm);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createVerify(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        if (algorithm.name.toUpperCase() === \"RSA-PSS\") {\r\n            options.padding = (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_PSS_PADDING;\r\n            options.saltLength = algorithm.saltLength;\r\n        }\r\n        const ok = signer.verify(options, signature);\r\n        return ok;\r\n    }\r\n    static encryptOAEP(algorithm, key, data) {\r\n        const options = {\r\n            key: `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_OAEP_PADDING,\r\n        };\r\n        if (algorithm.label) ;\r\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_1___default().publicEncrypt(options, data)).buffer;\r\n    }\r\n    static decryptOAEP(algorithm, key, data) {\r\n        const options = {\r\n            key: `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_PKCS1_OAEP_PADDING,\r\n        };\r\n        if (algorithm.label) ;\r\n        return new Uint8Array(crypto__WEBPACK_IMPORTED_MODULE_1___default().privateDecrypt(options, data)).buffer;\r\n    }\r\n}\r\nRsaCrypto.publicKeyUsages = [\"verify\", \"encrypt\", \"wrapKey\"];\r\nRsaCrypto.privateKeyUsages = [\"sign\", \"decrypt\", \"unwrapKey\"];\n\nclass RsaSsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaSsaProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass RsaPssProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaPssProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return RsaCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return RsaCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass ShaCrypto {\r\n    static size(algorithm) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return 160;\r\n            case \"SHA-256\":\r\n            case \"SHA3-256\":\r\n                return 256;\r\n            case \"SHA-384\":\r\n            case \"SHA3-384\":\r\n                return 384;\r\n            case \"SHA-512\":\r\n            case \"SHA3-512\":\r\n                return 512;\r\n            default:\r\n                throw new Error(\"Unrecognized name\");\r\n        }\r\n    }\r\n    static getAlgorithmName(algorithm) {\r\n        switch (algorithm.name.toUpperCase()) {\r\n            case \"SHA-1\":\r\n                return \"sha1\";\r\n            case \"SHA-256\":\r\n                return \"sha256\";\r\n            case \"SHA-384\":\r\n                return \"sha384\";\r\n            case \"SHA-512\":\r\n                return \"sha512\";\r\n            case \"SHA3-256\":\r\n                return \"sha3-256\";\r\n            case \"SHA3-384\":\r\n                return \"sha3-384\";\r\n            case \"SHA3-512\":\r\n                return \"sha3-512\";\r\n            default:\r\n                throw new Error(\"Unrecognized name\");\r\n        }\r\n    }\r\n    static digest(algorithm, data) {\r\n        const hashAlg = this.getAlgorithmName(algorithm);\r\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(hashAlg)\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hash).buffer;\r\n    }\r\n}\n\nclass RsaOaepProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.RsaOaepProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        const internalKey = getCryptoKey(key);\r\n        const dataView = new Uint8Array(data);\r\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\r\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\r\n        const dataLength = dataView.byteLength;\r\n        const psLength = keySize - dataLength - 2 * hashSize - 2;\r\n        if (dataLength > keySize - 2 * hashSize - 2) {\r\n            throw new Error(\"Data too large\");\r\n        }\r\n        const message = new Uint8Array(keySize);\r\n        const seed = message.subarray(1, hashSize + 1);\r\n        const dataBlock = message.subarray(hashSize + 1);\r\n        dataBlock.set(dataView, hashSize + psLength + 1);\r\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\r\n            .digest();\r\n        dataBlock.set(labelHash, 0);\r\n        dataBlock[hashSize + psLength] = 1;\r\n        crypto__WEBPACK_IMPORTED_MODULE_1___default().randomFillSync(seed);\r\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\r\n        for (let i = 0; i < dataBlock.length; i++) {\r\n            dataBlock[i] ^= dataBlockMask[i];\r\n        }\r\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\r\n        for (let i = 0; i < seed.length; i++) {\r\n            seed[i] ^= seedMask[i];\r\n        }\r\n        if (!internalKey.pem) {\r\n            internalKey.pem = `-----BEGIN PUBLIC KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_1___default().publicEncrypt({\r\n            key: internalKey.pem,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_NO_PADDING,\r\n        }, Buffer.from(message));\r\n        return new Uint8Array(pkcs0).buffer;\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        const internalKey = getCryptoKey(key);\r\n        const keySize = Math.ceil(internalKey.algorithm.modulusLength >> 3);\r\n        const hashSize = ShaCrypto.size(internalKey.algorithm.hash) >> 3;\r\n        const dataLength = data.byteLength;\r\n        if (dataLength !== keySize) {\r\n            throw new Error(\"Bad data\");\r\n        }\r\n        if (!internalKey.pem) {\r\n            internalKey.pem = `-----BEGIN PRIVATE KEY-----\\n${internalKey.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        let pkcs0 = crypto__WEBPACK_IMPORTED_MODULE_1___default().privateDecrypt({\r\n            key: internalKey.pem,\r\n            padding: (crypto__WEBPACK_IMPORTED_MODULE_1___default().constants).RSA_NO_PADDING,\r\n        }, Buffer.from(data));\r\n        const z = pkcs0[0];\r\n        const seed = pkcs0.subarray(1, hashSize + 1);\r\n        const dataBlock = pkcs0.subarray(hashSize + 1);\r\n        if (z !== 0) {\r\n            throw new Error(\"Decryption failed\");\r\n        }\r\n        const seedMask = this.mgf1(internalKey.algorithm.hash, dataBlock, seed.length);\r\n        for (let i = 0; i < seed.length; i++) {\r\n            seed[i] ^= seedMask[i];\r\n        }\r\n        const dataBlockMask = this.mgf1(internalKey.algorithm.hash, seed, dataBlock.length);\r\n        for (let i = 0; i < dataBlock.length; i++) {\r\n            dataBlock[i] ^= dataBlockMask[i];\r\n        }\r\n        const labelHash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(internalKey.algorithm.hash.name.replace(\"-\", \"\"))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(algorithm.label || new Uint8Array(0)))\r\n            .digest();\r\n        for (let i = 0; i < hashSize; i++) {\r\n            if (labelHash[i] !== dataBlock[i]) {\r\n                throw new Error(\"Decryption failed\");\r\n            }\r\n        }\r\n        let psEnd = hashSize;\r\n        for (; psEnd < dataBlock.length; psEnd++) {\r\n            const psz = dataBlock[psEnd];\r\n            if (psz === 1) {\r\n                break;\r\n            }\r\n            if (psz !== 0) {\r\n                throw new Error(\"Decryption failed\");\r\n            }\r\n        }\r\n        if (psEnd === dataBlock.length) {\r\n            throw new Error(\"Decryption failed\");\r\n        }\r\n        pkcs0 = dataBlock.subarray(psEnd + 1);\r\n        return new Uint8Array(pkcs0).buffer;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n    mgf1(algorithm, seed, length = 0) {\r\n        const hashSize = ShaCrypto.size(algorithm) >> 3;\r\n        const mask = new Uint8Array(length);\r\n        const counter = new Uint8Array(4);\r\n        const chunks = Math.ceil(length / hashSize);\r\n        for (let i = 0; i < chunks; i++) {\r\n            counter[0] = i >>> 24;\r\n            counter[1] = (i >>> 16) & 255;\r\n            counter[2] = (i >>> 8) & 255;\r\n            counter[3] = i & 255;\r\n            const submask = mask.subarray(i * hashSize);\r\n            let chunk = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(algorithm.name.replace(\"-\", \"\"))\r\n                .update(seed)\r\n                .update(counter)\r\n                .digest();\r\n            if (chunk.length > submask.length) {\r\n                chunk = chunk.subarray(0, submask.length);\r\n            }\r\n            submask.set(chunk);\r\n        }\r\n        return mask;\r\n    }\r\n}\n\nclass RsaEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"RSAES-PKCS1-v1_5\";\r\n        this.usages = {\r\n            publicKey: [\"encrypt\", \"wrapKey\"],\r\n            privateKey: [\"decrypt\", \"unwrapKey\"],\r\n        };\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await RsaCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    checkGenerateKeyParams(algorithm) {\r\n        this.checkRequiredProperty(algorithm, \"publicExponent\");\r\n        if (!(algorithm.publicExponent && algorithm.publicExponent instanceof Uint8Array)) {\r\n            throw new TypeError(\"publicExponent: Missing or not a Uint8Array\");\r\n        }\r\n        const publicExponent = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64(algorithm.publicExponent);\r\n        if (!(publicExponent === \"Aw==\" || publicExponent === \"AQAB\")) {\r\n            throw new TypeError(\"publicExponent: Must be [3] or [1,0,1]\");\r\n        }\r\n        this.checkRequiredProperty(algorithm, \"modulusLength\");\r\n        switch (algorithm.modulusLength) {\r\n            case 1024:\r\n            case 2048:\r\n            case 4096:\r\n                break;\r\n            default:\r\n                throw new TypeError(\"modulusLength: Must be 1024, 2048, or 4096\");\r\n        }\r\n    }\r\n    async onEncrypt(algorithm, key, data) {\r\n        const options = this.toCryptoOptions(key);\r\n        const enc = crypto__WEBPACK_IMPORTED_MODULE_1__.publicEncrypt(options, new Uint8Array(data));\r\n        return new Uint8Array(enc).buffer;\r\n    }\r\n    async onDecrypt(algorithm, key, data) {\r\n        const options = this.toCryptoOptions(key);\r\n        const dec = crypto__WEBPACK_IMPORTED_MODULE_1__.privateDecrypt(options, new Uint8Array(data));\r\n        return new Uint8Array(dec).buffer;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return RsaCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await RsaCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof RsaPrivateKey || internalKey instanceof RsaPublicKey)) {\r\n            throw new TypeError(\"key: Is not RSA CryptoKey\");\r\n        }\r\n    }\r\n    toCryptoOptions(key) {\r\n        const type = key.type.toUpperCase();\r\n        return {\r\n            key: `-----BEGIN ${type} KEY-----\\n${getCryptoKey(key).data.toString(\"base64\")}\\n-----END ${type} KEY-----`,\r\n            padding: crypto__WEBPACK_IMPORTED_MODULE_1__.constants.RSA_PKCS1_PADDING,\r\n        };\r\n    }\r\n}\n\nconst namedOIDs = {\r\n    \"1.2.840.10045.3.1.7\": \"P-256\",\r\n    \"P-256\": \"1.2.840.10045.3.1.7\",\r\n    \"1.3.132.0.34\": \"P-384\",\r\n    \"P-384\": \"1.3.132.0.34\",\r\n    \"1.3.132.0.35\": \"P-521\",\r\n    \"P-521\": \"1.3.132.0.35\",\r\n    \"1.3.132.0.10\": \"K-256\",\r\n    \"K-256\": \"1.3.132.0.10\",\r\n    \"brainpoolP160r1\": \"1.3.36.3.3.2.8.1.1.1\",\r\n    \"1.3.36.3.3.2.8.1.1.1\": \"brainpoolP160r1\",\r\n    \"brainpoolP160t1\": \"1.3.36.3.3.2.8.1.1.2\",\r\n    \"1.3.36.3.3.2.8.1.1.2\": \"brainpoolP160t1\",\r\n    \"brainpoolP192r1\": \"1.3.36.3.3.2.8.1.1.3\",\r\n    \"1.3.36.3.3.2.8.1.1.3\": \"brainpoolP192r1\",\r\n    \"brainpoolP192t1\": \"1.3.36.3.3.2.8.1.1.4\",\r\n    \"1.3.36.3.3.2.8.1.1.4\": \"brainpoolP192t1\",\r\n    \"brainpoolP224r1\": \"1.3.36.3.3.2.8.1.1.5\",\r\n    \"1.3.36.3.3.2.8.1.1.5\": \"brainpoolP224r1\",\r\n    \"brainpoolP224t1\": \"1.3.36.3.3.2.8.1.1.6\",\r\n    \"1.3.36.3.3.2.8.1.1.6\": \"brainpoolP224t1\",\r\n    \"brainpoolP256r1\": \"1.3.36.3.3.2.8.1.1.7\",\r\n    \"1.3.36.3.3.2.8.1.1.7\": \"brainpoolP256r1\",\r\n    \"brainpoolP256t1\": \"1.3.36.3.3.2.8.1.1.8\",\r\n    \"1.3.36.3.3.2.8.1.1.8\": \"brainpoolP256t1\",\r\n    \"brainpoolP320r1\": \"1.3.36.3.3.2.8.1.1.9\",\r\n    \"1.3.36.3.3.2.8.1.1.9\": \"brainpoolP320r1\",\r\n    \"brainpoolP320t1\": \"1.3.36.3.3.2.8.1.1.10\",\r\n    \"1.3.36.3.3.2.8.1.1.10\": \"brainpoolP320t1\",\r\n    \"brainpoolP384r1\": \"1.3.36.3.3.2.8.1.1.11\",\r\n    \"1.3.36.3.3.2.8.1.1.11\": \"brainpoolP384r1\",\r\n    \"brainpoolP384t1\": \"1.3.36.3.3.2.8.1.1.12\",\r\n    \"1.3.36.3.3.2.8.1.1.12\": \"brainpoolP384t1\",\r\n    \"brainpoolP512r1\": \"1.3.36.3.3.2.8.1.1.13\",\r\n    \"1.3.36.3.3.2.8.1.1.13\": \"brainpoolP512r1\",\r\n    \"brainpoolP512t1\": \"1.3.36.3.3.2.8.1.1.14\",\r\n    \"1.3.36.3.3.2.8.1.1.14\": \"brainpoolP512t1\",\r\n};\r\nfunction getOidByNamedCurve$1(namedCurve) {\r\n    const oid = namedOIDs[namedCurve];\r\n    if (!oid) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\r\n    }\r\n    return oid;\r\n}\n\nclass EcPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"EC\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EcPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"EC\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(json.crv)));\r\n        keyInfo.publicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.toASN(key).valueHex;\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass Sha1Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-1\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-256\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-384\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA-512\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-256\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3384Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-384\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Sha3512Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.ProviderCrypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.name = \"SHA3-512\";\r\n        this.usages = [];\r\n    }\r\n    async onDigest(algorithm, data) {\r\n        return ShaCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass EcCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new EcPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new EcPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(\"ec\", {\r\n            namedCurve: this.getOpenSSLNamedCurve(algorithm.namedCurve),\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createSign(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const signature = signer.sign(options);\r\n        const ecSignature = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(signature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature);\r\n        const signatureRaw = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.encodeSignature(ecSignature, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve).size);\r\n        return signatureRaw.buffer;\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(algorithm.hash);\r\n        const signer = crypto__WEBPACK_IMPORTED_MODULE_1___default().createVerify(cryptoAlg);\r\n        signer.update(Buffer.from(data));\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const ecSignature = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcDsaSignature();\r\n        const namedCurve = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.get(key.algorithm.namedCurve);\r\n        const signaturePoint = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcUtils.decodeSignature(signature, namedCurve.size);\r\n        ecSignature.r = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.BufferSourceConverter.toArrayBuffer(signaturePoint.r);\r\n        ecSignature.s = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.BufferSourceConverter.toArrayBuffer(signaturePoint.s);\r\n        const ecSignatureRaw = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(ecSignature));\r\n        const ok = signer.verify(options, ecSignatureRaw);\r\n        return ok;\r\n    }\r\n    static async deriveBits(algorithm, baseKey, length) {\r\n        const cryptoAlg = this.getOpenSSLNamedCurve(baseKey.algorithm.namedCurve);\r\n        const ecdh = crypto__WEBPACK_IMPORTED_MODULE_1___default().createECDH(cryptoAlg);\r\n        const asnPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(baseKey.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        const asnEcPrivateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(asnPrivateKey.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n        ecdh.setPrivateKey(Buffer.from(asnEcPrivateKey.privateKey));\r\n        const asnPublicKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(algorithm.public.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        const bits = ecdh.computeSecret(Buffer.from(asnPublicKey.publicKey));\r\n        if (length === null) {\r\n            return bits;\r\n        }\r\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            case \"raw\": {\r\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return publicKeyInfo.publicKey;\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey });\r\n                    return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"raw\": {\r\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyData);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                const asnKey = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPublicKey(keyInfo.publicKey);\r\n                this.assertKeyParameters(keyInfo.publicKeyAlgorithm.parameters, algorithm.namedCurve);\r\n                return this.importPublicKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.EcPrivateKey);\r\n                this.assertKeyParameters(keyInfo.privateKeyAlgorithm.parameters, algorithm.namedCurve);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static assertKeyParameters(parameters, namedCurve) {\r\n        if (!parameters) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info doesn't have required parameters\");\r\n        }\r\n        let namedCurveIdentifier = \"\";\r\n        try {\r\n            namedCurveIdentifier = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(parameters, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier).value;\r\n        }\r\n        catch (e) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Cannot read key info parameters\");\r\n        }\r\n        if (getOidByNamedCurve$1(namedCurve) !== namedCurveIdentifier) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.CryptoError(\"Key info parameter doesn't match to named curve\");\r\n        }\r\n    }\r\n    static async importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        keyInfo.privateKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(getOidByNamedCurve$1(algorithm.namedCurve)));\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(asnKey);\r\n        const key = new EcPrivateKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = \"1.2.840.10045.2.1\";\r\n        const namedCurve = getOidByNamedCurve$1(algorithm.namedCurve);\r\n        keyInfo.publicKeyAlgorithm.parameters = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.ObjectIdentifier(namedCurve));\r\n        keyInfo.publicKey = asnKey.value;\r\n        const key = new EcPublicKey();\r\n        key.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static getOpenSSLNamedCurve(curve) {\r\n        switch (curve.toUpperCase()) {\r\n            case \"P-256\":\r\n                return \"prime256v1\";\r\n            case \"K-256\":\r\n                return \"secp256k1\";\r\n            case \"P-384\":\r\n                return \"secp384r1\";\r\n            case \"P-521\":\r\n                return \"secp521r1\";\r\n            default:\r\n                return curve;\r\n        }\r\n    }\r\n}\r\nEcCrypto.publicKeyUsages = [\"verify\"];\r\nEcCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EcdsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdsaProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\r\n        this.hashAlgorithms = [\r\n            \"SHA-1\", \"SHA-256\", \"SHA-384\", \"SHA-512\",\r\n            \"shake128\", \"shake256\",\r\n            \"SHA3-256\", \"SHA3-384\", \"SHA3-512\"\r\n        ];\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EcCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return EcCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return EcCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EcCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\r\n            throw new TypeError(\"key: Is not EC CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass EcdhProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhProvider {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.namedCurves = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcCurves.names;\r\n    }\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EcCrypto.generateKey({\r\n            ...algorithm,\r\n            name: this.name,\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EcCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EcCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        const internalKey = getCryptoKey(key);\r\n        if (!(internalKey instanceof EcPrivateKey || internalKey instanceof EcPublicKey)) {\r\n            throw new TypeError(\"key: Is not EC CryptoKey\");\r\n        }\r\n    }\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        const bits = await EcCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\r\n        return bits;\r\n    }\r\n}\n\nconst edOIDs = {\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448]: \"Ed448\",\r\n    \"ed448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd448,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448]: \"X448\",\r\n    \"x448\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX448,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519]: \"Ed25519\",\r\n    \"ed25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idEd25519,\r\n    [webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519]: \"X25519\",\r\n    \"x25519\": webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.idX25519,\r\n};\r\nfunction getOidByNamedCurve(namedCurve) {\r\n    const oid = edOIDs[namedCurve.toLowerCase()];\r\n    if (!oid) {\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot convert WebCrypto named curve '${namedCurve}' to OID`);\r\n    }\r\n    return oid;\r\n}\n\nclass EdPrivateKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"private\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n        return _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"OKP\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key));\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo();\r\n        keyInfo.privateKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\r\n        const key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(json, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\r\n        keyInfo.privateKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(key);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EdPublicKey extends AsymmetricKey {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.type = \"public\";\r\n    }\r\n    getKey() {\r\n        const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(this.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n        return keyInfo.publicKey;\r\n    }\r\n    toJSON() {\r\n        const key = this.getKey();\r\n        const json = {\r\n            kty: \"OKP\",\r\n            crv: this.algorithm.namedCurve,\r\n            key_ops: this.usages,\r\n            ext: this.extractable,\r\n        };\r\n        return Object.assign(json, {\r\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(key)\r\n        });\r\n    }\r\n    fromJSON(json) {\r\n        if (!json.crv) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get named curve from JWK. Property 'crv' is required`);\r\n        }\r\n        if (!json.x) {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(`Cannot get property from JWK. Property 'x' is required`);\r\n        }\r\n        const keyInfo = new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo();\r\n        keyInfo.publicKeyAlgorithm.algorithm = getOidByNamedCurve(json.crv);\r\n        keyInfo.publicKey = pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(json.x);\r\n        this.data = Buffer.from(_peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnSerializer.serialize(keyInfo));\r\n        return this;\r\n    }\r\n}\n\nclass EdCrypto {\r\n    static async generateKey(algorithm, extractable, keyUsages) {\r\n        const privateKey = new EdPrivateKey();\r\n        privateKey.algorithm = algorithm;\r\n        privateKey.extractable = extractable;\r\n        privateKey.usages = keyUsages.filter((usage) => this.privateKeyUsages.indexOf(usage) !== -1);\r\n        const publicKey = new EdPublicKey();\r\n        publicKey.algorithm = algorithm;\r\n        publicKey.extractable = true;\r\n        publicKey.usages = keyUsages.filter((usage) => this.publicKeyUsages.indexOf(usage) !== -1);\r\n        const type = algorithm.namedCurve.toLowerCase();\r\n        const keys = crypto__WEBPACK_IMPORTED_MODULE_1___default().generateKeyPairSync(type, {\r\n            publicKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"spki\",\r\n            },\r\n            privateKeyEncoding: {\r\n                format: \"der\",\r\n                type: \"pkcs8\",\r\n            },\r\n        });\r\n        privateKey.data = keys.privateKey;\r\n        publicKey.data = keys.publicKey;\r\n        const res = {\r\n            privateKey,\r\n            publicKey,\r\n        };\r\n        return res;\r\n    }\r\n    static async sign(algorithm, key, data) {\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PRIVATE KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PRIVATE KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const signature = crypto__WEBPACK_IMPORTED_MODULE_1___default().sign(null, Buffer.from(data), options);\r\n        return webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(signature);\r\n    }\r\n    static async verify(algorithm, key, signature, data) {\r\n        if (!key.pem) {\r\n            key.pem = `-----BEGIN PUBLIC KEY-----\\n${key.data.toString(\"base64\")}\\n-----END PUBLIC KEY-----`;\r\n        }\r\n        const options = {\r\n            key: key.pem,\r\n        };\r\n        const ok = crypto__WEBPACK_IMPORTED_MODULE_1___default().verify(null, Buffer.from(data), options, Buffer.from(signature));\r\n        return ok;\r\n    }\r\n    static async deriveBits(algorithm, baseKey, length) {\r\n        const publicKey = crypto__WEBPACK_IMPORTED_MODULE_1___default().createPublicKey({\r\n            key: algorithm.public.data,\r\n            format: \"der\",\r\n            type: \"spki\",\r\n        });\r\n        const privateKey = crypto__WEBPACK_IMPORTED_MODULE_1___default().createPrivateKey({\r\n            key: baseKey.data,\r\n            format: \"der\",\r\n            type: \"pkcs8\",\r\n        });\r\n        const bits = crypto__WEBPACK_IMPORTED_MODULE_1___default().diffieHellman({\r\n            publicKey,\r\n            privateKey,\r\n        });\r\n        return new Uint8Array(bits).buffer.slice(0, length >> 3);\r\n    }\r\n    static async exportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(key);\r\n            case \"pkcs8\":\r\n            case \"spki\":\r\n                return new Uint8Array(key.data).buffer;\r\n            case \"raw\": {\r\n                const publicKeyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(key.data, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return publicKeyInfo.publicKey;\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static async importKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\": {\r\n                const jwk = keyData;\r\n                if (jwk.d) {\r\n                    const asnKey = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey });\r\n                    return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n                }\r\n                else {\r\n                    if (!jwk.x) {\r\n                        throw new TypeError(\"keyData: Cannot get required 'x' filed\");\r\n                    }\r\n                    return this.importPublicKey(pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.FromBase64Url(jwk.x), algorithm, extractable, keyUsages);\r\n                }\r\n            }\r\n            case \"raw\": {\r\n                return this.importPublicKey(keyData, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"spki\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PublicKeyInfo);\r\n                return this.importPublicKey(keyInfo.publicKey, algorithm, extractable, keyUsages);\r\n            }\r\n            case \"pkcs8\": {\r\n                const keyInfo = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(new Uint8Array(keyData), webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.PrivateKeyInfo);\r\n                const asnKey = _peculiar_asn1_schema__WEBPACK_IMPORTED_MODULE_5__.AsnParser.parse(keyInfo.privateKey, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.asn1.CurvePrivateKey);\r\n                return this.importPrivateKey(asnKey, algorithm, extractable, keyUsages);\r\n            }\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk', 'raw', 'pkcs8' or 'spki'\");\r\n        }\r\n    }\r\n    static importPrivateKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const key = new EdPrivateKey();\r\n        key.fromJSON({\r\n            crv: algorithm.namedCurve,\r\n            d: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(asnKey.d),\r\n        });\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n    static async importPublicKey(asnKey, algorithm, extractable, keyUsages) {\r\n        const key = new EdPublicKey();\r\n        key.fromJSON({\r\n            crv: algorithm.namedCurve,\r\n            x: pvtsutils__WEBPACK_IMPORTED_MODULE_4__.Convert.ToBase64Url(asnKey),\r\n        });\r\n        key.algorithm = Object.assign({}, algorithm);\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return key;\r\n    }\r\n}\r\nEdCrypto.publicKeyUsages = [\"verify\"];\r\nEdCrypto.privateKeyUsages = [\"sign\", \"deriveKey\", \"deriveBits\"];\n\nclass EdDsaProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EdDsaProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EdCrypto.generateKey({\r\n            name: this.name,\r\n            namedCurve: algorithm.namedCurve.replace(/^ed/i, \"Ed\"),\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        return EdCrypto.sign(algorithm, getCryptoKey(key), new Uint8Array(data));\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        return EdCrypto.verify(algorithm, getCryptoKey(key), new Uint8Array(signature), new Uint8Array(data));\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EdCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n}\n\nclass EcdhEsProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.EcdhEsProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const keys = await EdCrypto.generateKey({\r\n            name: this.name,\r\n            namedCurve: algorithm.namedCurve.toUpperCase(),\r\n        }, extractable, keyUsages);\r\n        return {\r\n            privateKey: setCryptoKey(keys.privateKey),\r\n            publicKey: setCryptoKey(keys.publicKey),\r\n        };\r\n    }\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        const bits = await EdCrypto.deriveBits({ ...algorithm, public: getCryptoKey(algorithm.public) }, getCryptoKey(baseKey), length);\r\n        return bits;\r\n    }\r\n    async onExportKey(format, key) {\r\n        return EdCrypto.exportKey(format, getCryptoKey(key));\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        const key = await EdCrypto.importKey(format, keyData, { ...algorithm, name: this.name }, extractable, keyUsages);\r\n        return setCryptoKey(key);\r\n    }\r\n}\n\nclass PbkdfCryptoKey extends CryptoKey {\r\n}\n\nclass Pbkdf2Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Pbkdf2Provider {\r\n    async onDeriveBits(algorithm, baseKey, length) {\r\n        return new Promise((resolve, reject) => {\r\n            const salt = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toArrayBuffer(algorithm.salt);\r\n            const hash = algorithm.hash.name.replace(\"-\", \"\");\r\n            crypto__WEBPACK_IMPORTED_MODULE_1___default().pbkdf2(getCryptoKey(baseKey).data, Buffer.from(salt), algorithm.iterations, length >> 3, hash, (err, derivedBits) => {\r\n                if (err) {\r\n                    reject(err);\r\n                }\r\n                else {\r\n                    resolve(new Uint8Array(derivedBits).buffer);\r\n                }\r\n            });\r\n        });\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        if (format === \"raw\") {\r\n            const key = new PbkdfCryptoKey();\r\n            key.data = Buffer.from(keyData);\r\n            key.algorithm = { name: this.name };\r\n            key.extractable = false;\r\n            key.usages = keyUsages;\r\n            return setCryptoKey(key);\r\n        }\r\n        throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'raw'\");\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof PbkdfCryptoKey)) {\r\n            throw new TypeError(\"key: Is not PBKDF CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass HmacCryptoKey extends CryptoKey {\r\n    get alg() {\r\n        const hash = this.algorithm.hash.name.toUpperCase();\r\n        return `HS${hash.replace(\"SHA-\", \"\")}`;\r\n    }\r\n    set alg(value) {\r\n    }\r\n}\r\n(0,tslib__WEBPACK_IMPORTED_MODULE_6__.__decorate)([\r\n    (0,_peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonProp)({ name: \"k\", converter: JsonBase64UrlConverter })\r\n], HmacCryptoKey.prototype, \"data\", void 0);\n\nclass HmacProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HmacProvider {\r\n    async onGenerateKey(algorithm, extractable, keyUsages) {\r\n        const length = (algorithm.length || this.getDefaultLength(algorithm.hash.name)) >> 3 << 3;\r\n        const key = new HmacCryptoKey();\r\n        key.algorithm = {\r\n            ...algorithm,\r\n            length,\r\n            name: this.name,\r\n        };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        key.data = crypto__WEBPACK_IMPORTED_MODULE_1___default().randomBytes(length >> 3);\r\n        return setCryptoKey(key);\r\n    }\r\n    async onSign(algorithm, key, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\r\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(cryptoAlg, getCryptoKey(key).data)\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hmac).buffer;\r\n    }\r\n    async onVerify(algorithm, key, signature, data) {\r\n        const cryptoAlg = ShaCrypto.getAlgorithmName(key.algorithm.hash);\r\n        const hmac = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(cryptoAlg, getCryptoKey(key).data)\r\n            .update(Buffer.from(data)).digest();\r\n        return hmac.compare(Buffer.from(signature)) === 0;\r\n    }\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        let key;\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                key = _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonParser.fromJSON(keyData, { targetSchema: HmacCryptoKey });\r\n                break;\r\n            case \"raw\":\r\n                key = new HmacCryptoKey();\r\n                key.data = Buffer.from(keyData);\r\n                break;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n        key.algorithm = {\r\n            hash: { name: algorithm.hash.name },\r\n            name: this.name,\r\n            length: key.data.length << 3,\r\n        };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return setCryptoKey(key);\r\n    }\r\n    async onExportKey(format, key) {\r\n        switch (format.toLowerCase()) {\r\n            case \"jwk\":\r\n                return _peculiar_json_schema__WEBPACK_IMPORTED_MODULE_3__.JsonSerializer.toJSON(getCryptoKey(key));\r\n            case \"raw\":\r\n                return new Uint8Array(getCryptoKey(key).data).buffer;\r\n            default:\r\n                throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"format: Must be 'jwk' or 'raw'\");\r\n        }\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof HmacCryptoKey)) {\r\n            throw new TypeError(\"key: Is not HMAC CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass HkdfCryptoKey extends CryptoKey {\r\n}\n\nclass HkdfProvider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.HkdfProvider {\r\n    async onImportKey(format, keyData, algorithm, extractable, keyUsages) {\r\n        if (format.toLowerCase() !== \"raw\") {\r\n            throw new webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.OperationError(\"Operation not supported\");\r\n        }\r\n        const key = new HkdfCryptoKey();\r\n        key.data = Buffer.from(keyData);\r\n        key.algorithm = { name: this.name };\r\n        key.extractable = extractable;\r\n        key.usages = keyUsages;\r\n        return setCryptoKey(key);\r\n    }\r\n    async onDeriveBits(params, baseKey, length) {\r\n        const hash = params.hash.name.replace(\"-\", \"\");\r\n        const hashLength = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(hash).digest().length;\r\n        const byteLength = length / 8;\r\n        const info = webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.info);\r\n        const PRK = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(hash, webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.salt))\r\n            .update(webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(getCryptoKey(baseKey).data))\r\n            .digest();\r\n        const blocks = [Buffer.alloc(0)];\r\n        const blockCount = Math.ceil(byteLength / hashLength) + 1;\r\n        for (let i = 1; i < blockCount; ++i) {\r\n            blocks.push(crypto__WEBPACK_IMPORTED_MODULE_1___default().createHmac(hash, PRK)\r\n                .update(Buffer.concat([blocks[i - 1], info, Buffer.from([i])]))\r\n                .digest());\r\n        }\r\n        return Buffer.concat(blocks).slice(0, byteLength);\r\n    }\r\n    checkCryptoKey(key, keyUsage) {\r\n        super.checkCryptoKey(key, keyUsage);\r\n        if (!(getCryptoKey(key) instanceof HkdfCryptoKey)) {\r\n            throw new TypeError(\"key: Is not HKDF CryptoKey\");\r\n        }\r\n    }\r\n}\n\nclass ShakeCrypto {\r\n    static digest(algorithm, data) {\r\n        const hash = crypto__WEBPACK_IMPORTED_MODULE_1___default().createHash(algorithm.name.toLowerCase(), { outputLength: algorithm.length })\r\n            .update(Buffer.from(data)).digest();\r\n        return new Uint8Array(hash).buffer;\r\n    }\r\n}\n\nclass Shake128Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake128Provider {\r\n    async onDigest(algorithm, data) {\r\n        return ShakeCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass Shake256Provider extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Shake256Provider {\r\n    async onDigest(algorithm, data) {\r\n        return ShakeCrypto.digest(algorithm, data);\r\n    }\r\n}\n\nclass SubtleCrypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.SubtleCrypto {\r\n    constructor() {\r\n        var _a;\r\n        super();\r\n        this.providers.set(new AesCbcProvider());\r\n        this.providers.set(new AesCtrProvider());\r\n        this.providers.set(new AesGcmProvider());\r\n        this.providers.set(new AesCmacProvider());\r\n        this.providers.set(new AesKwProvider());\r\n        this.providers.set(new AesEcbProvider());\r\n        this.providers.set(new DesCbcProvider());\r\n        this.providers.set(new DesEde3CbcProvider());\r\n        this.providers.set(new RsaSsaProvider());\r\n        this.providers.set(new RsaPssProvider());\r\n        this.providers.set(new RsaOaepProvider());\r\n        this.providers.set(new RsaEsProvider());\r\n        this.providers.set(new EcdsaProvider());\r\n        this.providers.set(new EcdhProvider());\r\n        this.providers.set(new Sha1Provider());\r\n        this.providers.set(new Sha256Provider());\r\n        this.providers.set(new Sha384Provider());\r\n        this.providers.set(new Sha512Provider());\r\n        this.providers.set(new Pbkdf2Provider());\r\n        this.providers.set(new HmacProvider());\r\n        this.providers.set(new HkdfProvider());\r\n        const nodeMajorVersion = (_a = /^v(\\d+)/.exec(process__WEBPACK_IMPORTED_MODULE_2__.version)) === null || _a === void 0 ? void 0 : _a[1];\r\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 12) {\r\n            this.providers.set(new Shake128Provider());\r\n            this.providers.set(new Shake256Provider());\r\n        }\r\n        const hashes = crypto__WEBPACK_IMPORTED_MODULE_1__.getHashes();\r\n        if (hashes.includes(\"sha3-256\")) {\r\n            this.providers.set(new Sha3256Provider());\r\n        }\r\n        if (hashes.includes(\"sha3-384\")) {\r\n            this.providers.set(new Sha3384Provider());\r\n        }\r\n        if (hashes.includes(\"sha3-512\")) {\r\n            this.providers.set(new Sha3512Provider());\r\n        }\r\n        if (nodeMajorVersion && parseInt(nodeMajorVersion, 10) >= 14) {\r\n            this.providers.set(new EdDsaProvider());\r\n            this.providers.set(new EcdhEsProvider());\r\n        }\r\n    }\r\n}\n\nclass Crypto extends webcrypto_core__WEBPACK_IMPORTED_MODULE_0__.Crypto {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.subtle = new SubtleCrypto();\r\n    }\r\n    getRandomValues(array) {\r\n        if (!ArrayBuffer.isView(array)) {\r\n            throw new TypeError(\"Failed to execute 'getRandomValues' on 'Crypto': parameter 1 is not of type 'ArrayBufferView'\");\r\n        }\r\n        const buffer = Buffer.from(array.buffer, array.byteOffset, array.byteLength);\r\n        crypto__WEBPACK_IMPORTED_MODULE_1___default().randomFillSync(buffer);\r\n        return array;\r\n    }\r\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL0BwZWN1bGlhci93ZWJjcnlwdG8vYnVpbGQvd2ViY3J5cHRvLmVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBOztBQUV1QztBQUMyQztBQUN2QztBQUNWO0FBQ0k7QUFDRjtBQUNBO0FBQ3lEO0FBQ2pDO0FBQ007O0FBRWpFO0FBQ0EscUNBQXFDLDhDQUFPO0FBQzVDLHVCQUF1Qiw4Q0FBTztBQUM5Qjs7QUFFQSx3QkFBd0IscURBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQVU7QUFDVixJQUFJLCtEQUFRLEdBQUcsbUJBQW1CLGdFQUFhLDBCQUEwQjtBQUN6RTtBQUNBLGlEQUFVO0FBQ1YsSUFBSSwrREFBUSxHQUFHLHVCQUF1QixnRUFBYSx5Q0FBeUM7QUFDNUY7QUFDQSxpREFBVTtBQUNWLElBQUksK0RBQVEsR0FBRyxNQUFNLGdFQUFhLFNBQVM7QUFDM0M7QUFDQSxpREFBVTtBQUNWLElBQUksK0RBQVEsR0FBRyxNQUFNLGdFQUFhLHlCQUF5QjtBQUMzRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNCQUFzQjtBQUNqRDtBQUNBLDJCQUEyQixzQkFBc0I7QUFDakQ7QUFDQSwyQkFBMkIsc0JBQXNCO0FBQ2pEO0FBQ0EsMkJBQTJCLHNCQUFzQjtBQUNqRDtBQUNBLDJCQUEyQixzQkFBc0I7QUFDakQ7QUFDQSwyQkFBMkIsc0JBQXNCO0FBQ2pEO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQVU7QUFDVixJQUFJLCtEQUFRLEdBQUcsOENBQThDO0FBQzdEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDBEQUFtQjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixxREFBYztBQUM5QjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIseURBQTJCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsaUVBQWM7QUFDckM7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQVUscUJBQXFCLDRCQUE0QjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLHVCQUF1Qiw0REFBOEIsUUFBUSxxQkFBcUI7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLDhEQUFnQyxRQUFRLHFCQUFxQjtBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDREQUE4QixRQUFRLHFCQUFxQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOERBQWdDLFFBQVEscUJBQXFCO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsNERBQThCLFFBQVEscUJBQXFCO0FBQ2xGO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw4REFBZ0MsUUFBUSxxQkFBcUI7QUFDdEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDREQUE4QixVQUFVLHFCQUFxQjtBQUNwRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLDhEQUFnQyxVQUFVLHFCQUFxQjtBQUN4RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDREQUE4QixRQUFRLHFCQUFxQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOERBQWdDLFFBQVEscUJBQXFCO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkIsMERBQW1CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxzQkFBc0I7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixjQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0JBQWdCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa0RBQXFCLE9BQU8sZ0JBQWdCO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix3QkFBd0I7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLDJEQUFvQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLHNCQUFzQjtBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsc0JBQXNCO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkIsMERBQW1CO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxzQkFBc0I7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDRCQUE0Qix5REFBa0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLHNCQUFzQjtBQUN2RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsc0JBQXNCO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQVU7QUFDVixJQUFJLCtEQUFRLEdBQUcsOENBQThDO0FBQzdEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQix5REFBMkI7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixpRUFBYztBQUNyQztBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBVSxxQkFBcUIsNEJBQTRCO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwREFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwREFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDREQUE4QjtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOERBQWdDO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsNERBQThCO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5Qiw4REFBZ0M7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw2QkFBNkIsdURBQWdCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSwyQ0FBMkM7QUFDNUc7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGlDQUFpQyx1REFBZ0I7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLDJDQUEyQztBQUM1RztBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEIscUJBQXFCLE9BQU8sT0FBTztBQUNqRTtBQUNBO0FBQ0Esd0JBQXdCLHNDQUFzQztBQUM5RDtBQUNBLHdCQUF3QixzQ0FBc0M7QUFDOUQ7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFtQjtBQUN6QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBUyxrQkFBa0IsZ0RBQVM7QUFDNUQsZUFBZSw0REFBUywyQkFBMkIsZ0RBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGlFQUFjO0FBQ2pEO0FBQ0E7QUFDQSxvQkFBb0IsNkRBQVUsa0JBQWtCLGNBQWMsZ0RBQVMsZ0JBQWdCO0FBQ3ZGLDRCQUE0QixnREFBUztBQUNyQztBQUNBO0FBQ0EsNkJBQTZCLGdFQUFhO0FBQzFDLGdDQUFnQyxnRUFBYTtBQUM3QztBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBUyxrQkFBa0IsZ0RBQVM7QUFDNUQsZUFBZSw0REFBUywwQkFBMEIsZ0RBQVM7QUFDM0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGlFQUFjO0FBQ2pEO0FBQ0E7QUFDQSxvQkFBb0IsNkRBQVUsa0JBQWtCLGNBQWMsZ0RBQVMsZUFBZTtBQUN0Riw0QkFBNEIsZ0RBQVM7QUFDckM7QUFDQTtBQUNBLDRCQUE0QixnRUFBYTtBQUN6QyxnQ0FBZ0MsZ0VBQWE7QUFDN0M7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGlFQUFtQztBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixpRUFBYztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwREFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsNkRBQVUscUJBQXFCLGNBQWMsZ0RBQVMsZ0JBQWdCO0FBQ3pHO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2REFBVSxxQkFBcUIsY0FBYyxnREFBUyxlQUFlO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDREQUFTLGdDQUFnQyxnREFBUztBQUNsRiwrQkFBK0IsNERBQVMsMEJBQTBCLGdEQUFTO0FBQzNFO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw0REFBUyxnQ0FBZ0MsZ0RBQVM7QUFDbEYsK0JBQStCLDREQUFTLDJCQUEyQixnREFBUztBQUM1RTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwREFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdEQUFTO0FBQ3JDO0FBQ0E7QUFDQSw2QkFBNkIsZ0VBQWE7QUFDMUM7QUFDQSwrQkFBK0IsZ0VBQWE7QUFDNUMsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGdEQUFTO0FBQ3JDO0FBQ0E7QUFDQSw0QkFBNEIsZ0VBQWE7QUFDekM7QUFDQSwrQkFBK0IsZ0VBQWE7QUFDNUMsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLHdEQUEwQjtBQUNqRDtBQUNBO0FBQ0Esc0RBQXNELDRCQUE0QjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHlEQUF5QjtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwwREFBNEI7QUFDbkQ7QUFDQTtBQUNBLHFEQUFxRCw0QkFBNEI7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qix5REFBeUI7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsNEJBQTRCO0FBQzVFLHFCQUFxQix5REFBeUI7QUFDOUM7QUFDQTtBQUNBLDhCQUE4QiwyREFBNkI7QUFDM0Q7QUFDQTtBQUNBO0FBQ0EsaURBQWlELDRCQUE0QjtBQUM3RSxxQkFBcUIseURBQXlCO0FBQzlDO0FBQ0E7QUFDQSw4QkFBOEIsNERBQThCO0FBQzVEO0FBQ0E7QUFDQTtBQUNBOztBQUVBLDZCQUE2QiwwREFBbUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSwrQkFBK0I7QUFDaEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLCtCQUErQjtBQUNoRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix3REFBMEI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOEJBQThCLDJEQUFvQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQix3REFBMEI7QUFDcEQsb0JBQW9CLGlFQUEwQjtBQUM5QztBQUNBO0FBQ0E7QUFDQSxRQUFRLDREQUE4QjtBQUN0QztBQUNBLHdCQUF3QixzQkFBc0I7QUFDOUM7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlCQUFpQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSw2REFBNkQsb0NBQW9DO0FBQ2pHO0FBQ0Esc0JBQXNCLDJEQUE2QjtBQUNuRDtBQUNBLHFCQUFxQix5REFBeUI7QUFDOUMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4REFBOEQsb0NBQW9DO0FBQ2xHO0FBQ0Esb0JBQW9CLDREQUE4QjtBQUNsRDtBQUNBLHFCQUFxQix5REFBeUI7QUFDOUMsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlCQUFpQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSx3QkFBd0Isc0JBQXNCO0FBQzlDO0FBQ0E7QUFDQSwwQkFBMEIsd0RBQTBCO0FBQ3BELG9CQUFvQixpRUFBMEI7QUFDOUM7QUFDQSx3QkFBd0IsY0FBYztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSwwQkFBMEI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLCtCQUErQjtBQUNoRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLFlBQVk7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix3REFBMEI7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0QkFBNEIsMERBQW1CO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQiw4Q0FBTztBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsaURBQW9CO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLGtEQUFxQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsK0JBQStCO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQixNQUFNLFlBQVksMENBQTBDLGFBQWEsTUFBTTtBQUM5RyxxQkFBcUIsNkNBQWdCO0FBQ3JDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDBEQUFtQiwwQ0FBMEMsV0FBVztBQUMxRjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLDREQUFTLGtCQUFrQixnREFBUztBQUM1RCxlQUFlLDREQUFTLDJCQUEyQixnREFBUztBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsaUVBQWM7QUFDakQ7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLDBEQUFtQjtBQUN6QztBQUNBLDRCQUE0QixnREFBUztBQUNyQztBQUNBLGlEQUFpRCxnRUFBYSxlQUFlLGdEQUFTO0FBQ3RGLG9CQUFvQiw2REFBVSxrQkFBa0IsY0FBYyxnREFBUyxlQUFlO0FBQ3RGLDZCQUE2QixnRUFBYTtBQUMxQyxnQ0FBZ0MsZ0VBQWE7QUFDN0M7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBUyxrQkFBa0IsZ0RBQVM7QUFDNUQsbUJBQW1CLGdEQUFTO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxpRUFBYztBQUNqRDtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsMERBQW1CO0FBQ3pDO0FBQ0Esb0JBQW9CLDZEQUFVLGtCQUFrQixjQUFjLGdEQUFTLGNBQWM7QUFDckYsNEJBQTRCLGdEQUFTO0FBQ3JDO0FBQ0EsZ0RBQWdELGdFQUFhLGVBQWUsZ0RBQVM7QUFDckYsNEJBQTRCLGdFQUFhO0FBQ3pDLGdDQUFnQyxnRUFBYTtBQUM3QztBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCLDBEQUFtQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsNkJBQTZCLDBEQUFtQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOEJBQThCLDBEQUFtQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOEJBQThCLDBEQUFtQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsOEJBQThCLDBEQUFtQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsaUVBQW1DO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsd0RBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxzREFBc0QsNEJBQTRCO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsNERBQVMsa0JBQWtCLGdEQUFTO0FBQ2hFLDZCQUE2QixtREFBWSw4QkFBOEIsb0RBQWE7QUFDcEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsMERBQTRCO0FBQ25EO0FBQ0E7QUFDQSxxREFBcUQsNEJBQTRCO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGdEQUFTO0FBQ3pDLDJCQUEyQixvREFBYTtBQUN4QywrQkFBK0IsbURBQVk7QUFDM0Msd0JBQXdCLDREQUFxQjtBQUM3Qyx3QkFBd0IsNERBQXFCO0FBQzdDLDJDQUEyQyxnRUFBYTtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLHdEQUEwQjtBQUMvQyw4QkFBOEIsNERBQVMscUJBQXFCLGdEQUFTO0FBQ3JFLGdDQUFnQyw0REFBUyxpQ0FBaUMsZ0RBQVM7QUFDbkY7QUFDQSw2QkFBNkIsNERBQVMsOEJBQThCLGdEQUFTO0FBQzdFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixpRUFBYztBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyw0REFBUyxpQkFBaUIsZ0RBQVM7QUFDekU7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2REFBVSxxQkFBcUIsY0FBYyxnREFBUyxlQUFlO0FBQ3hHO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyw2REFBVSxxQkFBcUIsY0FBYyxnREFBUyxjQUFjO0FBQ3ZHO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGdEQUFTO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyw0REFBUyxnQ0FBZ0MsZ0RBQVM7QUFDbEYsbUNBQW1DLGdEQUFTO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDREQUFTLGdDQUFnQyxnREFBUztBQUNsRiwrQkFBK0IsNERBQVMsMkJBQTJCLGdEQUFTO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix1REFBZ0I7QUFDdEM7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDREQUFTLG1CQUFtQixnREFBUztBQUN4RTtBQUNBO0FBQ0Esc0JBQXNCLHVEQUFnQjtBQUN0QztBQUNBO0FBQ0Esc0JBQXNCLHVEQUFnQjtBQUN0QztBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZ0RBQVM7QUFDckM7QUFDQSxpREFBaUQsZ0VBQWEsZUFBZSxnREFBUztBQUN0Riw2QkFBNkIsZ0VBQWE7QUFDMUM7QUFDQSwrQkFBK0IsZ0VBQWE7QUFDNUMsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsZ0RBQVM7QUFDckM7QUFDQTtBQUNBLGdEQUFnRCxnRUFBYSxlQUFlLGdEQUFTO0FBQ3JGO0FBQ0E7QUFDQSwrQkFBK0IsZ0VBQWE7QUFDNUMsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0QkFBNEIseURBQWtCO0FBQzlDO0FBQ0E7QUFDQSwyQkFBMkIsb0RBQWE7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLCtCQUErQjtBQUMvRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwyQkFBMkIsd0RBQWlCO0FBQzVDO0FBQ0E7QUFDQSwyQkFBMkIsb0RBQWE7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsK0JBQStCO0FBQy9GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELHNEQUFzRDtBQUN2RztBQUNBO0FBQ0E7O0FBRUE7QUFDQSxLQUFLLGdEQUFTO0FBQ2QsYUFBYSxnREFBUztBQUN0QixLQUFLLGdEQUFTO0FBQ2QsWUFBWSxnREFBUztBQUNyQixLQUFLLGdEQUFTO0FBQ2QsZUFBZSxnREFBUztBQUN4QixLQUFLLGdEQUFTO0FBQ2QsY0FBYyxnREFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwwREFBbUIsMENBQTBDLFdBQVc7QUFDMUY7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBUyxrQkFBa0IsZ0RBQVM7QUFDNUQsZUFBZSw0REFBUywyQkFBMkIsZ0RBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLGlFQUFjO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQSw0QkFBNEIsZ0RBQVM7QUFDckM7QUFDQSxvQkFBb0IsNkRBQVUsa0JBQWtCLGNBQWMsZ0RBQVMsa0JBQWtCO0FBQ3pGLDZCQUE2QixnRUFBYTtBQUMxQyxnQ0FBZ0MsZ0VBQWE7QUFDN0M7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw0REFBUyxrQkFBa0IsZ0RBQVM7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsOENBQU87QUFDdEIsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQTtBQUNBLHNCQUFzQiwwREFBbUI7QUFDekM7QUFDQSw0QkFBNEIsZ0RBQVM7QUFDckM7QUFDQSw0QkFBNEIsOENBQU87QUFDbkMsZ0NBQWdDLGdFQUFhO0FBQzdDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLGlFQUFtQztBQUN4RDtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNEQUFzRCw0QkFBNEI7QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsa0RBQW9CO0FBQzlDLGVBQWUsaUVBQTBCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCw0QkFBNEI7QUFDakY7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsb0RBQXNCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiw2REFBK0I7QUFDekQ7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDJCQUEyQiw4REFBZ0M7QUFDM0Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULHFCQUFxQiwyREFBNkI7QUFDbEQ7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFjO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLDREQUFTLGlCQUFpQixnREFBUztBQUN6RTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDZEQUFVLHFCQUFxQixjQUFjLGdEQUFTLGtCQUFrQjtBQUMzRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsOENBQU87QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLDREQUFTLGdDQUFnQyxnREFBUztBQUNsRjtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsNERBQVMsZ0NBQWdDLGdEQUFTO0FBQ2xGLCtCQUErQiw0REFBUywyQkFBMkIsZ0RBQVM7QUFDNUU7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDBEQUFtQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDhDQUFPO0FBQ3RCLFNBQVM7QUFDVCx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsOENBQU87QUFDdEIsU0FBUztBQUNULHdDQUF3QztBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSw0QkFBNEIseURBQWtCO0FBQzlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdFQUFnRSwrQkFBK0I7QUFDL0Y7QUFDQTtBQUNBOztBQUVBLDZCQUE2QiwwREFBbUI7QUFDaEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELHNEQUFzRDtBQUN2RztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRUFBZ0UsK0JBQStCO0FBQy9GO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBLDZCQUE2QiwwREFBbUI7QUFDaEQ7QUFDQTtBQUNBLHlCQUF5QixpRUFBMEI7QUFDbkQ7QUFDQSxZQUFZLG9EQUFzQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4QkFBOEI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMERBQW1CO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHlCQUF5QjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBLGlEQUFVO0FBQ1YsSUFBSSwrREFBUSxHQUFHLDhDQUE4QztBQUM3RDs7QUFFQSwyQkFBMkIsd0RBQWlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLHlEQUEyQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix3REFBMEI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQix3REFBMEI7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNkRBQVUscUJBQXFCLDZCQUE2QjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsMERBQW1CO0FBQzdDO0FBQ0E7QUFDQSxvQkFBb0IsMkJBQTJCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFjO0FBQ3JDO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQiwwREFBbUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsMkJBQTJCLHdEQUFpQjtBQUM1QztBQUNBO0FBQ0Esc0JBQXNCLDBEQUFtQjtBQUN6QztBQUNBO0FBQ0E7QUFDQSwwQkFBMEI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHdEQUEwQjtBQUNyRDtBQUNBLHFCQUFxQixpRUFBdUI7QUFDNUMsb0JBQW9CLHdEQUEwQixPQUFPLGlFQUF1QjtBQUM1RSxvQkFBb0IsaUVBQXVCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnQkFBZ0I7QUFDeEMsd0JBQXdCLHdEQUEwQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHFCQUFxQix3REFBMEIsaUNBQWlDLGdDQUFnQztBQUNoSDtBQUNBO0FBQ0E7QUFDQTs7QUFFQSwrQkFBK0IsNERBQXFCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBOztBQUVBLCtCQUErQiw0REFBcUI7QUFDcEQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsMkJBQTJCLHdEQUFpQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsNENBQWU7QUFDckU7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsNkNBQWdCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxxQkFBcUIsa0RBQVc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSw0REFBOEI7QUFDdEM7QUFDQTtBQUNBOztBQUVrQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BiYWhpbmxpbmsvd2ViLWFkbWluLy4uLy4uL25vZGVfbW9kdWxlcy9AcGVjdWxpYXIvd2ViY3J5cHRvL2J1aWxkL3dlYmNyeXB0by5lcy5qcz9hNGVkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuIENvcHlyaWdodCAoYykgUGVjdWxpYXIgVmVudHVyZXMsIExMQ1xuKi9cblxuaW1wb3J0ICogYXMgY29yZSBmcm9tICd3ZWJjcnlwdG8tY29yZSc7XG5pbXBvcnQgeyBCdWZmZXJTb3VyY2VDb252ZXJ0ZXIgYXMgQnVmZmVyU291cmNlQ29udmVydGVyJDEgfSBmcm9tICd3ZWJjcnlwdG8tY29yZSc7XG5leHBvcnQgeyBDcnlwdG9LZXkgfSBmcm9tICd3ZWJjcnlwdG8tY29yZSc7XG5pbXBvcnQgKiBhcyBjcnlwdG8gZnJvbSAnY3J5cHRvJztcbmltcG9ydCBjcnlwdG9fX2RlZmF1bHQgZnJvbSAnY3J5cHRvJztcbmltcG9ydCAqIGFzIHByb2Nlc3MgZnJvbSAncHJvY2Vzcyc7XG5pbXBvcnQgeyBfX2RlY29yYXRlIH0gZnJvbSAndHNsaWInO1xuaW1wb3J0IHsgSnNvblByb3AsIEpzb25Qcm9wVHlwZXMsIEpzb25TZXJpYWxpemVyLCBKc29uUGFyc2VyIH0gZnJvbSAnQHBlY3VsaWFyL2pzb24tc2NoZW1hJztcbmltcG9ydCB7IENvbnZlcnQsIEJ1ZmZlclNvdXJjZUNvbnZlcnRlciB9IGZyb20gJ3B2dHN1dGlscyc7XG5pbXBvcnQgeyBBc25QYXJzZXIsIEFzblNlcmlhbGl6ZXIgfSBmcm9tICdAcGVjdWxpYXIvYXNuMS1zY2hlbWEnO1xuXG5jb25zdCBKc29uQmFzZTY0VXJsQ29udmVydGVyID0ge1xyXG4gICAgZnJvbUpTT046ICh2YWx1ZSkgPT4gQnVmZmVyLmZyb20oQ29udmVydC5Gcm9tQmFzZTY0VXJsKHZhbHVlKSksXHJcbiAgICB0b0pTT046ICh2YWx1ZSkgPT4gQ29udmVydC5Ub0Jhc2U2NFVybCh2YWx1ZSksXHJcbn07XG5cbmNsYXNzIENyeXB0b0tleSBleHRlbmRzIGNvcmUuQ3J5cHRvS2V5IHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gQnVmZmVyLmFsbG9jKDApO1xyXG4gICAgICAgIHRoaXMuYWxnb3JpdGhtID0geyBuYW1lOiBcIlwiIH07XHJcbiAgICAgICAgdGhpcy5leHRyYWN0YWJsZSA9IGZhbHNlO1xyXG4gICAgICAgIHRoaXMudHlwZSA9IFwic2VjcmV0XCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgICAgICB0aGlzLmt0eSA9IFwib2N0XCI7XHJcbiAgICAgICAgdGhpcy5hbGcgPSBcIlwiO1xyXG4gICAgfVxyXG59XHJcbl9fZGVjb3JhdGUoW1xyXG4gICAgSnNvblByb3AoeyBuYW1lOiBcImV4dFwiLCB0eXBlOiBKc29uUHJvcFR5cGVzLkJvb2xlYW4sIG9wdGlvbmFsOiB0cnVlIH0pXHJcbl0sIENyeXB0b0tleS5wcm90b3R5cGUsIFwiZXh0cmFjdGFibGVcIiwgdm9pZCAwKTtcclxuX19kZWNvcmF0ZShbXHJcbiAgICBKc29uUHJvcCh7IG5hbWU6IFwia2V5X29wc1wiLCB0eXBlOiBKc29uUHJvcFR5cGVzLlN0cmluZywgcmVwZWF0ZWQ6IHRydWUsIG9wdGlvbmFsOiB0cnVlIH0pXHJcbl0sIENyeXB0b0tleS5wcm90b3R5cGUsIFwidXNhZ2VzXCIsIHZvaWQgMCk7XHJcbl9fZGVjb3JhdGUoW1xyXG4gICAgSnNvblByb3AoeyB0eXBlOiBKc29uUHJvcFR5cGVzLlN0cmluZyB9KVxyXG5dLCBDcnlwdG9LZXkucHJvdG90eXBlLCBcImt0eVwiLCB2b2lkIDApO1xyXG5fX2RlY29yYXRlKFtcclxuICAgIEpzb25Qcm9wKHsgdHlwZTogSnNvblByb3BUeXBlcy5TdHJpbmcsIG9wdGlvbmFsOiB0cnVlIH0pXHJcbl0sIENyeXB0b0tleS5wcm90b3R5cGUsIFwiYWxnXCIsIHZvaWQgMCk7XG5cbmNsYXNzIFN5bW1ldHJpY0tleSBleHRlbmRzIENyeXB0b0tleSB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMua3R5ID0gXCJvY3RcIjtcclxuICAgICAgICB0aGlzLnR5cGUgPSBcInNlY3JldFwiO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIEFzeW1tZXRyaWNLZXkgZXh0ZW5kcyBDcnlwdG9LZXkge1xyXG59XG5cbmNsYXNzIEFlc0NyeXB0b0tleSBleHRlbmRzIFN5bW1ldHJpY0tleSB7XHJcbiAgICBnZXQgYWxnKCkge1xyXG4gICAgICAgIHN3aXRjaCAodGhpcy5hbGdvcml0aG0ubmFtZS50b1VwcGVyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgIGNhc2UgXCJBRVMtQ0JDXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gYEEke3RoaXMuYWxnb3JpdGhtLmxlbmd0aH1DQkNgO1xyXG4gICAgICAgICAgICBjYXNlIFwiQUVTLUNUUlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGBBJHt0aGlzLmFsZ29yaXRobS5sZW5ndGh9Q1RSYDtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1HQ01cIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBgQSR7dGhpcy5hbGdvcml0aG0ubGVuZ3RofUdDTWA7XHJcbiAgICAgICAgICAgIGNhc2UgXCJBRVMtS1dcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBgQSR7dGhpcy5hbGdvcml0aG0ubGVuZ3RofUtXYDtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1DTUFDXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gYEEke3RoaXMuYWxnb3JpdGhtLmxlbmd0aH1DTUFDYDtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1FQ0JcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBgQSR7dGhpcy5hbGdvcml0aG0ubGVuZ3RofUVDQmA7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5BbGdvcml0aG1FcnJvcihcIlVuc3VwcG9ydGVkIGFsZ29yaXRobSBuYW1lXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHNldCBhbGcodmFsdWUpIHtcclxuICAgIH1cclxufVxyXG5fX2RlY29yYXRlKFtcclxuICAgIEpzb25Qcm9wKHsgbmFtZTogXCJrXCIsIGNvbnZlcnRlcjogSnNvbkJhc2U2NFVybENvbnZlcnRlciB9KVxyXG5dLCBBZXNDcnlwdG9LZXkucHJvdG90eXBlLCBcImRhdGFcIiwgdm9pZCAwKTtcblxuY29uc3Qga2V5U3RvcmFnZSA9IG5ldyBXZWFrTWFwKCk7XHJcbmZ1bmN0aW9uIGdldENyeXB0b0tleShrZXkpIHtcclxuICAgIGNvbnN0IHJlcyA9IGtleVN0b3JhZ2UuZ2V0KGtleSk7XHJcbiAgICBpZiAoIXJlcykge1xyXG4gICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiQ2Fubm90IGdldCBDcnlwdG9LZXkgZnJvbSBzZWN1cmUgc3RvcmFnZVwiKTtcclxuICAgIH1cclxuICAgIHJldHVybiByZXM7XHJcbn1cclxuZnVuY3Rpb24gc2V0Q3J5cHRvS2V5KHZhbHVlKSB7XHJcbiAgICBjb25zdCBrZXkgPSBjb3JlLkNyeXB0b0tleS5jcmVhdGUodmFsdWUuYWxnb3JpdGhtLCB2YWx1ZS50eXBlLCB2YWx1ZS5leHRyYWN0YWJsZSwgdmFsdWUudXNhZ2VzKTtcclxuICAgIE9iamVjdC5mcmVlemUoa2V5KTtcclxuICAgIGtleVN0b3JhZ2Uuc2V0KGtleSwgdmFsdWUpO1xyXG4gICAgcmV0dXJuIGtleTtcclxufVxuXG5jbGFzcyBBZXNDcnlwdG8ge1xyXG4gICAgc3RhdGljIGFzeW5jIGdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IG5ldyBBZXNDcnlwdG9LZXkoKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtID0gYWxnb3JpdGhtO1xyXG4gICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIGtleS51c2FnZXMgPSBrZXlVc2FnZXM7XHJcbiAgICAgICAga2V5LmRhdGEgPSBjcnlwdG9fX2RlZmF1bHQucmFuZG9tQnl0ZXMoYWxnb3JpdGhtLmxlbmd0aCA+PiAzKTtcclxuICAgICAgICByZXR1cm4ga2V5O1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIGlmICghKGtleSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwia2V5OiBJcyBub3QgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBzd2l0Y2ggKGZvcm1hdC50b0xvd2VyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgIGNhc2UgXCJqd2tcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBKc29uU2VyaWFsaXplci50b0pTT04oa2V5KTtcclxuICAgICAgICAgICAgY2FzZSBcInJhd1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGtleS5kYXRhKS5idWZmZXI7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImZvcm1hdDogTXVzdCBiZSAnandrJyBvciAncmF3J1wiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgbGV0IGtleTtcclxuICAgICAgICBzd2l0Y2ggKGZvcm1hdC50b0xvd2VyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgIGNhc2UgXCJqd2tcIjpcclxuICAgICAgICAgICAgICAgIGtleSA9IEpzb25QYXJzZXIuZnJvbUpTT04oa2V5RGF0YSwgeyB0YXJnZXRTY2hlbWE6IEFlc0NyeXB0b0tleSB9KTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIFwicmF3XCI6XHJcbiAgICAgICAgICAgICAgICBrZXkgPSBuZXcgQWVzQ3J5cHRvS2V5KCk7XHJcbiAgICAgICAgICAgICAgICBrZXkuZGF0YSA9IEJ1ZmZlci5mcm9tKGtleURhdGEpO1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImZvcm1hdDogTXVzdCBiZSAnandrJyBvciAncmF3J1wiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAga2V5LmFsZ29yaXRobSA9IGFsZ29yaXRobTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtLmxlbmd0aCA9IGtleS5kYXRhLmxlbmd0aCA8PCAzO1xyXG4gICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIGtleS51c2FnZXMgPSBrZXlVc2FnZXM7XHJcbiAgICAgICAgc3dpdGNoIChrZXkuYWxnb3JpdGhtLmxlbmd0aCkge1xyXG4gICAgICAgICAgICBjYXNlIDEyODpcclxuICAgICAgICAgICAgY2FzZSAxOTI6XHJcbiAgICAgICAgICAgIGNhc2UgMjU2OlxyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImtleURhdGE6IElzIHdyb25nIGtleSBsZW5ndGhcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBrZXk7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZW5jcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIHN3aXRjaCAoYWxnb3JpdGhtLm5hbWUudG9VcHBlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiQUVTLUNCQ1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZW5jcnlwdEFlc0NCQyhhbGdvcml0aG0sIGtleSwgQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgICAgICBjYXNlIFwiQUVTLUNUUlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZW5jcnlwdEFlc0NUUihhbGdvcml0aG0sIGtleSwgQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgICAgICBjYXNlIFwiQUVTLUdDTVwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZW5jcnlwdEFlc0dDTShhbGdvcml0aG0sIGtleSwgQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgICAgICBjYXNlIFwiQUVTLUtXXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5lbmNyeXB0QWVzS1coYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1FQ0JcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmVuY3J5cHRBZXNFQ0IoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiYWxnb3JpdGhtOiBJcyBub3QgcmVjb2duaXplZFwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZGVjcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGlmICghKGtleSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwia2V5OiBJcyBub3QgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1DQkNcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY3J5cHRBZXNDQkMoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1DVFJcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY3J5cHRBZXNDVFIoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1HQ01cIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY3J5cHRBZXNHQ00oYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkFFUy1LV1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZGVjcnlwdEFlc0tXKGFsZ29yaXRobSwga2V5LCBCdWZmZXIuZnJvbShkYXRhKSk7XHJcbiAgICAgICAgICAgIGNhc2UgXCJBRVMtRUNCXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNyeXB0QWVzRUNCKGFsZ29yaXRobSwga2V5LCBCdWZmZXIuZnJvbShkYXRhKSk7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobTogSXMgbm90IHJlY29nbml6ZWRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGVuY3J5cHRBZXNDQkMoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBjaXBoZXIgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlQ2lwaGVyaXYoYGFlcy0ke2tleS5hbGdvcml0aG0ubGVuZ3RofS1jYmNgLCBrZXkuZGF0YSwgbmV3IFVpbnQ4QXJyYXkoYWxnb3JpdGhtLml2KSk7XHJcbiAgICAgICAgbGV0IGVuYyA9IGNpcGhlci51cGRhdGUoZGF0YSk7XHJcbiAgICAgICAgZW5jID0gQnVmZmVyLmNvbmNhdChbZW5jLCBjaXBoZXIuZmluYWwoKV0pO1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IG5ldyBVaW50OEFycmF5KGVuYykuYnVmZmVyO1xyXG4gICAgICAgIHJldHVybiByZXM7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZGVjcnlwdEFlc0NCQyhhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGRlY2lwaGVyID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZURlY2lwaGVyaXYoYGFlcy0ke2tleS5hbGdvcml0aG0ubGVuZ3RofS1jYmNgLCBrZXkuZGF0YSwgbmV3IFVpbnQ4QXJyYXkoYWxnb3JpdGhtLml2KSk7XHJcbiAgICAgICAgbGV0IGRlYyA9IGRlY2lwaGVyLnVwZGF0ZShkYXRhKTtcclxuICAgICAgICBkZWMgPSBCdWZmZXIuY29uY2F0KFtkZWMsIGRlY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGVjKS5idWZmZXI7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZW5jcnlwdEFlc0NUUihhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVDaXBoZXJpdihgYWVzLSR7a2V5LmFsZ29yaXRobS5sZW5ndGh9LWN0cmAsIGtleS5kYXRhLCBCdWZmZXIuZnJvbShhbGdvcml0aG0uY291bnRlcikpO1xyXG4gICAgICAgIGxldCBlbmMgPSBjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGVuYyA9IEJ1ZmZlci5jb25jYXQoW2VuYywgY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICBjb25zdCByZXMgPSBuZXcgVWludDhBcnJheShlbmMpLmJ1ZmZlcjtcclxuICAgICAgICByZXR1cm4gcmVzO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHRBZXNDVFIoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBkZWNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVEZWNpcGhlcml2KGBhZXMtJHtrZXkuYWxnb3JpdGhtLmxlbmd0aH0tY3RyYCwga2V5LmRhdGEsIG5ldyBVaW50OEFycmF5KGFsZ29yaXRobS5jb3VudGVyKSk7XHJcbiAgICAgICAgbGV0IGRlYyA9IGRlY2lwaGVyLnVwZGF0ZShkYXRhKTtcclxuICAgICAgICBkZWMgPSBCdWZmZXIuY29uY2F0KFtkZWMsIGRlY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGVjKS5idWZmZXI7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZW5jcnlwdEFlc0dDTShhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVDaXBoZXJpdihgYWVzLSR7a2V5LmFsZ29yaXRobS5sZW5ndGh9LWdjbWAsIGtleS5kYXRhLCBCdWZmZXIuZnJvbShhbGdvcml0aG0uaXYpLCB7XHJcbiAgICAgICAgICAgIGF1dGhUYWdMZW5ndGg6IChhbGdvcml0aG0udGFnTGVuZ3RoIHx8IDEyOCkgPj4gMyxcclxuICAgICAgICB9KTtcclxuICAgICAgICBpZiAoYWxnb3JpdGhtLmFkZGl0aW9uYWxEYXRhKSB7XHJcbiAgICAgICAgICAgIGNpcGhlci5zZXRBQUQoQnVmZmVyLmZyb20oYWxnb3JpdGhtLmFkZGl0aW9uYWxEYXRhKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBlbmMgPSBjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGVuYyA9IEJ1ZmZlci5jb25jYXQoW2VuYywgY2lwaGVyLmZpbmFsKCksIGNpcGhlci5nZXRBdXRoVGFnKCldKTtcclxuICAgICAgICBjb25zdCByZXMgPSBuZXcgVWludDhBcnJheShlbmMpLmJ1ZmZlcjtcclxuICAgICAgICByZXR1cm4gcmVzO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHRBZXNHQ00oYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBkZWNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVEZWNpcGhlcml2KGBhZXMtJHtrZXkuYWxnb3JpdGhtLmxlbmd0aH0tZ2NtYCwga2V5LmRhdGEsIG5ldyBVaW50OEFycmF5KGFsZ29yaXRobS5pdikpO1xyXG4gICAgICAgIGNvbnN0IHRhZ0xlbmd0aCA9IChhbGdvcml0aG0udGFnTGVuZ3RoIHx8IDEyOCkgPj4gMztcclxuICAgICAgICBjb25zdCBlbmMgPSBkYXRhLnNsaWNlKDAsIGRhdGEubGVuZ3RoIC0gdGFnTGVuZ3RoKTtcclxuICAgICAgICBjb25zdCB0YWcgPSBkYXRhLnNsaWNlKGRhdGEubGVuZ3RoIC0gdGFnTGVuZ3RoKTtcclxuICAgICAgICBpZiAoYWxnb3JpdGhtLmFkZGl0aW9uYWxEYXRhKSB7XHJcbiAgICAgICAgICAgIGRlY2lwaGVyLnNldEFBRChCdWZmZXIuZnJvbShhbGdvcml0aG0uYWRkaXRpb25hbERhdGEpKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgZGVjaXBoZXIuc2V0QXV0aFRhZyh0YWcpO1xyXG4gICAgICAgIGxldCBkZWMgPSBkZWNpcGhlci51cGRhdGUoZW5jKTtcclxuICAgICAgICBkZWMgPSBCdWZmZXIuY29uY2F0KFtkZWMsIGRlY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGVjKS5idWZmZXI7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZW5jcnlwdEFlc0tXKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3QgY2lwaGVyID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUNpcGhlcml2KGBpZC1hZXMke2tleS5hbGdvcml0aG0ubGVuZ3RofS13cmFwYCwga2V5LmRhdGEsIHRoaXMuQUVTX0tXX0lWKTtcclxuICAgICAgICBsZXQgZW5jID0gY2lwaGVyLnVwZGF0ZShkYXRhKTtcclxuICAgICAgICBlbmMgPSBCdWZmZXIuY29uY2F0KFtlbmMsIGNpcGhlci5maW5hbCgpXSk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGVuYykuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHRBZXNLVyhhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGRlY2lwaGVyID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZURlY2lwaGVyaXYoYGlkLWFlcyR7a2V5LmFsZ29yaXRobS5sZW5ndGh9LXdyYXBgLCBrZXkuZGF0YSwgdGhpcy5BRVNfS1dfSVYpO1xyXG4gICAgICAgIGxldCBkZWMgPSBkZWNpcGhlci51cGRhdGUoZGF0YSk7XHJcbiAgICAgICAgZGVjID0gQnVmZmVyLmNvbmNhdChbZGVjLCBkZWNpcGhlci5maW5hbCgpXSk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGRlYykuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGVuY3J5cHRBZXNFQ0IoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBjaXBoZXIgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlQ2lwaGVyaXYoYGFlcy0ke2tleS5hbGdvcml0aG0ubGVuZ3RofS1lY2JgLCBrZXkuZGF0YSwgbmV3IFVpbnQ4QXJyYXkoMCkpO1xyXG4gICAgICAgIGxldCBlbmMgPSBjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGVuYyA9IEJ1ZmZlci5jb25jYXQoW2VuYywgY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICBjb25zdCByZXMgPSBuZXcgVWludDhBcnJheShlbmMpLmJ1ZmZlcjtcclxuICAgICAgICByZXR1cm4gcmVzO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHRBZXNFQ0IoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBkZWNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVEZWNpcGhlcml2KGBhZXMtJHtrZXkuYWxnb3JpdGhtLmxlbmd0aH0tZWNiYCwga2V5LmRhdGEsIG5ldyBVaW50OEFycmF5KDApKTtcclxuICAgICAgICBsZXQgZGVjID0gZGVjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGRlYyA9IEJ1ZmZlci5jb25jYXQoW2RlYywgZGVjaXBoZXIuZmluYWwoKV0pO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShkZWMpLmJ1ZmZlcjtcclxuICAgIH1cclxufVxyXG5BZXNDcnlwdG8uQUVTX0tXX0lWID0gQnVmZmVyLmZyb20oXCJBNkE2QTZBNkE2QTZBNkE2XCIsIFwiaGV4XCIpO1xuXG5jbGFzcyBBZXNDYmNQcm92aWRlciBleHRlbmRzIGNvcmUuQWVzQ2JjUHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBBZXNDcnlwdG8uZ2VuZXJhdGVLZXkoe1xyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgICAgIGxlbmd0aDogYWxnb3JpdGhtLmxlbmd0aCxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gQWVzQ3J5cHRvLmVuY3J5cHQoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZWNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5kZWNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IGF3YWl0IEFlc0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IG5hbWU6IGFsZ29yaXRobS5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IGEgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jb25zdCB6ZXJvID0gQnVmZmVyLmZyb20oWzAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDAsIDBdKTtcclxuY29uc3QgcmIgPSBCdWZmZXIuZnJvbShbMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMCwgMTM1XSk7XHJcbmNvbnN0IGJsb2NrU2l6ZSA9IDE2O1xyXG5mdW5jdGlvbiBiaXRTaGlmdExlZnQoYnVmZmVyKSB7XHJcbiAgICBjb25zdCBzaGlmdGVkID0gQnVmZmVyLmFsbG9jKGJ1ZmZlci5sZW5ndGgpO1xyXG4gICAgY29uc3QgbGFzdCA9IGJ1ZmZlci5sZW5ndGggLSAxO1xyXG4gICAgZm9yIChsZXQgaW5kZXggPSAwOyBpbmRleCA8IGxhc3Q7IGluZGV4KyspIHtcclxuICAgICAgICBzaGlmdGVkW2luZGV4XSA9IGJ1ZmZlcltpbmRleF0gPDwgMTtcclxuICAgICAgICBpZiAoYnVmZmVyW2luZGV4ICsgMV0gJiAweDgwKSB7XHJcbiAgICAgICAgICAgIHNoaWZ0ZWRbaW5kZXhdICs9IDB4MDE7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc2hpZnRlZFtsYXN0XSA9IGJ1ZmZlcltsYXN0XSA8PCAxO1xyXG4gICAgcmV0dXJuIHNoaWZ0ZWQ7XHJcbn1cclxuZnVuY3Rpb24geG9yKGEsIGIpIHtcclxuICAgIGNvbnN0IGxlbmd0aCA9IE1hdGgubWluKGEubGVuZ3RoLCBiLmxlbmd0aCk7XHJcbiAgICBjb25zdCBvdXRwdXQgPSBCdWZmZXIuYWxsb2MobGVuZ3RoKTtcclxuICAgIGZvciAobGV0IGluZGV4ID0gMDsgaW5kZXggPCBsZW5ndGg7IGluZGV4KyspIHtcclxuICAgICAgICBvdXRwdXRbaW5kZXhdID0gYVtpbmRleF0gXiBiW2luZGV4XTtcclxuICAgIH1cclxuICAgIHJldHVybiBvdXRwdXQ7XHJcbn1cclxuZnVuY3Rpb24gYWVzKGtleSwgbWVzc2FnZSkge1xyXG4gICAgY29uc3QgY2lwaGVyID0gY3J5cHRvLmNyZWF0ZUNpcGhlcml2KGBhZXMke2tleS5sZW5ndGggPDwgM31gLCBrZXksIHplcm8pO1xyXG4gICAgY29uc3QgcmVzdWx0ID0gY2lwaGVyLnVwZGF0ZShtZXNzYWdlKTtcclxuICAgIGNpcGhlci5maW5hbCgpO1xyXG4gICAgcmV0dXJuIHJlc3VsdDtcclxufVxyXG5mdW5jdGlvbiBnZXRNZXNzYWdlQmxvY2sobWVzc2FnZSwgYmxvY2tJbmRleCkge1xyXG4gICAgY29uc3QgYmxvY2sgPSBCdWZmZXIuYWxsb2MoYmxvY2tTaXplKTtcclxuICAgIGNvbnN0IHN0YXJ0ID0gYmxvY2tJbmRleCAqIGJsb2NrU2l6ZTtcclxuICAgIGNvbnN0IGVuZCA9IHN0YXJ0ICsgYmxvY2tTaXplO1xyXG4gICAgbWVzc2FnZS5jb3B5KGJsb2NrLCAwLCBzdGFydCwgZW5kKTtcclxuICAgIHJldHVybiBibG9jaztcclxufVxyXG5mdW5jdGlvbiBnZXRQYWRkZWRNZXNzYWdlQmxvY2sobWVzc2FnZSwgYmxvY2tJbmRleCkge1xyXG4gICAgY29uc3QgYmxvY2sgPSBCdWZmZXIuYWxsb2MoYmxvY2tTaXplKTtcclxuICAgIGNvbnN0IHN0YXJ0ID0gYmxvY2tJbmRleCAqIGJsb2NrU2l6ZTtcclxuICAgIGNvbnN0IGVuZCA9IG1lc3NhZ2UubGVuZ3RoO1xyXG4gICAgYmxvY2suZmlsbCgwKTtcclxuICAgIG1lc3NhZ2UuY29weShibG9jaywgMCwgc3RhcnQsIGVuZCk7XHJcbiAgICBibG9ja1tlbmQgLSBzdGFydF0gPSAweDgwO1xyXG4gICAgcmV0dXJuIGJsb2NrO1xyXG59XHJcbmZ1bmN0aW9uIGdlbmVyYXRlU3Via2V5cyhrZXkpIHtcclxuICAgIGNvbnN0IGwgPSBhZXMoa2V5LCB6ZXJvKTtcclxuICAgIGxldCBzdWJrZXkxID0gYml0U2hpZnRMZWZ0KGwpO1xyXG4gICAgaWYgKGxbMF0gJiAweDgwKSB7XHJcbiAgICAgICAgc3Via2V5MSA9IHhvcihzdWJrZXkxLCByYik7XHJcbiAgICB9XHJcbiAgICBsZXQgc3Via2V5MiA9IGJpdFNoaWZ0TGVmdChzdWJrZXkxKTtcclxuICAgIGlmIChzdWJrZXkxWzBdICYgMHg4MCkge1xyXG4gICAgICAgIHN1YmtleTIgPSB4b3Ioc3Via2V5MiwgcmIpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHsgc3Via2V5MSwgc3Via2V5MiB9O1xyXG59XHJcbmZ1bmN0aW9uIGFlc0NtYWMoa2V5LCBtZXNzYWdlKSB7XHJcbiAgICBjb25zdCBzdWJrZXlzID0gZ2VuZXJhdGVTdWJrZXlzKGtleSk7XHJcbiAgICBsZXQgYmxvY2tDb3VudCA9IE1hdGguY2VpbChtZXNzYWdlLmxlbmd0aCAvIGJsb2NrU2l6ZSk7XHJcbiAgICBsZXQgbGFzdEJsb2NrQ29tcGxldGVGbGFnO1xyXG4gICAgbGV0IGxhc3RCbG9jaztcclxuICAgIGlmIChibG9ja0NvdW50ID09PSAwKSB7XHJcbiAgICAgICAgYmxvY2tDb3VudCA9IDE7XHJcbiAgICAgICAgbGFzdEJsb2NrQ29tcGxldGVGbGFnID0gZmFsc2U7XHJcbiAgICB9XHJcbiAgICBlbHNlIHtcclxuICAgICAgICBsYXN0QmxvY2tDb21wbGV0ZUZsYWcgPSAobWVzc2FnZS5sZW5ndGggJSBibG9ja1NpemUgPT09IDApO1xyXG4gICAgfVxyXG4gICAgY29uc3QgbGFzdEJsb2NrSW5kZXggPSBibG9ja0NvdW50IC0gMTtcclxuICAgIGlmIChsYXN0QmxvY2tDb21wbGV0ZUZsYWcpIHtcclxuICAgICAgICBsYXN0QmxvY2sgPSB4b3IoZ2V0TWVzc2FnZUJsb2NrKG1lc3NhZ2UsIGxhc3RCbG9ja0luZGV4KSwgc3Via2V5cy5zdWJrZXkxKTtcclxuICAgIH1cclxuICAgIGVsc2Uge1xyXG4gICAgICAgIGxhc3RCbG9jayA9IHhvcihnZXRQYWRkZWRNZXNzYWdlQmxvY2sobWVzc2FnZSwgbGFzdEJsb2NrSW5kZXgpLCBzdWJrZXlzLnN1YmtleTIpO1xyXG4gICAgfVxyXG4gICAgbGV0IHggPSB6ZXJvO1xyXG4gICAgbGV0IHk7XHJcbiAgICBmb3IgKGxldCBpbmRleCA9IDA7IGluZGV4IDwgbGFzdEJsb2NrSW5kZXg7IGluZGV4KyspIHtcclxuICAgICAgICB5ID0geG9yKHgsIGdldE1lc3NhZ2VCbG9jayhtZXNzYWdlLCBpbmRleCkpO1xyXG4gICAgICAgIHggPSBhZXMoa2V5LCB5KTtcclxuICAgIH1cclxuICAgIHkgPSB4b3IobGFzdEJsb2NrLCB4KTtcclxuICAgIHJldHVybiBhZXMoa2V5LCB5KTtcclxufVxyXG5jbGFzcyBBZXNDbWFjUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLkFlc0NtYWNQcm92aWRlciB7XHJcbiAgICBhc3luYyBvbkdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IGF3YWl0IEFlc0NyeXB0by5nZW5lcmF0ZUtleSh7XHJcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcclxuICAgICAgICAgICAgbGVuZ3RoOiBhbGdvcml0aG0ubGVuZ3RoLFxyXG4gICAgICAgIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uU2lnbihhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGFlc0NtYWMoZ2V0Q3J5cHRvS2V5KGtleSkuZGF0YSwgQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShyZXN1bHQpLmJ1ZmZlcjtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uVmVyaWZ5KGFsZ29yaXRobSwga2V5LCBzaWduYXR1cmUsIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBzaWduYXR1cmUyID0gYXdhaXQgdGhpcy5zaWduKGFsZ29yaXRobSwga2V5LCBkYXRhKTtcclxuICAgICAgICByZXR1cm4gQnVmZmVyLmZyb20oc2lnbmF0dXJlKS5jb21wYXJlKEJ1ZmZlci5mcm9tKHNpZ25hdHVyZTIpKSA9PT0gMDtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEFlc0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IG5hbWU6IGFsZ29yaXRobS5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkocmVzKTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IGEgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBBZXNDdHJQcm92aWRlciBleHRlbmRzIGNvcmUuQWVzQ3RyUHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBBZXNDcnlwdG8uZ2VuZXJhdGVLZXkoe1xyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgICAgIGxlbmd0aDogYWxnb3JpdGhtLmxlbmd0aCxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gQWVzQ3J5cHRvLmVuY3J5cHQoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZWNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5kZWNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEFlc0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IG5hbWU6IGFsZ29yaXRobS5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkocmVzKTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IGEgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBBZXNHY21Qcm92aWRlciBleHRlbmRzIGNvcmUuQWVzR2NtUHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBBZXNDcnlwdG8uZ2VuZXJhdGVLZXkoe1xyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgICAgIGxlbmd0aDogYWxnb3JpdGhtLmxlbmd0aCxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gQWVzQ3J5cHRvLmVuY3J5cHQoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZWNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5kZWNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IEFlc0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IG5hbWU6IGFsZ29yaXRobS5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkocmVzKTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIEFlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IGEgQWVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBBZXNLd1Byb3ZpZGVyIGV4dGVuZHMgY29yZS5BZXNLd1Byb3ZpZGVyIHtcclxuICAgIGFzeW5jIG9uR2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgQWVzQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICAgICAgICBsZW5ndGg6IGFsZ29yaXRobS5sZW5ndGgsXHJcbiAgICAgICAgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShyZXMpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gQWVzQ3J5cHRvLmV4cG9ydEtleShmb3JtYXQsIGdldENyeXB0b0tleShrZXkpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgQWVzQ3J5cHRvLmltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIHsgbmFtZTogYWxnb3JpdGhtLm5hbWUgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShyZXMpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FbmNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5lbmNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGVjcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBBZXNDcnlwdG8uZGVjcnlwdChhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBjaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKSB7XHJcbiAgICAgICAgc3VwZXIuY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSk7XHJcbiAgICAgICAgaWYgKCEoZ2V0Q3J5cHRvS2V5KGtleSkgaW5zdGFuY2VvZiBBZXNDcnlwdG9LZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBhIEFlc0NyeXB0b0tleVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgQWVzRWNiUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLkFlc0VjYlByb3ZpZGVyIHtcclxuICAgIGFzeW5jIG9uR2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gYXdhaXQgQWVzQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICAgICAgICBsZW5ndGg6IGFsZ29yaXRobS5sZW5ndGgsXHJcbiAgICAgICAgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FbmNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEFlc0NyeXB0by5lbmNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGVjcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBBZXNDcnlwdG8uZGVjcnlwdChhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIHJldHVybiBBZXNDcnlwdG8uZXhwb3J0S2V5KGZvcm1hdCwgZ2V0Q3J5cHRvS2V5KGtleSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBBZXNDcnlwdG8uaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgeyBuYW1lOiBhbGdvcml0aG0ubmFtZSB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KHJlcyk7XHJcbiAgICB9XHJcbiAgICBjaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKSB7XHJcbiAgICAgICAgc3VwZXIuY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSk7XHJcbiAgICAgICAgaWYgKCEoZ2V0Q3J5cHRvS2V5KGtleSkgaW5zdGFuY2VvZiBBZXNDcnlwdG9LZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBhIEFlc0NyeXB0b0tleVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgRGVzQ3J5cHRvS2V5IGV4dGVuZHMgU3ltbWV0cmljS2V5IHtcclxuICAgIGdldCBhbGcoKSB7XHJcbiAgICAgICAgc3dpdGNoICh0aGlzLmFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIkRFUy1DQkNcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBgREVTLUNCQ2A7XHJcbiAgICAgICAgICAgIGNhc2UgXCJERVMtRURFMy1DQkNcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBgM0RFUy1DQkNgO1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuQWxnb3JpdGhtRXJyb3IoXCJVbnN1cHBvcnRlZCBhbGdvcml0aG0gbmFtZVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzZXQgYWxnKHZhbHVlKSB7XHJcbiAgICB9XHJcbn1cclxuX19kZWNvcmF0ZShbXHJcbiAgICBKc29uUHJvcCh7IG5hbWU6IFwia1wiLCBjb252ZXJ0ZXI6IEpzb25CYXNlNjRVcmxDb252ZXJ0ZXIgfSlcclxuXSwgRGVzQ3J5cHRvS2V5LnByb3RvdHlwZSwgXCJkYXRhXCIsIHZvaWQgMCk7XG5cbmNsYXNzIERlc0NyeXB0byB7XHJcbiAgICBzdGF0aWMgYXN5bmMgZ2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gbmV3IERlc0NyeXB0b0tleSgpO1xyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSBhbGdvcml0aG07XHJcbiAgICAgICAga2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAga2V5LnVzYWdlcyA9IGtleVVzYWdlcztcclxuICAgICAgICBrZXkuZGF0YSA9IGNyeXB0b19fZGVmYXVsdC5yYW5kb21CeXRlcyhhbGdvcml0aG0ubGVuZ3RoID4+IDMpO1xyXG4gICAgICAgIHJldHVybiBrZXk7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgc3dpdGNoIChmb3JtYXQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiandrXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gSnNvblNlcmlhbGl6ZXIudG9KU09OKGtleSk7XHJcbiAgICAgICAgICAgIGNhc2UgXCJyYXdcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShrZXkuZGF0YSkuYnVmZmVyO1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJmb3JtYXQ6IE11c3QgYmUgJ2p3aycgb3IgJ3JhdydcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGxldCBrZXk7XHJcbiAgICAgICAgc3dpdGNoIChmb3JtYXQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiandrXCI6XHJcbiAgICAgICAgICAgICAgICBrZXkgPSBKc29uUGFyc2VyLmZyb21KU09OKGtleURhdGEsIHsgdGFyZ2V0U2NoZW1hOiBEZXNDcnlwdG9LZXkgfSk7XHJcbiAgICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgY2FzZSBcInJhd1wiOlxyXG4gICAgICAgICAgICAgICAga2V5ID0gbmV3IERlc0NyeXB0b0tleSgpO1xyXG4gICAgICAgICAgICAgICAga2V5LmRhdGEgPSBCdWZmZXIuZnJvbShrZXlEYXRhKTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJmb3JtYXQ6IE11c3QgYmUgJ2p3aycgb3IgJ3JhdydcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSBhbGdvcml0aG07XHJcbiAgICAgICAga2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAga2V5LnVzYWdlcyA9IGtleVVzYWdlcztcclxuICAgICAgICByZXR1cm4ga2V5O1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIkRFUy1DQkNcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmVuY3J5cHREZXNDQkMoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkRFUy1FREUzLUNCQ1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZW5jcnlwdERlc0VERTNDQkMoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiYWxnb3JpdGhtOiBJcyBub3QgcmVjb2duaXplZFwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZGVjcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGlmICghKGtleSBpbnN0YW5jZW9mIERlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwia2V5OiBJcyBub3QgRGVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIkRFUy1DQkNcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmRlY3J5cHREZXNDQkMoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgY2FzZSBcIkRFUy1FREUzLUNCQ1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuZGVjcnlwdERlc0VERTNDQkMoYWxnb3JpdGhtLCBrZXksIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiYWxnb3JpdGhtOiBJcyBub3QgcmVjb2duaXplZFwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZW5jcnlwdERlc0NCQyhhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVDaXBoZXJpdihgZGVzLWNiY2AsIGtleS5kYXRhLCBuZXcgVWludDhBcnJheShhbGdvcml0aG0uaXYpKTtcclxuICAgICAgICBsZXQgZW5jID0gY2lwaGVyLnVwZGF0ZShkYXRhKTtcclxuICAgICAgICBlbmMgPSBCdWZmZXIuY29uY2F0KFtlbmMsIGNpcGhlci5maW5hbCgpXSk7XHJcbiAgICAgICAgY29uc3QgcmVzID0gbmV3IFVpbnQ4QXJyYXkoZW5jKS5idWZmZXI7XHJcbiAgICAgICAgcmV0dXJuIHJlcztcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBkZWNyeXB0RGVzQ0JDKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3QgZGVjaXBoZXIgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlRGVjaXBoZXJpdihgZGVzLWNiY2AsIGtleS5kYXRhLCBuZXcgVWludDhBcnJheShhbGdvcml0aG0uaXYpKTtcclxuICAgICAgICBsZXQgZGVjID0gZGVjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGRlYyA9IEJ1ZmZlci5jb25jYXQoW2RlYywgZGVjaXBoZXIuZmluYWwoKV0pO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShkZWMpLmJ1ZmZlcjtcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBlbmNyeXB0RGVzRURFM0NCQyhhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNpcGhlciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVDaXBoZXJpdihgZGVzLWVkZTMtY2JjYCwga2V5LmRhdGEsIEJ1ZmZlci5mcm9tKGFsZ29yaXRobS5pdikpO1xyXG4gICAgICAgIGxldCBlbmMgPSBjaXBoZXIudXBkYXRlKGRhdGEpO1xyXG4gICAgICAgIGVuYyA9IEJ1ZmZlci5jb25jYXQoW2VuYywgY2lwaGVyLmZpbmFsKCldKTtcclxuICAgICAgICBjb25zdCByZXMgPSBuZXcgVWludDhBcnJheShlbmMpLmJ1ZmZlcjtcclxuICAgICAgICByZXR1cm4gcmVzO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHREZXNFREUzQ0JDKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3QgZGVjaXBoZXIgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlRGVjaXBoZXJpdihgZGVzLWVkZTMtY2JjYCwga2V5LmRhdGEsIG5ldyBVaW50OEFycmF5KGFsZ29yaXRobS5pdikpO1xyXG4gICAgICAgIGxldCBkZWMgPSBkZWNpcGhlci51cGRhdGUoZGF0YSk7XHJcbiAgICAgICAgZGVjID0gQnVmZmVyLmNvbmNhdChbZGVjLCBkZWNpcGhlci5maW5hbCgpXSk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGRlYykuYnVmZmVyO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIERlc0NiY1Byb3ZpZGVyIGV4dGVuZHMgY29yZS5EZXNQcm92aWRlciB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMua2V5U2l6ZUJpdHMgPSA2NDtcclxuICAgICAgICB0aGlzLml2U2l6ZSA9IDg7XHJcbiAgICAgICAgdGhpcy5uYW1lID0gXCJERVMtQ0JDXCI7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IGF3YWl0IERlc0NyeXB0by5nZW5lcmF0ZUtleSh7XHJcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcclxuICAgICAgICAgICAgbGVuZ3RoOiB0aGlzLmtleVNpemVCaXRzLFxyXG4gICAgICAgIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRW5jcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBEZXNDcnlwdG8uZW5jcnlwdChhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkRlY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gRGVzQ3J5cHRvLmRlY3J5cHQoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gRGVzQ3J5cHRvLmV4cG9ydEtleShmb3JtYXQsIGdldENyeXB0b0tleShrZXkpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gYXdhaXQgRGVzQ3J5cHRvLmltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIHsgbmFtZTogdGhpcy5uYW1lLCBsZW5ndGg6IHRoaXMua2V5U2l6ZUJpdHMgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgaWYgKGtleS5kYXRhLmxlbmd0aCAhPT0gKHRoaXMua2V5U2l6ZUJpdHMgPj4gMykpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJrZXlEYXRhOiBXcm9uZyBrZXkgc2l6ZVwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG4gICAgY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSkge1xyXG4gICAgICAgIHN1cGVyLmNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpO1xyXG4gICAgICAgIGlmICghKGdldENyeXB0b0tleShrZXkpIGluc3RhbmNlb2YgRGVzQ3J5cHRvS2V5KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwia2V5OiBJcyBub3QgYSBEZXNDcnlwdG9LZXlcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XG5cbmNsYXNzIERlc0VkZTNDYmNQcm92aWRlciBleHRlbmRzIGNvcmUuRGVzUHJvdmlkZXIge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLmtleVNpemVCaXRzID0gMTkyO1xyXG4gICAgICAgIHRoaXMuaXZTaXplID0gODtcclxuICAgICAgICB0aGlzLm5hbWUgPSBcIkRFUy1FREUzLUNCQ1wiO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBEZXNDcnlwdG8uZ2VuZXJhdGVLZXkoe1xyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgICAgIGxlbmd0aDogdGhpcy5rZXlTaXplQml0cyxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gRGVzQ3J5cHRvLmVuY3J5cHQoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZWNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIERlc0NyeXB0by5kZWNyeXB0KGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIERlc0NyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IGF3YWl0IERlc0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IG5hbWU6IHRoaXMubmFtZSwgbGVuZ3RoOiB0aGlzLmtleVNpemVCaXRzIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIGlmIChrZXkuZGF0YS5sZW5ndGggIT09ICh0aGlzLmtleVNpemVCaXRzID4+IDMpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwia2V5RGF0YTogV3Jvbmcga2V5IHNpemVcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIERlc0NyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IGEgRGVzQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5mdW5jdGlvbiBnZXRKd2tBbGdvcml0aG0oYWxnb3JpdGhtKSB7XHJcbiAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICBjYXNlIFwiUlNBLU9BRVBcIjoge1xyXG4gICAgICAgICAgICBjb25zdCBtZFNpemUgPSAvKFxcZCspJC8uZXhlYyhhbGdvcml0aG0uaGFzaC5uYW1lKVsxXTtcclxuICAgICAgICAgICAgcmV0dXJuIGBSU0EtT0FFUCR7bWRTaXplICE9PSBcIjFcIiA/IGAtJHttZFNpemV9YCA6IFwiXCJ9YDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY2FzZSBcIlJTQVNTQS1QS0NTMS1WMV81XCI6XHJcbiAgICAgICAgICAgIHJldHVybiBgUlMkey8oXFxkKykkLy5leGVjKGFsZ29yaXRobS5oYXNoLm5hbWUpWzFdfWA7XHJcbiAgICAgICAgY2FzZSBcIlJTQS1QU1NcIjpcclxuICAgICAgICAgICAgcmV0dXJuIGBQUyR7LyhcXGQrKSQvLmV4ZWMoYWxnb3JpdGhtLmhhc2gubmFtZSlbMV19YDtcclxuICAgICAgICBjYXNlIFwiUlNBLVBLQ1MxXCI6XHJcbiAgICAgICAgICAgIHJldHVybiBgUlMxYDtcclxuICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobTogSXMgbm90IHJlY29nbml6ZWRcIik7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgUnNhUHJpdmF0ZUtleSBleHRlbmRzIEFzeW1tZXRyaWNLZXkge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLnR5cGUgPSBcInByaXZhdGVcIjtcclxuICAgIH1cclxuICAgIGdldEtleSgpIHtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gQXNuUGFyc2VyLnBhcnNlKHRoaXMuZGF0YSwgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKTtcclxuICAgICAgICByZXR1cm4gQXNuUGFyc2VyLnBhcnNlKGtleUluZm8ucHJpdmF0ZUtleSwgY29yZS5hc24xLlJzYVByaXZhdGVLZXkpO1xyXG4gICAgfVxyXG4gICAgdG9KU09OKCkge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IHRoaXMuZ2V0S2V5KCk7XHJcbiAgICAgICAgY29uc3QganNvbiA9IHtcclxuICAgICAgICAgICAga3R5OiBcIlJTQVwiLFxyXG4gICAgICAgICAgICBhbGc6IGdldEp3a0FsZ29yaXRobSh0aGlzLmFsZ29yaXRobSksXHJcbiAgICAgICAgICAgIGtleV9vcHM6IHRoaXMudXNhZ2VzLFxyXG4gICAgICAgICAgICBleHQ6IHRoaXMuZXh0cmFjdGFibGUsXHJcbiAgICAgICAgfTtcclxuICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihqc29uLCBKc29uU2VyaWFsaXplci50b0pTT04oa2V5KSk7XHJcbiAgICB9XHJcbiAgICBmcm9tSlNPTihqc29uKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gSnNvblBhcnNlci5mcm9tSlNPTihqc29uLCB7IHRhcmdldFNjaGVtYTogY29yZS5hc24xLlJzYVByaXZhdGVLZXkgfSk7XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IG5ldyBjb3JlLmFzbjEuUHJpdmF0ZUtleUluZm8oKTtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXlBbGdvcml0aG0uYWxnb3JpdGhtID0gXCIxLjIuODQwLjExMzU0OS4xLjEuMVwiO1xyXG4gICAgICAgIGtleUluZm8ucHJpdmF0ZUtleUFsZ29yaXRobS5wYXJhbWV0ZXJzID0gbnVsbDtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXkgPSBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShrZXkpO1xyXG4gICAgICAgIHRoaXMuZGF0YSA9IEJ1ZmZlci5mcm9tKEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleUluZm8pKTtcclxuICAgIH1cclxufVxuXG5jbGFzcyBSc2FQdWJsaWNLZXkgZXh0ZW5kcyBBc3ltbWV0cmljS2V5IHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy50eXBlID0gXCJwdWJsaWNcIjtcclxuICAgIH1cclxuICAgIGdldEtleSgpIHtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gQXNuUGFyc2VyLnBhcnNlKHRoaXMuZGF0YSwgY29yZS5hc24xLlB1YmxpY0tleUluZm8pO1xyXG4gICAgICAgIHJldHVybiBBc25QYXJzZXIucGFyc2Uoa2V5SW5mby5wdWJsaWNLZXksIGNvcmUuYXNuMS5Sc2FQdWJsaWNLZXkpO1xyXG4gICAgfVxyXG4gICAgdG9KU09OKCkge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IHRoaXMuZ2V0S2V5KCk7XHJcbiAgICAgICAgY29uc3QganNvbiA9IHtcclxuICAgICAgICAgICAga3R5OiBcIlJTQVwiLFxyXG4gICAgICAgICAgICBhbGc6IGdldEp3a0FsZ29yaXRobSh0aGlzLmFsZ29yaXRobSksXHJcbiAgICAgICAgICAgIGtleV9vcHM6IHRoaXMudXNhZ2VzLFxyXG4gICAgICAgICAgICBleHQ6IHRoaXMuZXh0cmFjdGFibGUsXHJcbiAgICAgICAgfTtcclxuICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihqc29uLCBKc29uU2VyaWFsaXplci50b0pTT04oa2V5KSk7XHJcbiAgICB9XHJcbiAgICBmcm9tSlNPTihqc29uKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gSnNvblBhcnNlci5mcm9tSlNPTihqc29uLCB7IHRhcmdldFNjaGVtYTogY29yZS5hc24xLlJzYVB1YmxpY0tleSB9KTtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gbmV3IGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKCk7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXlBbGdvcml0aG0uYWxnb3JpdGhtID0gXCIxLjIuODQwLjExMzU0OS4xLjEuMVwiO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5QWxnb3JpdGhtLnBhcmFtZXRlcnMgPSBudWxsO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5ID0gQXNuU2VyaWFsaXplci5zZXJpYWxpemUoa2V5KTtcclxuICAgICAgICB0aGlzLmRhdGEgPSBCdWZmZXIuZnJvbShBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShrZXlJbmZvKSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgUnNhQ3J5cHRvIHtcclxuICAgIHN0YXRpYyBhc3luYyBnZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBwcml2YXRlS2V5ID0gbmV3IFJzYVByaXZhdGVLZXkoKTtcclxuICAgICAgICBwcml2YXRlS2V5LmFsZ29yaXRobSA9IGFsZ29yaXRobTtcclxuICAgICAgICBwcml2YXRlS2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAgcHJpdmF0ZUtleS51c2FnZXMgPSBrZXlVc2FnZXMuZmlsdGVyKCh1c2FnZSkgPT4gdGhpcy5wcml2YXRlS2V5VXNhZ2VzLmluZGV4T2YodXNhZ2UpICE9PSAtMSk7XHJcbiAgICAgICAgY29uc3QgcHVibGljS2V5ID0gbmV3IFJzYVB1YmxpY0tleSgpO1xyXG4gICAgICAgIHB1YmxpY0tleS5hbGdvcml0aG0gPSBhbGdvcml0aG07XHJcbiAgICAgICAgcHVibGljS2V5LmV4dHJhY3RhYmxlID0gdHJ1ZTtcclxuICAgICAgICBwdWJsaWNLZXkudXNhZ2VzID0ga2V5VXNhZ2VzLmZpbHRlcigodXNhZ2UpID0+IHRoaXMucHVibGljS2V5VXNhZ2VzLmluZGV4T2YodXNhZ2UpICE9PSAtMSk7XHJcbiAgICAgICAgY29uc3QgcHVibGljRXhwb25lbnQgPSBCdWZmZXIuY29uY2F0KFtcclxuICAgICAgICAgICAgQnVmZmVyLmFsbG9jKDQgLSBhbGdvcml0aG0ucHVibGljRXhwb25lbnQuYnl0ZUxlbmd0aCwgMCksXHJcbiAgICAgICAgICAgIEJ1ZmZlci5mcm9tKGFsZ29yaXRobS5wdWJsaWNFeHBvbmVudCksXHJcbiAgICAgICAgXSkucmVhZEludDMyQkUoMCk7XHJcbiAgICAgICAgY29uc3Qga2V5cyA9IGNyeXB0b19fZGVmYXVsdC5nZW5lcmF0ZUtleVBhaXJTeW5jKFwicnNhXCIsIHtcclxuICAgICAgICAgICAgbW9kdWx1c0xlbmd0aDogYWxnb3JpdGhtLm1vZHVsdXNMZW5ndGgsXHJcbiAgICAgICAgICAgIHB1YmxpY0V4cG9uZW50LFxyXG4gICAgICAgICAgICBwdWJsaWNLZXlFbmNvZGluZzoge1xyXG4gICAgICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJzcGtpXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHByaXZhdGVLZXlFbmNvZGluZzoge1xyXG4gICAgICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJwa2NzOFwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHByaXZhdGVLZXkuZGF0YSA9IGtleXMucHJpdmF0ZUtleTtcclxuICAgICAgICBwdWJsaWNLZXkuZGF0YSA9IGtleXMucHVibGljS2V5O1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IHtcclxuICAgICAgICAgICAgcHJpdmF0ZUtleSxcclxuICAgICAgICAgICAgcHVibGljS2V5LFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmV0dXJuIHJlcztcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBleHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICBzd2l0Y2ggKGZvcm1hdC50b0xvd2VyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgIGNhc2UgXCJqd2tcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBKc29uU2VyaWFsaXplci50b0pTT04oa2V5KTtcclxuICAgICAgICAgICAgY2FzZSBcInBrY3M4XCI6XHJcbiAgICAgICAgICAgIGNhc2UgXCJzcGtpXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoa2V5LmRhdGEpLmJ1ZmZlcjtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiZm9ybWF0OiBNdXN0IGJlICdqd2snLCAncGtjczgnIG9yICdzcGtpJ1wiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgc3dpdGNoIChmb3JtYXQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiandrXCI6IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGp3ayA9IGtleURhdGE7XHJcbiAgICAgICAgICAgICAgICBpZiAoandrLmQpIHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBhc25LZXkgPSBKc29uUGFyc2VyLmZyb21KU09OKGtleURhdGEsIHsgdGFyZ2V0U2NoZW1hOiBjb3JlLmFzbjEuUnNhUHJpdmF0ZUtleSB9KTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQcml2YXRlS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFzbktleSA9IEpzb25QYXJzZXIuZnJvbUpTT04oa2V5RGF0YSwgeyB0YXJnZXRTY2hlbWE6IGNvcmUuYXNuMS5Sc2FQdWJsaWNLZXkgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0UHVibGljS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjYXNlIFwic3BraVwiOiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBrZXlJbmZvID0gQXNuUGFyc2VyLnBhcnNlKG5ldyBVaW50OEFycmF5KGtleURhdGEpLCBjb3JlLmFzbjEuUHVibGljS2V5SW5mbyk7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBhc25LZXkgPSBBc25QYXJzZXIucGFyc2Uoa2V5SW5mby5wdWJsaWNLZXksIGNvcmUuYXNuMS5Sc2FQdWJsaWNLZXkpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0UHVibGljS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjYXNlIFwicGtjczhcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZShuZXcgVWludDhBcnJheShrZXlEYXRhKSwgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGFzbktleSA9IEFzblBhcnNlci5wYXJzZShrZXlJbmZvLnByaXZhdGVLZXksIGNvcmUuYXNuMS5Sc2FQcml2YXRlS2V5KTtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmltcG9ydFByaXZhdGVLZXkoYXNuS2V5LCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImZvcm1hdDogTXVzdCBiZSAnandrJywgJ3BrY3M4JyBvciAnc3BraSdcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIHNpZ24oYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIlJTQS1QU1NcIjpcclxuICAgICAgICAgICAgY2FzZSBcIlJTQVNTQS1QS0NTMS1WMV81XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5zaWduUnNhKGFsZ29yaXRobSwga2V5LCBkYXRhKTtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiYWxnb3JpdGhtOiBJcyBub3QgcmVjb2duaXplZFwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgdmVyaWZ5KGFsZ29yaXRobSwga2V5LCBzaWduYXR1cmUsIGRhdGEpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIlJTQS1QU1NcIjpcclxuICAgICAgICAgICAgY2FzZSBcIlJTQVNTQS1QS0NTMS1WMV81XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy52ZXJpZnlTU0EoYWxnb3JpdGhtLCBrZXksIGRhdGEsIHNpZ25hdHVyZSk7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobTogSXMgbm90IHJlY29nbml6ZWRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIlJTQS1PQUVQXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5lbmNyeXB0T0FFUChhbGdvcml0aG0sIGtleSwgZGF0YSk7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobTogSXMgbm90IHJlY29nbml6ZWRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGRlY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIlJTQS1PQUVQXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5kZWNyeXB0T0FFUChhbGdvcml0aG0sIGtleSwgZGF0YSk7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobTogSXMgbm90IHJlY29nbml6ZWRcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGltcG9ydFByaXZhdGVLZXkoYXNuS2V5LCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gbmV3IGNvcmUuYXNuMS5Qcml2YXRlS2V5SW5mbygpO1xyXG4gICAgICAgIGtleUluZm8ucHJpdmF0ZUtleUFsZ29yaXRobS5hbGdvcml0aG0gPSBcIjEuMi44NDAuMTEzNTQ5LjEuMS4xXCI7XHJcbiAgICAgICAga2V5SW5mby5wcml2YXRlS2V5QWxnb3JpdGhtLnBhcmFtZXRlcnMgPSBudWxsO1xyXG4gICAgICAgIGtleUluZm8ucHJpdmF0ZUtleSA9IEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGFzbktleSk7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gbmV3IFJzYVByaXZhdGVLZXkoKTtcclxuICAgICAgICBrZXkuZGF0YSA9IEJ1ZmZlci5mcm9tKEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleUluZm8pKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtID0gT2JqZWN0LmFzc2lnbih7fSwgYWxnb3JpdGhtKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtLnB1YmxpY0V4cG9uZW50ID0gbmV3IFVpbnQ4QXJyYXkoYXNuS2V5LnB1YmxpY0V4cG9uZW50KTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtLm1vZHVsdXNMZW5ndGggPSBhc25LZXkubW9kdWx1cy5ieXRlTGVuZ3RoIDw8IDM7XHJcbiAgICAgICAga2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAga2V5LnVzYWdlcyA9IGtleVVzYWdlcztcclxuICAgICAgICByZXR1cm4ga2V5O1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGltcG9ydFB1YmxpY0tleShhc25LZXksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleUluZm8gPSBuZXcgY29yZS5hc24xLlB1YmxpY0tleUluZm8oKTtcclxuICAgICAgICBrZXlJbmZvLnB1YmxpY0tleUFsZ29yaXRobS5hbGdvcml0aG0gPSBcIjEuMi44NDAuMTEzNTQ5LjEuMS4xXCI7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXlBbGdvcml0aG0ucGFyYW1ldGVycyA9IG51bGw7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXkgPSBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShhc25LZXkpO1xyXG4gICAgICAgIGNvbnN0IGtleSA9IG5ldyBSc2FQdWJsaWNLZXkoKTtcclxuICAgICAgICBrZXkuZGF0YSA9IEJ1ZmZlci5mcm9tKEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleUluZm8pKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtID0gT2JqZWN0LmFzc2lnbih7fSwgYWxnb3JpdGhtKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtLnB1YmxpY0V4cG9uZW50ID0gbmV3IFVpbnQ4QXJyYXkoYXNuS2V5LnB1YmxpY0V4cG9uZW50KTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtLm1vZHVsdXNMZW5ndGggPSBhc25LZXkubW9kdWx1cy5ieXRlTGVuZ3RoIDw8IDM7XHJcbiAgICAgICAga2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAga2V5LnVzYWdlcyA9IGtleVVzYWdlcztcclxuICAgICAgICByZXR1cm4ga2V5O1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGdldENyeXB0b0FsZ29yaXRobShhbGcpIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZy5oYXNoLm5hbWUudG9VcHBlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiU0hBLTFcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBcIlJTQS1TSEExXCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEtMjU2XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJSU0EtU0hBMjU2XCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEtMzg0XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJSU0EtU0hBMzg0XCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEtNTEyXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJSU0EtU0hBNTEyXCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEzLTI1NlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwiUlNBLVNIQTMtMjU2XCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEzLTM4NFwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwiUlNBLVNIQTMtMzg0XCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEzLTUxMlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwiUlNBLVNIQTMtNTEyXCI7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImFsZ29yaXRobS5oYXNoOiBJcyBub3QgcmVjb2duaXplZFwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgc2lnblJzYShhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNyeXB0b0FsZyA9IHRoaXMuZ2V0Q3J5cHRvQWxnb3JpdGhtKGtleS5hbGdvcml0aG0pO1xyXG4gICAgICAgIGNvbnN0IHNpZ25lciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVTaWduKGNyeXB0b0FsZyk7XHJcbiAgICAgICAgc2lnbmVyLnVwZGF0ZShCdWZmZXIuZnJvbShkYXRhKSk7XHJcbiAgICAgICAgaWYgKCFrZXkucGVtKSB7XHJcbiAgICAgICAgICAgIGtleS5wZW0gPSBgLS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tXFxuJHtrZXkuZGF0YS50b1N0cmluZyhcImJhc2U2NFwiKX1cXG4tLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tYDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcclxuICAgICAgICAgICAga2V5OiBrZXkucGVtLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgaWYgKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkgPT09IFwiUlNBLVBTU1wiKSB7XHJcbiAgICAgICAgICAgIG9wdGlvbnMucGFkZGluZyA9IGNyeXB0b19fZGVmYXVsdC5jb25zdGFudHMuUlNBX1BLQ1MxX1BTU19QQURESU5HO1xyXG4gICAgICAgICAgICBvcHRpb25zLnNhbHRMZW5ndGggPSBhbGdvcml0aG0uc2FsdExlbmd0aDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgc2lnbmF0dXJlID0gc2lnbmVyLnNpZ24ob3B0aW9ucyk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KHNpZ25hdHVyZSkuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIHZlcmlmeVNTQShhbGdvcml0aG0sIGtleSwgZGF0YSwgc2lnbmF0dXJlKSB7XHJcbiAgICAgICAgY29uc3QgY3J5cHRvQWxnID0gdGhpcy5nZXRDcnlwdG9BbGdvcml0aG0oa2V5LmFsZ29yaXRobSk7XHJcbiAgICAgICAgY29uc3Qgc2lnbmVyID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZVZlcmlmeShjcnlwdG9BbGcpO1xyXG4gICAgICAgIHNpZ25lci51cGRhdGUoQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgIGlmICgha2V5LnBlbSkge1xyXG4gICAgICAgICAgICBrZXkucGVtID0gYC0tLS0tQkVHSU4gUFVCTElDIEtFWS0tLS0tXFxuJHtrZXkuZGF0YS50b1N0cmluZyhcImJhc2U2NFwiKX1cXG4tLS0tLUVORCBQVUJMSUMgS0VZLS0tLS1gO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xyXG4gICAgICAgICAgICBrZXk6IGtleS5wZW0sXHJcbiAgICAgICAgfTtcclxuICAgICAgICBpZiAoYWxnb3JpdGhtLm5hbWUudG9VcHBlckNhc2UoKSA9PT0gXCJSU0EtUFNTXCIpIHtcclxuICAgICAgICAgICAgb3B0aW9ucy5wYWRkaW5nID0gY3J5cHRvX19kZWZhdWx0LmNvbnN0YW50cy5SU0FfUEtDUzFfUFNTX1BBRERJTkc7XHJcbiAgICAgICAgICAgIG9wdGlvbnMuc2FsdExlbmd0aCA9IGFsZ29yaXRobS5zYWx0TGVuZ3RoO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBvayA9IHNpZ25lci52ZXJpZnkob3B0aW9ucywgc2lnbmF0dXJlKTtcclxuICAgICAgICByZXR1cm4gb2s7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgZW5jcnlwdE9BRVAoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xyXG4gICAgICAgICAgICBrZXk6IGAtLS0tLUJFR0lOIFBVQkxJQyBLRVktLS0tLVxcbiR7a2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tYCxcclxuICAgICAgICAgICAgcGFkZGluZzogY3J5cHRvX19kZWZhdWx0LmNvbnN0YW50cy5SU0FfUEtDUzFfT0FFUF9QQURESU5HLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgaWYgKGFsZ29yaXRobS5sYWJlbCkgO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShjcnlwdG9fX2RlZmF1bHQucHVibGljRW5jcnlwdChvcHRpb25zLCBkYXRhKSkuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGRlY3J5cHRPQUVQKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcclxuICAgICAgICAgICAga2V5OiBgLS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tXFxuJHtrZXkuZGF0YS50b1N0cmluZyhcImJhc2U2NFwiKX1cXG4tLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tYCxcclxuICAgICAgICAgICAgcGFkZGluZzogY3J5cHRvX19kZWZhdWx0LmNvbnN0YW50cy5SU0FfUEtDUzFfT0FFUF9QQURESU5HLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgaWYgKGFsZ29yaXRobS5sYWJlbCkgO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShjcnlwdG9fX2RlZmF1bHQucHJpdmF0ZURlY3J5cHQob3B0aW9ucywgZGF0YSkpLmJ1ZmZlcjtcclxuICAgIH1cclxufVxyXG5Sc2FDcnlwdG8ucHVibGljS2V5VXNhZ2VzID0gW1widmVyaWZ5XCIsIFwiZW5jcnlwdFwiLCBcIndyYXBLZXlcIl07XHJcblJzYUNyeXB0by5wcml2YXRlS2V5VXNhZ2VzID0gW1wic2lnblwiLCBcImRlY3J5cHRcIiwgXCJ1bndyYXBLZXlcIl07XG5cbmNsYXNzIFJzYVNzYVByb3ZpZGVyIGV4dGVuZHMgY29yZS5Sc2FTc2FQcm92aWRlciB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMuaGFzaEFsZ29yaXRobXMgPSBbXHJcbiAgICAgICAgICAgIFwiU0hBLTFcIiwgXCJTSEEtMjU2XCIsIFwiU0hBLTM4NFwiLCBcIlNIQS01MTJcIixcclxuICAgICAgICAgICAgXCJzaGFrZTEyOFwiLCBcInNoYWtlMjU2XCIsXHJcbiAgICAgICAgICAgIFwiU0hBMy0yNTZcIiwgXCJTSEEzLTM4NFwiLCBcIlNIQTMtNTEyXCJcclxuICAgICAgICBdO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXlzID0gYXdhaXQgUnNhQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgLi4uYWxnb3JpdGhtLFxyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgcHJpdmF0ZUtleTogc2V0Q3J5cHRvS2V5KGtleXMucHJpdmF0ZUtleSksXHJcbiAgICAgICAgICAgIHB1YmxpY0tleTogc2V0Q3J5cHRvS2V5KGtleXMucHVibGljS2V5KSxcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25TaWduKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIFJzYUNyeXB0by5zaWduKGFsZ29yaXRobSwgZ2V0Q3J5cHRvS2V5KGtleSksIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uVmVyaWZ5KGFsZ29yaXRobSwga2V5LCBzaWduYXR1cmUsIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gUnNhQ3J5cHRvLnZlcmlmeShhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShzaWduYXR1cmUpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIHJldHVybiBSc2FDcnlwdG8uZXhwb3J0S2V5KGZvcm1hdCwgZ2V0Q3J5cHRvS2V5KGtleSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBSc2FDcnlwdG8uaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgeyAuLi5hbGdvcml0aG0sIG5hbWU6IHRoaXMubmFtZSB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBjaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKSB7XHJcbiAgICAgICAgc3VwZXIuY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSk7XHJcbiAgICAgICAgY29uc3QgaW50ZXJuYWxLZXkgPSBnZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgICAgICBpZiAoIShpbnRlcm5hbEtleSBpbnN0YW5jZW9mIFJzYVByaXZhdGVLZXkgfHwgaW50ZXJuYWxLZXkgaW5zdGFuY2VvZiBSc2FQdWJsaWNLZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBSU0EgQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBSc2FQc3NQcm92aWRlciBleHRlbmRzIGNvcmUuUnNhUHNzUHJvdmlkZXIge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLmhhc2hBbGdvcml0aG1zID0gW1xyXG4gICAgICAgICAgICBcIlNIQS0xXCIsIFwiU0hBLTI1NlwiLCBcIlNIQS0zODRcIiwgXCJTSEEtNTEyXCIsXHJcbiAgICAgICAgICAgIFwic2hha2UxMjhcIiwgXCJzaGFrZTI1NlwiLFxyXG4gICAgICAgICAgICBcIlNIQTMtMjU2XCIsIFwiU0hBMy0zODRcIiwgXCJTSEEzLTUxMlwiXHJcbiAgICAgICAgXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uR2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5cyA9IGF3YWl0IFJzYUNyeXB0by5nZW5lcmF0ZUtleSh7XHJcbiAgICAgICAgICAgIC4uLmFsZ29yaXRobSxcclxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICAgIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHByaXZhdGVLZXk6IHNldENyeXB0b0tleShrZXlzLnByaXZhdGVLZXkpLFxyXG4gICAgICAgICAgICBwdWJsaWNLZXk6IHNldENyeXB0b0tleShrZXlzLnB1YmxpY0tleSksXHJcbiAgICAgICAgfTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uU2lnbihhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBSc2FDcnlwdG8uc2lnbihhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvblZlcmlmeShhbGdvcml0aG0sIGtleSwgc2lnbmF0dXJlLCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIFJzYUNyeXB0by52ZXJpZnkoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoc2lnbmF0dXJlKSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gUnNhQ3J5cHRvLmV4cG9ydEtleShmb3JtYXQsIGdldENyeXB0b0tleShrZXkpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gYXdhaXQgUnNhQ3J5cHRvLmltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIHsgLi4uYWxnb3JpdGhtLCBuYW1lOiB0aGlzLm5hbWUgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG4gICAgY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSkge1xyXG4gICAgICAgIHN1cGVyLmNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpO1xyXG4gICAgICAgIGNvbnN0IGludGVybmFsS2V5ID0gZ2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICAgICAgaWYgKCEoaW50ZXJuYWxLZXkgaW5zdGFuY2VvZiBSc2FQcml2YXRlS2V5IHx8IGludGVybmFsS2V5IGluc3RhbmNlb2YgUnNhUHVibGljS2V5KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwia2V5OiBJcyBub3QgUlNBIENyeXB0b0tleVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgU2hhQ3J5cHRvIHtcclxuICAgIHN0YXRpYyBzaXplKGFsZ29yaXRobSkge1xyXG4gICAgICAgIHN3aXRjaCAoYWxnb3JpdGhtLm5hbWUudG9VcHBlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiU0hBLTFcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiAxNjA7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEtMjU2XCI6XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEzLTI1NlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIDI1NjtcclxuICAgICAgICAgICAgY2FzZSBcIlNIQS0zODRcIjpcclxuICAgICAgICAgICAgY2FzZSBcIlNIQTMtMzg0XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gMzg0O1xyXG4gICAgICAgICAgICBjYXNlIFwiU0hBLTUxMlwiOlxyXG4gICAgICAgICAgICBjYXNlIFwiU0hBMy01MTJcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiA1MTI7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJVbnJlY29nbml6ZWQgbmFtZVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgZ2V0QWxnb3JpdGhtTmFtZShhbGdvcml0aG0pIHtcclxuICAgICAgICBzd2l0Y2ggKGFsZ29yaXRobS5uYW1lLnRvVXBwZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcIlNIQS0xXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzaGExXCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEtMjU2XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzaGEyNTZcIjtcclxuICAgICAgICAgICAgY2FzZSBcIlNIQS0zODRcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBcInNoYTM4NFwiO1xyXG4gICAgICAgICAgICBjYXNlIFwiU0hBLTUxMlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwic2hhNTEyXCI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJTSEEzLTI1NlwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIFwic2hhMy0yNTZcIjtcclxuICAgICAgICAgICAgY2FzZSBcIlNIQTMtMzg0XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzaGEzLTM4NFwiO1xyXG4gICAgICAgICAgICBjYXNlIFwiU0hBMy01MTJcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBcInNoYTMtNTEyXCI7XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJVbnJlY29nbml6ZWQgbmFtZVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgZGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGhhc2hBbGcgPSB0aGlzLmdldEFsZ29yaXRobU5hbWUoYWxnb3JpdGhtKTtcclxuICAgICAgICBjb25zdCBoYXNoID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUhhc2goaGFzaEFsZylcclxuICAgICAgICAgICAgLnVwZGF0ZShCdWZmZXIuZnJvbShkYXRhKSkuZGlnZXN0KCk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGhhc2gpLmJ1ZmZlcjtcclxuICAgIH1cclxufVxuXG5jbGFzcyBSc2FPYWVwUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlJzYU9hZXBQcm92aWRlciB7XHJcbiAgICBhc3luYyBvbkdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleXMgPSBhd2FpdCBSc2FDcnlwdG8uZ2VuZXJhdGVLZXkoe1xyXG4gICAgICAgICAgICAuLi5hbGdvcml0aG0sXHJcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBwcml2YXRlS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wcml2YXRlS2V5KSxcclxuICAgICAgICAgICAgcHVibGljS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wdWJsaWNLZXkpLFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBpbnRlcm5hbEtleSA9IGdldENyeXB0b0tleShrZXkpO1xyXG4gICAgICAgIGNvbnN0IGRhdGFWaWV3ID0gbmV3IFVpbnQ4QXJyYXkoZGF0YSk7XHJcbiAgICAgICAgY29uc3Qga2V5U2l6ZSA9IE1hdGguY2VpbChpbnRlcm5hbEtleS5hbGdvcml0aG0ubW9kdWx1c0xlbmd0aCA+PiAzKTtcclxuICAgICAgICBjb25zdCBoYXNoU2l6ZSA9IFNoYUNyeXB0by5zaXplKGludGVybmFsS2V5LmFsZ29yaXRobS5oYXNoKSA+PiAzO1xyXG4gICAgICAgIGNvbnN0IGRhdGFMZW5ndGggPSBkYXRhVmlldy5ieXRlTGVuZ3RoO1xyXG4gICAgICAgIGNvbnN0IHBzTGVuZ3RoID0ga2V5U2l6ZSAtIGRhdGFMZW5ndGggLSAyICogaGFzaFNpemUgLSAyO1xyXG4gICAgICAgIGlmIChkYXRhTGVuZ3RoID4ga2V5U2l6ZSAtIDIgKiBoYXNoU2l6ZSAtIDIpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRGF0YSB0b28gbGFyZ2VcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IG1lc3NhZ2UgPSBuZXcgVWludDhBcnJheShrZXlTaXplKTtcclxuICAgICAgICBjb25zdCBzZWVkID0gbWVzc2FnZS5zdWJhcnJheSgxLCBoYXNoU2l6ZSArIDEpO1xyXG4gICAgICAgIGNvbnN0IGRhdGFCbG9jayA9IG1lc3NhZ2Uuc3ViYXJyYXkoaGFzaFNpemUgKyAxKTtcclxuICAgICAgICBkYXRhQmxvY2suc2V0KGRhdGFWaWV3LCBoYXNoU2l6ZSArIHBzTGVuZ3RoICsgMSk7XHJcbiAgICAgICAgY29uc3QgbGFiZWxIYXNoID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUhhc2goaW50ZXJuYWxLZXkuYWxnb3JpdGhtLmhhc2gubmFtZS5yZXBsYWNlKFwiLVwiLCBcIlwiKSlcclxuICAgICAgICAgICAgLnVwZGF0ZShjb3JlLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoYWxnb3JpdGhtLmxhYmVsIHx8IG5ldyBVaW50OEFycmF5KDApKSlcclxuICAgICAgICAgICAgLmRpZ2VzdCgpO1xyXG4gICAgICAgIGRhdGFCbG9jay5zZXQobGFiZWxIYXNoLCAwKTtcclxuICAgICAgICBkYXRhQmxvY2tbaGFzaFNpemUgKyBwc0xlbmd0aF0gPSAxO1xyXG4gICAgICAgIGNyeXB0b19fZGVmYXVsdC5yYW5kb21GaWxsU3luYyhzZWVkKTtcclxuICAgICAgICBjb25zdCBkYXRhQmxvY2tNYXNrID0gdGhpcy5tZ2YxKGludGVybmFsS2V5LmFsZ29yaXRobS5oYXNoLCBzZWVkLCBkYXRhQmxvY2subGVuZ3RoKTtcclxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGFCbG9jay5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBkYXRhQmxvY2tbaV0gXj0gZGF0YUJsb2NrTWFza1tpXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgc2VlZE1hc2sgPSB0aGlzLm1nZjEoaW50ZXJuYWxLZXkuYWxnb3JpdGhtLmhhc2gsIGRhdGFCbG9jaywgc2VlZC5sZW5ndGgpO1xyXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2VlZC5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBzZWVkW2ldIF49IHNlZWRNYXNrW2ldO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAoIWludGVybmFsS2V5LnBlbSkge1xyXG4gICAgICAgICAgICBpbnRlcm5hbEtleS5wZW0gPSBgLS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS1cXG4ke2ludGVybmFsS2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tYDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgcGtjczAgPSBjcnlwdG9fX2RlZmF1bHQucHVibGljRW5jcnlwdCh7XHJcbiAgICAgICAgICAgIGtleTogaW50ZXJuYWxLZXkucGVtLFxyXG4gICAgICAgICAgICBwYWRkaW5nOiBjcnlwdG9fX2RlZmF1bHQuY29uc3RhbnRzLlJTQV9OT19QQURESU5HLFxyXG4gICAgICAgIH0sIEJ1ZmZlci5mcm9tKG1lc3NhZ2UpKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkocGtjczApLmJ1ZmZlcjtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGVjcnlwdChhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGludGVybmFsS2V5ID0gZ2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICAgICAgY29uc3Qga2V5U2l6ZSA9IE1hdGguY2VpbChpbnRlcm5hbEtleS5hbGdvcml0aG0ubW9kdWx1c0xlbmd0aCA+PiAzKTtcclxuICAgICAgICBjb25zdCBoYXNoU2l6ZSA9IFNoYUNyeXB0by5zaXplKGludGVybmFsS2V5LmFsZ29yaXRobS5oYXNoKSA+PiAzO1xyXG4gICAgICAgIGNvbnN0IGRhdGFMZW5ndGggPSBkYXRhLmJ5dGVMZW5ndGg7XHJcbiAgICAgICAgaWYgKGRhdGFMZW5ndGggIT09IGtleVNpemUpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQmFkIGRhdGFcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICghaW50ZXJuYWxLZXkucGVtKSB7XHJcbiAgICAgICAgICAgIGludGVybmFsS2V5LnBlbSA9IGAtLS0tLUJFR0lOIFBSSVZBVEUgS0VZLS0tLS1cXG4ke2ludGVybmFsS2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFJJVkFURSBLRVktLS0tLWA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBwa2NzMCA9IGNyeXB0b19fZGVmYXVsdC5wcml2YXRlRGVjcnlwdCh7XHJcbiAgICAgICAgICAgIGtleTogaW50ZXJuYWxLZXkucGVtLFxyXG4gICAgICAgICAgICBwYWRkaW5nOiBjcnlwdG9fX2RlZmF1bHQuY29uc3RhbnRzLlJTQV9OT19QQURESU5HLFxyXG4gICAgICAgIH0sIEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICBjb25zdCB6ID0gcGtjczBbMF07XHJcbiAgICAgICAgY29uc3Qgc2VlZCA9IHBrY3MwLnN1YmFycmF5KDEsIGhhc2hTaXplICsgMSk7XHJcbiAgICAgICAgY29uc3QgZGF0YUJsb2NrID0gcGtjczAuc3ViYXJyYXkoaGFzaFNpemUgKyAxKTtcclxuICAgICAgICBpZiAoeiAhPT0gMCkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJEZWNyeXB0aW9uIGZhaWxlZFwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgc2VlZE1hc2sgPSB0aGlzLm1nZjEoaW50ZXJuYWxLZXkuYWxnb3JpdGhtLmhhc2gsIGRhdGFCbG9jaywgc2VlZC5sZW5ndGgpO1xyXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2VlZC5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBzZWVkW2ldIF49IHNlZWRNYXNrW2ldO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBkYXRhQmxvY2tNYXNrID0gdGhpcy5tZ2YxKGludGVybmFsS2V5LmFsZ29yaXRobS5oYXNoLCBzZWVkLCBkYXRhQmxvY2subGVuZ3RoKTtcclxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGRhdGFCbG9jay5sZW5ndGg7IGkrKykge1xyXG4gICAgICAgICAgICBkYXRhQmxvY2tbaV0gXj0gZGF0YUJsb2NrTWFza1tpXTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3QgbGFiZWxIYXNoID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUhhc2goaW50ZXJuYWxLZXkuYWxnb3JpdGhtLmhhc2gubmFtZS5yZXBsYWNlKFwiLVwiLCBcIlwiKSlcclxuICAgICAgICAgICAgLnVwZGF0ZShjb3JlLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b1VpbnQ4QXJyYXkoYWxnb3JpdGhtLmxhYmVsIHx8IG5ldyBVaW50OEFycmF5KDApKSlcclxuICAgICAgICAgICAgLmRpZ2VzdCgpO1xyXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgaGFzaFNpemU7IGkrKykge1xyXG4gICAgICAgICAgICBpZiAobGFiZWxIYXNoW2ldICE9PSBkYXRhQmxvY2tbaV0pIHtcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkRlY3J5cHRpb24gZmFpbGVkXCIpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBwc0VuZCA9IGhhc2hTaXplO1xyXG4gICAgICAgIGZvciAoOyBwc0VuZCA8IGRhdGFCbG9jay5sZW5ndGg7IHBzRW5kKyspIHtcclxuICAgICAgICAgICAgY29uc3QgcHN6ID0gZGF0YUJsb2NrW3BzRW5kXTtcclxuICAgICAgICAgICAgaWYgKHBzeiA9PT0gMSkge1xyXG4gICAgICAgICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgaWYgKHBzeiAhPT0gMCkge1xyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRGVjcnlwdGlvbiBmYWlsZWRcIik7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKHBzRW5kID09PSBkYXRhQmxvY2subGVuZ3RoKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkRlY3J5cHRpb24gZmFpbGVkXCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBwa2NzMCA9IGRhdGFCbG9jay5zdWJhcnJheShwc0VuZCArIDEpO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShwa2NzMCkuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gUnNhQ3J5cHRvLmV4cG9ydEtleShmb3JtYXQsIGdldENyeXB0b0tleShrZXkpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gYXdhaXQgUnNhQ3J5cHRvLmltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIHsgLi4uYWxnb3JpdGhtLCBuYW1lOiB0aGlzLm5hbWUgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG4gICAgY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSkge1xyXG4gICAgICAgIHN1cGVyLmNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpO1xyXG4gICAgICAgIGNvbnN0IGludGVybmFsS2V5ID0gZ2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICAgICAgaWYgKCEoaW50ZXJuYWxLZXkgaW5zdGFuY2VvZiBSc2FQcml2YXRlS2V5IHx8IGludGVybmFsS2V5IGluc3RhbmNlb2YgUnNhUHVibGljS2V5KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwia2V5OiBJcyBub3QgUlNBIENyeXB0b0tleVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBtZ2YxKGFsZ29yaXRobSwgc2VlZCwgbGVuZ3RoID0gMCkge1xyXG4gICAgICAgIGNvbnN0IGhhc2hTaXplID0gU2hhQ3J5cHRvLnNpemUoYWxnb3JpdGhtKSA+PiAzO1xyXG4gICAgICAgIGNvbnN0IG1hc2sgPSBuZXcgVWludDhBcnJheShsZW5ndGgpO1xyXG4gICAgICAgIGNvbnN0IGNvdW50ZXIgPSBuZXcgVWludDhBcnJheSg0KTtcclxuICAgICAgICBjb25zdCBjaHVua3MgPSBNYXRoLmNlaWwobGVuZ3RoIC8gaGFzaFNpemUpO1xyXG4gICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgY2h1bmtzOyBpKyspIHtcclxuICAgICAgICAgICAgY291bnRlclswXSA9IGkgPj4+IDI0O1xyXG4gICAgICAgICAgICBjb3VudGVyWzFdID0gKGkgPj4+IDE2KSAmIDI1NTtcclxuICAgICAgICAgICAgY291bnRlclsyXSA9IChpID4+PiA4KSAmIDI1NTtcclxuICAgICAgICAgICAgY291bnRlclszXSA9IGkgJiAyNTU7XHJcbiAgICAgICAgICAgIGNvbnN0IHN1Ym1hc2sgPSBtYXNrLnN1YmFycmF5KGkgKiBoYXNoU2l6ZSk7XHJcbiAgICAgICAgICAgIGxldCBjaHVuayA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVIYXNoKGFsZ29yaXRobS5uYW1lLnJlcGxhY2UoXCItXCIsIFwiXCIpKVxyXG4gICAgICAgICAgICAgICAgLnVwZGF0ZShzZWVkKVxyXG4gICAgICAgICAgICAgICAgLnVwZGF0ZShjb3VudGVyKVxyXG4gICAgICAgICAgICAgICAgLmRpZ2VzdCgpO1xyXG4gICAgICAgICAgICBpZiAoY2h1bmsubGVuZ3RoID4gc3VibWFzay5sZW5ndGgpIHtcclxuICAgICAgICAgICAgICAgIGNodW5rID0gY2h1bmsuc3ViYXJyYXkoMCwgc3VibWFzay5sZW5ndGgpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHN1Ym1hc2suc2V0KGNodW5rKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG1hc2s7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgUnNhRXNQcm92aWRlciBleHRlbmRzIGNvcmUuUHJvdmlkZXJDcnlwdG8ge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLm5hbWUgPSBcIlJTQUVTLVBLQ1MxLXYxXzVcIjtcclxuICAgICAgICB0aGlzLnVzYWdlcyA9IHtcclxuICAgICAgICAgICAgcHVibGljS2V5OiBbXCJlbmNyeXB0XCIsIFwid3JhcEtleVwiXSxcclxuICAgICAgICAgICAgcHJpdmF0ZUtleTogW1wiZGVjcnlwdFwiLCBcInVud3JhcEtleVwiXSxcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXlzID0gYXdhaXQgUnNhQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgLi4uYWxnb3JpdGhtLFxyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgcHJpdmF0ZUtleTogc2V0Q3J5cHRvS2V5KGtleXMucHJpdmF0ZUtleSksXHJcbiAgICAgICAgICAgIHB1YmxpY0tleTogc2V0Q3J5cHRvS2V5KGtleXMucHVibGljS2V5KSxcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG4gICAgY2hlY2tHZW5lcmF0ZUtleVBhcmFtcyhhbGdvcml0aG0pIHtcclxuICAgICAgICB0aGlzLmNoZWNrUmVxdWlyZWRQcm9wZXJ0eShhbGdvcml0aG0sIFwicHVibGljRXhwb25lbnRcIik7XHJcbiAgICAgICAgaWYgKCEoYWxnb3JpdGhtLnB1YmxpY0V4cG9uZW50ICYmIGFsZ29yaXRobS5wdWJsaWNFeHBvbmVudCBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJwdWJsaWNFeHBvbmVudDogTWlzc2luZyBvciBub3QgYSBVaW50OEFycmF5XCIpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBwdWJsaWNFeHBvbmVudCA9IENvbnZlcnQuVG9CYXNlNjQoYWxnb3JpdGhtLnB1YmxpY0V4cG9uZW50KTtcclxuICAgICAgICBpZiAoIShwdWJsaWNFeHBvbmVudCA9PT0gXCJBdz09XCIgfHwgcHVibGljRXhwb25lbnQgPT09IFwiQVFBQlwiKSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwicHVibGljRXhwb25lbnQ6IE11c3QgYmUgWzNdIG9yIFsxLDAsMV1cIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRoaXMuY2hlY2tSZXF1aXJlZFByb3BlcnR5KGFsZ29yaXRobSwgXCJtb2R1bHVzTGVuZ3RoXCIpO1xyXG4gICAgICAgIHN3aXRjaCAoYWxnb3JpdGhtLm1vZHVsdXNMZW5ndGgpIHtcclxuICAgICAgICAgICAgY2FzZSAxMDI0OlxyXG4gICAgICAgICAgICBjYXNlIDIwNDg6XHJcbiAgICAgICAgICAgIGNhc2UgNDA5NjpcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcIm1vZHVsdXNMZW5ndGg6IE11c3QgYmUgMTAyNCwgMjA0OCwgb3IgNDA5NlwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkVuY3J5cHQoYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBvcHRpb25zID0gdGhpcy50b0NyeXB0b09wdGlvbnMoa2V5KTtcclxuICAgICAgICBjb25zdCBlbmMgPSBjcnlwdG8ucHVibGljRW5jcnlwdChvcHRpb25zLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGVuYykuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZWNyeXB0KGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHRoaXMudG9DcnlwdG9PcHRpb25zKGtleSk7XHJcbiAgICAgICAgY29uc3QgZGVjID0gY3J5cHRvLnByaXZhdGVEZWNyeXB0KG9wdGlvbnMsIG5ldyBVaW50OEFycmF5KGRhdGEpKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoZGVjKS5idWZmZXI7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIHJldHVybiBSc2FDcnlwdG8uZXhwb3J0S2V5KGZvcm1hdCwgZ2V0Q3J5cHRvS2V5KGtleSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBSc2FDcnlwdG8uaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgeyAuLi5hbGdvcml0aG0sIG5hbWU6IHRoaXMubmFtZSB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBjaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKSB7XHJcbiAgICAgICAgc3VwZXIuY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSk7XHJcbiAgICAgICAgY29uc3QgaW50ZXJuYWxLZXkgPSBnZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgICAgICBpZiAoIShpbnRlcm5hbEtleSBpbnN0YW5jZW9mIFJzYVByaXZhdGVLZXkgfHwgaW50ZXJuYWxLZXkgaW5zdGFuY2VvZiBSc2FQdWJsaWNLZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBSU0EgQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHRvQ3J5cHRvT3B0aW9ucyhrZXkpIHtcclxuICAgICAgICBjb25zdCB0eXBlID0ga2V5LnR5cGUudG9VcHBlckNhc2UoKTtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBrZXk6IGAtLS0tLUJFR0lOICR7dHlwZX0gS0VZLS0tLS1cXG4ke2dldENyeXB0b0tleShrZXkpLmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgJHt0eXBlfSBLRVktLS0tLWAsXHJcbiAgICAgICAgICAgIHBhZGRpbmc6IGNyeXB0by5jb25zdGFudHMuUlNBX1BLQ1MxX1BBRERJTkcsXHJcbiAgICAgICAgfTtcclxuICAgIH1cclxufVxuXG5jb25zdCBuYW1lZE9JRHMgPSB7XHJcbiAgICBcIjEuMi44NDAuMTAwNDUuMy4xLjdcIjogXCJQLTI1NlwiLFxyXG4gICAgXCJQLTI1NlwiOiBcIjEuMi44NDAuMTAwNDUuMy4xLjdcIixcclxuICAgIFwiMS4zLjEzMi4wLjM0XCI6IFwiUC0zODRcIixcclxuICAgIFwiUC0zODRcIjogXCIxLjMuMTMyLjAuMzRcIixcclxuICAgIFwiMS4zLjEzMi4wLjM1XCI6IFwiUC01MjFcIixcclxuICAgIFwiUC01MjFcIjogXCIxLjMuMTMyLjAuMzVcIixcclxuICAgIFwiMS4zLjEzMi4wLjEwXCI6IFwiSy0yNTZcIixcclxuICAgIFwiSy0yNTZcIjogXCIxLjMuMTMyLjAuMTBcIixcclxuICAgIFwiYnJhaW5wb29sUDE2MHIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjFcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjFcIjogXCJicmFpbnBvb2xQMTYwcjFcIixcclxuICAgIFwiYnJhaW5wb29sUDE2MHQxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjJcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjJcIjogXCJicmFpbnBvb2xQMTYwdDFcIixcclxuICAgIFwiYnJhaW5wb29sUDE5MnIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjNcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjNcIjogXCJicmFpbnBvb2xQMTkycjFcIixcclxuICAgIFwiYnJhaW5wb29sUDE5MnQxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjRcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjRcIjogXCJicmFpbnBvb2xQMTkydDFcIixcclxuICAgIFwiYnJhaW5wb29sUDIyNHIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjVcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjVcIjogXCJicmFpbnBvb2xQMjI0cjFcIixcclxuICAgIFwiYnJhaW5wb29sUDIyNHQxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjZcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjZcIjogXCJicmFpbnBvb2xQMjI0dDFcIixcclxuICAgIFwiYnJhaW5wb29sUDI1NnIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjdcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjdcIjogXCJicmFpbnBvb2xQMjU2cjFcIixcclxuICAgIFwiYnJhaW5wb29sUDI1NnQxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjhcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjhcIjogXCJicmFpbnBvb2xQMjU2dDFcIixcclxuICAgIFwiYnJhaW5wb29sUDMyMHIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjlcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjlcIjogXCJicmFpbnBvb2xQMzIwcjFcIixcclxuICAgIFwiYnJhaW5wb29sUDMyMHQxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjEwXCIsXHJcbiAgICBcIjEuMy4zNi4zLjMuMi44LjEuMS4xMFwiOiBcImJyYWlucG9vbFAzMjB0MVwiLFxyXG4gICAgXCJicmFpbnBvb2xQMzg0cjFcIjogXCIxLjMuMzYuMy4zLjIuOC4xLjEuMTFcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjExXCI6IFwiYnJhaW5wb29sUDM4NHIxXCIsXHJcbiAgICBcImJyYWlucG9vbFAzODR0MVwiOiBcIjEuMy4zNi4zLjMuMi44LjEuMS4xMlwiLFxyXG4gICAgXCIxLjMuMzYuMy4zLjIuOC4xLjEuMTJcIjogXCJicmFpbnBvb2xQMzg0dDFcIixcclxuICAgIFwiYnJhaW5wb29sUDUxMnIxXCI6IFwiMS4zLjM2LjMuMy4yLjguMS4xLjEzXCIsXHJcbiAgICBcIjEuMy4zNi4zLjMuMi44LjEuMS4xM1wiOiBcImJyYWlucG9vbFA1MTJyMVwiLFxyXG4gICAgXCJicmFpbnBvb2xQNTEydDFcIjogXCIxLjMuMzYuMy4zLjIuOC4xLjEuMTRcIixcclxuICAgIFwiMS4zLjM2LjMuMy4yLjguMS4xLjE0XCI6IFwiYnJhaW5wb29sUDUxMnQxXCIsXHJcbn07XHJcbmZ1bmN0aW9uIGdldE9pZEJ5TmFtZWRDdXJ2ZSQxKG5hbWVkQ3VydmUpIHtcclxuICAgIGNvbnN0IG9pZCA9IG5hbWVkT0lEc1tuYW1lZEN1cnZlXTtcclxuICAgIGlmICghb2lkKSB7XHJcbiAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoYENhbm5vdCBjb252ZXJ0IFdlYkNyeXB0byBuYW1lZCBjdXJ2ZSAnJHtuYW1lZEN1cnZlfScgdG8gT0lEYCk7XHJcbiAgICB9XHJcbiAgICByZXR1cm4gb2lkO1xyXG59XG5cbmNsYXNzIEVjUHJpdmF0ZUtleSBleHRlbmRzIEFzeW1tZXRyaWNLZXkge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLnR5cGUgPSBcInByaXZhdGVcIjtcclxuICAgIH1cclxuICAgIGdldEtleSgpIHtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gQXNuUGFyc2VyLnBhcnNlKHRoaXMuZGF0YSwgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKTtcclxuICAgICAgICByZXR1cm4gQXNuUGFyc2VyLnBhcnNlKGtleUluZm8ucHJpdmF0ZUtleSwgY29yZS5hc24xLkVjUHJpdmF0ZUtleSk7XHJcbiAgICB9XHJcbiAgICB0b0pTT04oKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRLZXkoKTtcclxuICAgICAgICBjb25zdCBqc29uID0ge1xyXG4gICAgICAgICAgICBrdHk6IFwiRUNcIixcclxuICAgICAgICAgICAgY3J2OiB0aGlzLmFsZ29yaXRobS5uYW1lZEN1cnZlLFxyXG4gICAgICAgICAgICBrZXlfb3BzOiB0aGlzLnVzYWdlcyxcclxuICAgICAgICAgICAgZXh0OiB0aGlzLmV4dHJhY3RhYmxlLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oanNvbiwgSnNvblNlcmlhbGl6ZXIudG9KU09OKGtleSkpO1xyXG4gICAgfVxyXG4gICAgZnJvbUpTT04oanNvbikge1xyXG4gICAgICAgIGlmICghanNvbi5jcnYpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoYENhbm5vdCBnZXQgbmFtZWQgY3VydmUgZnJvbSBKV0suIFByb3BlcnR5ICdjcnYnIGlzIHJlcXVpcmVkYCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGtleUluZm8gPSBuZXcgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKCk7XHJcbiAgICAgICAga2V5SW5mby5wcml2YXRlS2V5QWxnb3JpdGhtLmFsZ29yaXRobSA9IFwiMS4yLjg0MC4xMDA0NS4yLjFcIjtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXlBbGdvcml0aG0ucGFyYW1ldGVycyA9IEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKG5ldyBjb3JlLmFzbjEuT2JqZWN0SWRlbnRpZmllcihnZXRPaWRCeU5hbWVkQ3VydmUkMShqc29uLmNydikpKTtcclxuICAgICAgICBjb25zdCBrZXkgPSBKc29uUGFyc2VyLmZyb21KU09OKGpzb24sIHsgdGFyZ2V0U2NoZW1hOiBjb3JlLmFzbjEuRWNQcml2YXRlS2V5IH0pO1xyXG4gICAgICAgIGtleUluZm8ucHJpdmF0ZUtleSA9IEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleSk7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gQnVmZmVyLmZyb20oQXNuU2VyaWFsaXplci5zZXJpYWxpemUoa2V5SW5mbykpO1xyXG4gICAgICAgIHJldHVybiB0aGlzO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIEVjUHVibGljS2V5IGV4dGVuZHMgQXN5bW1ldHJpY0tleSB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMudHlwZSA9IFwicHVibGljXCI7XHJcbiAgICB9XHJcbiAgICBnZXRLZXkoKSB7XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZSh0aGlzLmRhdGEsIGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKTtcclxuICAgICAgICByZXR1cm4gbmV3IGNvcmUuYXNuMS5FY1B1YmxpY0tleShrZXlJbmZvLnB1YmxpY0tleSk7XHJcbiAgICB9XHJcbiAgICB0b0pTT04oKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRLZXkoKTtcclxuICAgICAgICBjb25zdCBqc29uID0ge1xyXG4gICAgICAgICAgICBrdHk6IFwiRUNcIixcclxuICAgICAgICAgICAgY3J2OiB0aGlzLmFsZ29yaXRobS5uYW1lZEN1cnZlLFxyXG4gICAgICAgICAgICBrZXlfb3BzOiB0aGlzLnVzYWdlcyxcclxuICAgICAgICAgICAgZXh0OiB0aGlzLmV4dHJhY3RhYmxlLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oanNvbiwgSnNvblNlcmlhbGl6ZXIudG9KU09OKGtleSkpO1xyXG4gICAgfVxyXG4gICAgZnJvbUpTT04oanNvbikge1xyXG4gICAgICAgIGlmICghanNvbi5jcnYpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoYENhbm5vdCBnZXQgbmFtZWQgY3VydmUgZnJvbSBKV0suIFByb3BlcnR5ICdjcnYnIGlzIHJlcXVpcmVkYCk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGtleSA9IEpzb25QYXJzZXIuZnJvbUpTT04oanNvbiwgeyB0YXJnZXRTY2hlbWE6IGNvcmUuYXNuMS5FY1B1YmxpY0tleSB9KTtcclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gbmV3IGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKCk7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXlBbGdvcml0aG0uYWxnb3JpdGhtID0gXCIxLjIuODQwLjEwMDQ1LjIuMVwiO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5QWxnb3JpdGhtLnBhcmFtZXRlcnMgPSBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShuZXcgY29yZS5hc24xLk9iamVjdElkZW50aWZpZXIoZ2V0T2lkQnlOYW1lZEN1cnZlJDEoanNvbi5jcnYpKSk7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXkgPSBBc25TZXJpYWxpemVyLnRvQVNOKGtleSkudmFsdWVIZXg7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gQnVmZmVyLmZyb20oQXNuU2VyaWFsaXplci5zZXJpYWxpemUoa2V5SW5mbykpO1xyXG4gICAgICAgIHJldHVybiB0aGlzO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIFNoYTFQcm92aWRlciBleHRlbmRzIGNvcmUuUHJvdmlkZXJDcnlwdG8ge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLm5hbWUgPSBcIlNIQS0xXCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBTaGFDcnlwdG8uZGlnZXN0KGFsZ29yaXRobSwgZGF0YSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgU2hhMjU2UHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlByb3ZpZGVyQ3J5cHRvIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5uYW1lID0gXCJTSEEtMjU2XCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBTaGFDcnlwdG8uZGlnZXN0KGFsZ29yaXRobSwgZGF0YSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgU2hhMzg0UHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlByb3ZpZGVyQ3J5cHRvIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5uYW1lID0gXCJTSEEtMzg0XCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBTaGFDcnlwdG8uZGlnZXN0KGFsZ29yaXRobSwgZGF0YSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgU2hhNTEyUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlByb3ZpZGVyQ3J5cHRvIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5uYW1lID0gXCJTSEEtNTEyXCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBTaGFDcnlwdG8uZGlnZXN0KGFsZ29yaXRobSwgZGF0YSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgU2hhMzI1NlByb3ZpZGVyIGV4dGVuZHMgY29yZS5Qcm92aWRlckNyeXB0byB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMubmFtZSA9IFwiU0hBMy0yNTZcIjtcclxuICAgICAgICB0aGlzLnVzYWdlcyA9IFtdO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EaWdlc3QoYWxnb3JpdGhtLCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIFNoYUNyeXB0by5kaWdlc3QoYWxnb3JpdGhtLCBkYXRhKTtcclxuICAgIH1cclxufVxuXG5jbGFzcyBTaGEzMzg0UHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlByb3ZpZGVyQ3J5cHRvIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5uYW1lID0gXCJTSEEzLTM4NFwiO1xyXG4gICAgICAgIHRoaXMudXNhZ2VzID0gW107XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkRpZ2VzdChhbGdvcml0aG0sIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gU2hhQ3J5cHRvLmRpZ2VzdChhbGdvcml0aG0sIGRhdGEpO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIFNoYTM1MTJQcm92aWRlciBleHRlbmRzIGNvcmUuUHJvdmlkZXJDcnlwdG8ge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLm5hbWUgPSBcIlNIQTMtNTEyXCI7XHJcbiAgICAgICAgdGhpcy51c2FnZXMgPSBbXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBTaGFDcnlwdG8uZGlnZXN0KGFsZ29yaXRobSwgZGF0YSk7XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgRWNDcnlwdG8ge1xyXG4gICAgc3RhdGljIGFzeW5jIGdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IHByaXZhdGVLZXkgPSBuZXcgRWNQcml2YXRlS2V5KCk7XHJcbiAgICAgICAgcHJpdmF0ZUtleS5hbGdvcml0aG0gPSBhbGdvcml0aG07XHJcbiAgICAgICAgcHJpdmF0ZUtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIHByaXZhdGVLZXkudXNhZ2VzID0ga2V5VXNhZ2VzLmZpbHRlcigodXNhZ2UpID0+IHRoaXMucHJpdmF0ZUtleVVzYWdlcy5pbmRleE9mKHVzYWdlKSAhPT0gLTEpO1xyXG4gICAgICAgIGNvbnN0IHB1YmxpY0tleSA9IG5ldyBFY1B1YmxpY0tleSgpO1xyXG4gICAgICAgIHB1YmxpY0tleS5hbGdvcml0aG0gPSBhbGdvcml0aG07XHJcbiAgICAgICAgcHVibGljS2V5LmV4dHJhY3RhYmxlID0gdHJ1ZTtcclxuICAgICAgICBwdWJsaWNLZXkudXNhZ2VzID0ga2V5VXNhZ2VzLmZpbHRlcigodXNhZ2UpID0+IHRoaXMucHVibGljS2V5VXNhZ2VzLmluZGV4T2YodXNhZ2UpICE9PSAtMSk7XHJcbiAgICAgICAgY29uc3Qga2V5cyA9IGNyeXB0b19fZGVmYXVsdC5nZW5lcmF0ZUtleVBhaXJTeW5jKFwiZWNcIiwge1xyXG4gICAgICAgICAgICBuYW1lZEN1cnZlOiB0aGlzLmdldE9wZW5TU0xOYW1lZEN1cnZlKGFsZ29yaXRobS5uYW1lZEN1cnZlKSxcclxuICAgICAgICAgICAgcHVibGljS2V5RW5jb2Rpbmc6IHtcclxuICAgICAgICAgICAgICAgIGZvcm1hdDogXCJkZXJcIixcclxuICAgICAgICAgICAgICAgIHR5cGU6IFwic3BraVwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBwcml2YXRlS2V5RW5jb2Rpbmc6IHtcclxuICAgICAgICAgICAgICAgIGZvcm1hdDogXCJkZXJcIixcclxuICAgICAgICAgICAgICAgIHR5cGU6IFwicGtjczhcIixcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KTtcclxuICAgICAgICBwcml2YXRlS2V5LmRhdGEgPSBrZXlzLnByaXZhdGVLZXk7XHJcbiAgICAgICAgcHVibGljS2V5LmRhdGEgPSBrZXlzLnB1YmxpY0tleTtcclxuICAgICAgICBjb25zdCByZXMgPSB7XHJcbiAgICAgICAgICAgIHByaXZhdGVLZXksXHJcbiAgICAgICAgICAgIHB1YmxpY0tleSxcclxuICAgICAgICB9O1xyXG4gICAgICAgIHJldHVybiByZXM7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgc2lnbihhbGdvcml0aG0sIGtleSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGNyeXB0b0FsZyA9IFNoYUNyeXB0by5nZXRBbGdvcml0aG1OYW1lKGFsZ29yaXRobS5oYXNoKTtcclxuICAgICAgICBjb25zdCBzaWduZXIgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlU2lnbihjcnlwdG9BbGcpO1xyXG4gICAgICAgIHNpZ25lci51cGRhdGUoQnVmZmVyLmZyb20oZGF0YSkpO1xyXG4gICAgICAgIGlmICgha2V5LnBlbSkge1xyXG4gICAgICAgICAgICBrZXkucGVtID0gYC0tLS0tQkVHSU4gUFJJVkFURSBLRVktLS0tLVxcbiR7a2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFJJVkFURSBLRVktLS0tLWA7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IG9wdGlvbnMgPSB7XHJcbiAgICAgICAgICAgIGtleToga2V5LnBlbSxcclxuICAgICAgICB9O1xyXG4gICAgICAgIGNvbnN0IHNpZ25hdHVyZSA9IHNpZ25lci5zaWduKG9wdGlvbnMpO1xyXG4gICAgICAgIGNvbnN0IGVjU2lnbmF0dXJlID0gQXNuUGFyc2VyLnBhcnNlKHNpZ25hdHVyZSwgY29yZS5hc24xLkVjRHNhU2lnbmF0dXJlKTtcclxuICAgICAgICBjb25zdCBzaWduYXR1cmVSYXcgPSBjb3JlLkVjVXRpbHMuZW5jb2RlU2lnbmF0dXJlKGVjU2lnbmF0dXJlLCBjb3JlLkVjQ3VydmVzLmdldChrZXkuYWxnb3JpdGhtLm5hbWVkQ3VydmUpLnNpemUpO1xyXG4gICAgICAgIHJldHVybiBzaWduYXR1cmVSYXcuYnVmZmVyO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIHZlcmlmeShhbGdvcml0aG0sIGtleSwgc2lnbmF0dXJlLCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3QgY3J5cHRvQWxnID0gU2hhQ3J5cHRvLmdldEFsZ29yaXRobU5hbWUoYWxnb3JpdGhtLmhhc2gpO1xyXG4gICAgICAgIGNvbnN0IHNpZ25lciA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVWZXJpZnkoY3J5cHRvQWxnKTtcclxuICAgICAgICBzaWduZXIudXBkYXRlKEJ1ZmZlci5mcm9tKGRhdGEpKTtcclxuICAgICAgICBpZiAoIWtleS5wZW0pIHtcclxuICAgICAgICAgICAga2V5LnBlbSA9IGAtLS0tLUJFR0lOIFBVQkxJQyBLRVktLS0tLVxcbiR7a2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tYDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcclxuICAgICAgICAgICAga2V5OiBrZXkucGVtLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3QgZWNTaWduYXR1cmUgPSBuZXcgY29yZS5hc24xLkVjRHNhU2lnbmF0dXJlKCk7XHJcbiAgICAgICAgY29uc3QgbmFtZWRDdXJ2ZSA9IGNvcmUuRWNDdXJ2ZXMuZ2V0KGtleS5hbGdvcml0aG0ubmFtZWRDdXJ2ZSk7XHJcbiAgICAgICAgY29uc3Qgc2lnbmF0dXJlUG9pbnQgPSBjb3JlLkVjVXRpbHMuZGVjb2RlU2lnbmF0dXJlKHNpZ25hdHVyZSwgbmFtZWRDdXJ2ZS5zaXplKTtcclxuICAgICAgICBlY1NpZ25hdHVyZS5yID0gQnVmZmVyU291cmNlQ29udmVydGVyLnRvQXJyYXlCdWZmZXIoc2lnbmF0dXJlUG9pbnQucik7XHJcbiAgICAgICAgZWNTaWduYXR1cmUucyA9IEJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b0FycmF5QnVmZmVyKHNpZ25hdHVyZVBvaW50LnMpO1xyXG4gICAgICAgIGNvbnN0IGVjU2lnbmF0dXJlUmF3ID0gQnVmZmVyLmZyb20oQXNuU2VyaWFsaXplci5zZXJpYWxpemUoZWNTaWduYXR1cmUpKTtcclxuICAgICAgICBjb25zdCBvayA9IHNpZ25lci52ZXJpZnkob3B0aW9ucywgZWNTaWduYXR1cmVSYXcpO1xyXG4gICAgICAgIHJldHVybiBvaztcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBkZXJpdmVCaXRzKGFsZ29yaXRobSwgYmFzZUtleSwgbGVuZ3RoKSB7XHJcbiAgICAgICAgY29uc3QgY3J5cHRvQWxnID0gdGhpcy5nZXRPcGVuU1NMTmFtZWRDdXJ2ZShiYXNlS2V5LmFsZ29yaXRobS5uYW1lZEN1cnZlKTtcclxuICAgICAgICBjb25zdCBlY2RoID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUVDREgoY3J5cHRvQWxnKTtcclxuICAgICAgICBjb25zdCBhc25Qcml2YXRlS2V5ID0gQXNuUGFyc2VyLnBhcnNlKGJhc2VLZXkuZGF0YSwgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKTtcclxuICAgICAgICBjb25zdCBhc25FY1ByaXZhdGVLZXkgPSBBc25QYXJzZXIucGFyc2UoYXNuUHJpdmF0ZUtleS5wcml2YXRlS2V5LCBjb3JlLmFzbjEuRWNQcml2YXRlS2V5KTtcclxuICAgICAgICBlY2RoLnNldFByaXZhdGVLZXkoQnVmZmVyLmZyb20oYXNuRWNQcml2YXRlS2V5LnByaXZhdGVLZXkpKTtcclxuICAgICAgICBjb25zdCBhc25QdWJsaWNLZXkgPSBBc25QYXJzZXIucGFyc2UoYWxnb3JpdGhtLnB1YmxpYy5kYXRhLCBjb3JlLmFzbjEuUHVibGljS2V5SW5mbyk7XHJcbiAgICAgICAgY29uc3QgYml0cyA9IGVjZGguY29tcHV0ZVNlY3JldChCdWZmZXIuZnJvbShhc25QdWJsaWNLZXkucHVibGljS2V5KSk7XHJcbiAgICAgICAgaWYgKGxlbmd0aCA9PT0gbnVsbCkge1xyXG4gICAgICAgICAgICByZXR1cm4gYml0cztcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGJpdHMpLmJ1ZmZlci5zbGljZSgwLCBsZW5ndGggPj4gMyk7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgc3dpdGNoIChmb3JtYXQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiandrXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gSnNvblNlcmlhbGl6ZXIudG9KU09OKGtleSk7XHJcbiAgICAgICAgICAgIGNhc2UgXCJwa2NzOFwiOlxyXG4gICAgICAgICAgICBjYXNlIFwic3BraVwiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGtleS5kYXRhKS5idWZmZXI7XHJcbiAgICAgICAgICAgIGNhc2UgXCJyYXdcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgcHVibGljS2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZShrZXkuZGF0YSwgY29yZS5hc24xLlB1YmxpY0tleUluZm8pO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHB1YmxpY0tleUluZm8ucHVibGljS2V5O1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImZvcm1hdDogTXVzdCBiZSAnandrJywgJ3JhdycsIHBrY3M4JyBvciAnc3BraSdcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIHN3aXRjaCAoZm9ybWF0LnRvTG93ZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcImp3a1wiOiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBqd2sgPSBrZXlEYXRhO1xyXG4gICAgICAgICAgICAgICAgaWYgKGp3ay5kKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgYXNuS2V5ID0gSnNvblBhcnNlci5mcm9tSlNPTihrZXlEYXRhLCB7IHRhcmdldFNjaGVtYTogY29yZS5hc24xLkVjUHJpdmF0ZUtleSB9KTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQcml2YXRlS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIGVsc2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFzbktleSA9IEpzb25QYXJzZXIuZnJvbUpTT04oa2V5RGF0YSwgeyB0YXJnZXRTY2hlbWE6IGNvcmUuYXNuMS5FY1B1YmxpY0tleSB9KTtcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQdWJsaWNLZXkoYXNuS2V5LCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGNhc2UgXCJyYXdcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgYXNuS2V5ID0gbmV3IGNvcmUuYXNuMS5FY1B1YmxpY0tleShrZXlEYXRhKTtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmltcG9ydFB1YmxpY0tleShhc25LZXksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgY2FzZSBcInNwa2lcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZShuZXcgVWludDhBcnJheShrZXlEYXRhKSwgY29yZS5hc24xLlB1YmxpY0tleUluZm8pO1xyXG4gICAgICAgICAgICAgICAgY29uc3QgYXNuS2V5ID0gbmV3IGNvcmUuYXNuMS5FY1B1YmxpY0tleShrZXlJbmZvLnB1YmxpY0tleSk7XHJcbiAgICAgICAgICAgICAgICB0aGlzLmFzc2VydEtleVBhcmFtZXRlcnMoa2V5SW5mby5wdWJsaWNLZXlBbGdvcml0aG0ucGFyYW1ldGVycywgYWxnb3JpdGhtLm5hbWVkQ3VydmUpO1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0UHVibGljS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjYXNlIFwicGtjczhcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZShuZXcgVWludDhBcnJheShrZXlEYXRhKSwgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKTtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGFzbktleSA9IEFzblBhcnNlci5wYXJzZShrZXlJbmZvLnByaXZhdGVLZXksIGNvcmUuYXNuMS5FY1ByaXZhdGVLZXkpO1xyXG4gICAgICAgICAgICAgICAgdGhpcy5hc3NlcnRLZXlQYXJhbWV0ZXJzKGtleUluZm8ucHJpdmF0ZUtleUFsZ29yaXRobS5wYXJhbWV0ZXJzLCBhbGdvcml0aG0ubmFtZWRDdXJ2ZSk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQcml2YXRlS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJmb3JtYXQ6IE11c3QgYmUgJ2p3aycsICdyYXcnLCAncGtjczgnIG9yICdzcGtpJ1wiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXNzZXJ0S2V5UGFyYW1ldGVycyhwYXJhbWV0ZXJzLCBuYW1lZEN1cnZlKSB7XHJcbiAgICAgICAgaWYgKCFwYXJhbWV0ZXJzKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLkNyeXB0b0Vycm9yKFwiS2V5IGluZm8gZG9lc24ndCBoYXZlIHJlcXVpcmVkIHBhcmFtZXRlcnNcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGxldCBuYW1lZEN1cnZlSWRlbnRpZmllciA9IFwiXCI7XHJcbiAgICAgICAgdHJ5IHtcclxuICAgICAgICAgICAgbmFtZWRDdXJ2ZUlkZW50aWZpZXIgPSBBc25QYXJzZXIucGFyc2UocGFyYW1ldGVycywgY29yZS5hc24xLk9iamVjdElkZW50aWZpZXIpLnZhbHVlO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjYXRjaCAoZSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5DcnlwdG9FcnJvcihcIkNhbm5vdCByZWFkIGtleSBpbmZvIHBhcmFtZXRlcnNcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChnZXRPaWRCeU5hbWVkQ3VydmUkMShuYW1lZEN1cnZlKSAhPT0gbmFtZWRDdXJ2ZUlkZW50aWZpZXIpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuQ3J5cHRvRXJyb3IoXCJLZXkgaW5mbyBwYXJhbWV0ZXIgZG9lc24ndCBtYXRjaCB0byBuYW1lZCBjdXJ2ZVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgaW1wb3J0UHJpdmF0ZUtleShhc25LZXksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleUluZm8gPSBuZXcgY29yZS5hc24xLlByaXZhdGVLZXlJbmZvKCk7XHJcbiAgICAgICAga2V5SW5mby5wcml2YXRlS2V5QWxnb3JpdGhtLmFsZ29yaXRobSA9IFwiMS4yLjg0MC4xMDA0NS4yLjFcIjtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXlBbGdvcml0aG0ucGFyYW1ldGVycyA9IEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKG5ldyBjb3JlLmFzbjEuT2JqZWN0SWRlbnRpZmllcihnZXRPaWRCeU5hbWVkQ3VydmUkMShhbGdvcml0aG0ubmFtZWRDdXJ2ZSkpKTtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXkgPSBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShhc25LZXkpO1xyXG4gICAgICAgIGNvbnN0IGtleSA9IG5ldyBFY1ByaXZhdGVLZXkoKTtcclxuICAgICAgICBrZXkuZGF0YSA9IEJ1ZmZlci5mcm9tKEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleUluZm8pKTtcclxuICAgICAgICBrZXkuYWxnb3JpdGhtID0gT2JqZWN0LmFzc2lnbih7fSwgYWxnb3JpdGhtKTtcclxuICAgICAgICBrZXkuZXh0cmFjdGFibGUgPSBleHRyYWN0YWJsZTtcclxuICAgICAgICBrZXkudXNhZ2VzID0ga2V5VXNhZ2VzO1xyXG4gICAgICAgIHJldHVybiBrZXk7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgaW1wb3J0UHVibGljS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IG5ldyBjb3JlLmFzbjEuUHVibGljS2V5SW5mbygpO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5QWxnb3JpdGhtLmFsZ29yaXRobSA9IFwiMS4yLjg0MC4xMDA0NS4yLjFcIjtcclxuICAgICAgICBjb25zdCBuYW1lZEN1cnZlID0gZ2V0T2lkQnlOYW1lZEN1cnZlJDEoYWxnb3JpdGhtLm5hbWVkQ3VydmUpO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5QWxnb3JpdGhtLnBhcmFtZXRlcnMgPSBBc25TZXJpYWxpemVyLnNlcmlhbGl6ZShuZXcgY29yZS5hc24xLk9iamVjdElkZW50aWZpZXIobmFtZWRDdXJ2ZSkpO1xyXG4gICAgICAgIGtleUluZm8ucHVibGljS2V5ID0gYXNuS2V5LnZhbHVlO1xyXG4gICAgICAgIGNvbnN0IGtleSA9IG5ldyBFY1B1YmxpY0tleSgpO1xyXG4gICAgICAgIGtleS5kYXRhID0gQnVmZmVyLmZyb20oQXNuU2VyaWFsaXplci5zZXJpYWxpemUoa2V5SW5mbykpO1xyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSBPYmplY3QuYXNzaWduKHt9LCBhbGdvcml0aG0pO1xyXG4gICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIGtleS51c2FnZXMgPSBrZXlVc2FnZXM7XHJcbiAgICAgICAgcmV0dXJuIGtleTtcclxuICAgIH1cclxuICAgIHN0YXRpYyBnZXRPcGVuU1NMTmFtZWRDdXJ2ZShjdXJ2ZSkge1xyXG4gICAgICAgIHN3aXRjaCAoY3VydmUudG9VcHBlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiUC0yNTZcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBcInByaW1lMjU2djFcIjtcclxuICAgICAgICAgICAgY2FzZSBcIkstMjU2XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzZWNwMjU2azFcIjtcclxuICAgICAgICAgICAgY2FzZSBcIlAtMzg0XCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzZWNwMzg0cjFcIjtcclxuICAgICAgICAgICAgY2FzZSBcIlAtNTIxXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gXCJzZWNwNTIxcjFcIjtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHJldHVybiBjdXJ2ZTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuRWNDcnlwdG8ucHVibGljS2V5VXNhZ2VzID0gW1widmVyaWZ5XCJdO1xyXG5FY0NyeXB0by5wcml2YXRlS2V5VXNhZ2VzID0gW1wic2lnblwiLCBcImRlcml2ZUtleVwiLCBcImRlcml2ZUJpdHNcIl07XG5cbmNsYXNzIEVjZHNhUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLkVjZHNhUHJvdmlkZXIge1xyXG4gICAgY29uc3RydWN0b3IoKSB7XHJcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcclxuICAgICAgICB0aGlzLm5hbWVkQ3VydmVzID0gY29yZS5FY0N1cnZlcy5uYW1lcztcclxuICAgICAgICB0aGlzLmhhc2hBbGdvcml0aG1zID0gW1xyXG4gICAgICAgICAgICBcIlNIQS0xXCIsIFwiU0hBLTI1NlwiLCBcIlNIQS0zODRcIiwgXCJTSEEtNTEyXCIsXHJcbiAgICAgICAgICAgIFwic2hha2UxMjhcIiwgXCJzaGFrZTI1NlwiLFxyXG4gICAgICAgICAgICBcIlNIQTMtMjU2XCIsIFwiU0hBMy0zODRcIiwgXCJTSEEzLTUxMlwiXHJcbiAgICAgICAgXTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uR2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5cyA9IGF3YWl0IEVjQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgLi4uYWxnb3JpdGhtLFxyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgcHJpdmF0ZUtleTogc2V0Q3J5cHRvS2V5KGtleXMucHJpdmF0ZUtleSksXHJcbiAgICAgICAgICAgIHB1YmxpY0tleTogc2V0Q3J5cHRvS2V5KGtleXMucHVibGljS2V5KSxcclxuICAgICAgICB9O1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25TaWduKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEVjQ3J5cHRvLnNpZ24oYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25WZXJpZnkoYWxnb3JpdGhtLCBrZXksIHNpZ25hdHVyZSwgZGF0YSkge1xyXG4gICAgICAgIHJldHVybiBFY0NyeXB0by52ZXJpZnkoYWxnb3JpdGhtLCBnZXRDcnlwdG9LZXkoa2V5KSwgbmV3IFVpbnQ4QXJyYXkoc2lnbmF0dXJlKSwgbmV3IFVpbnQ4QXJyYXkoZGF0YSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gRWNDcnlwdG8uZXhwb3J0S2V5KGZvcm1hdCwgZ2V0Q3J5cHRvS2V5KGtleSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBFY0NyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IC4uLmFsZ29yaXRobSwgbmFtZTogdGhpcy5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBjb25zdCBpbnRlcm5hbEtleSA9IGdldENyeXB0b0tleShrZXkpO1xyXG4gICAgICAgIGlmICghKGludGVybmFsS2V5IGluc3RhbmNlb2YgRWNQcml2YXRlS2V5IHx8IGludGVybmFsS2V5IGluc3RhbmNlb2YgRWNQdWJsaWNLZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBFQyBDcnlwdG9LZXlcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XG5cbmNsYXNzIEVjZGhQcm92aWRlciBleHRlbmRzIGNvcmUuRWNkaFByb3ZpZGVyIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy5uYW1lZEN1cnZlcyA9IGNvcmUuRWNDdXJ2ZXMubmFtZXM7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleXMgPSBhd2FpdCBFY0NyeXB0by5nZW5lcmF0ZUtleSh7XHJcbiAgICAgICAgICAgIC4uLmFsZ29yaXRobSxcclxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICAgIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgIHByaXZhdGVLZXk6IHNldENyeXB0b0tleShrZXlzLnByaXZhdGVLZXkpLFxyXG4gICAgICAgICAgICBwdWJsaWNLZXk6IHNldENyeXB0b0tleShrZXlzLnB1YmxpY0tleSksXHJcbiAgICAgICAgfTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgcmV0dXJuIEVjQ3J5cHRvLmV4cG9ydEtleShmb3JtYXQsIGdldENyeXB0b0tleShrZXkpKTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gYXdhaXQgRWNDcnlwdG8uaW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgeyAuLi5hbGdvcml0aG0sIG5hbWU6IHRoaXMubmFtZSB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBjaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKSB7XHJcbiAgICAgICAgc3VwZXIuY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSk7XHJcbiAgICAgICAgY29uc3QgaW50ZXJuYWxLZXkgPSBnZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgICAgICBpZiAoIShpbnRlcm5hbEtleSBpbnN0YW5jZW9mIEVjUHJpdmF0ZUtleSB8fCBpbnRlcm5hbEtleSBpbnN0YW5jZW9mIEVjUHVibGljS2V5KSkge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgVHlwZUVycm9yKFwia2V5OiBJcyBub3QgRUMgQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGFzeW5jIG9uRGVyaXZlQml0cyhhbGdvcml0aG0sIGJhc2VLZXksIGxlbmd0aCkge1xyXG4gICAgICAgIGNvbnN0IGJpdHMgPSBhd2FpdCBFY0NyeXB0by5kZXJpdmVCaXRzKHsgLi4uYWxnb3JpdGhtLCBwdWJsaWM6IGdldENyeXB0b0tleShhbGdvcml0aG0ucHVibGljKSB9LCBnZXRDcnlwdG9LZXkoYmFzZUtleSksIGxlbmd0aCk7XHJcbiAgICAgICAgcmV0dXJuIGJpdHM7XHJcbiAgICB9XHJcbn1cblxuY29uc3QgZWRPSURzID0ge1xyXG4gICAgW2NvcmUuYXNuMS5pZEVkNDQ4XTogXCJFZDQ0OFwiLFxyXG4gICAgXCJlZDQ0OFwiOiBjb3JlLmFzbjEuaWRFZDQ0OCxcclxuICAgIFtjb3JlLmFzbjEuaWRYNDQ4XTogXCJYNDQ4XCIsXHJcbiAgICBcIng0NDhcIjogY29yZS5hc24xLmlkWDQ0OCxcclxuICAgIFtjb3JlLmFzbjEuaWRFZDI1NTE5XTogXCJFZDI1NTE5XCIsXHJcbiAgICBcImVkMjU1MTlcIjogY29yZS5hc24xLmlkRWQyNTUxOSxcclxuICAgIFtjb3JlLmFzbjEuaWRYMjU1MTldOiBcIlgyNTUxOVwiLFxyXG4gICAgXCJ4MjU1MTlcIjogY29yZS5hc24xLmlkWDI1NTE5LFxyXG59O1xyXG5mdW5jdGlvbiBnZXRPaWRCeU5hbWVkQ3VydmUobmFtZWRDdXJ2ZSkge1xyXG4gICAgY29uc3Qgb2lkID0gZWRPSURzW25hbWVkQ3VydmUudG9Mb3dlckNhc2UoKV07XHJcbiAgICBpZiAoIW9pZCkge1xyXG4gICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKGBDYW5ub3QgY29udmVydCBXZWJDcnlwdG8gbmFtZWQgY3VydmUgJyR7bmFtZWRDdXJ2ZX0nIHRvIE9JRGApO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIG9pZDtcclxufVxuXG5jbGFzcyBFZFByaXZhdGVLZXkgZXh0ZW5kcyBBc3ltbWV0cmljS2V5IHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHN1cGVyKC4uLmFyZ3VtZW50cyk7XHJcbiAgICAgICAgdGhpcy50eXBlID0gXCJwcml2YXRlXCI7XHJcbiAgICB9XHJcbiAgICBnZXRLZXkoKSB7XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZSh0aGlzLmRhdGEsIGNvcmUuYXNuMS5Qcml2YXRlS2V5SW5mbyk7XHJcbiAgICAgICAgcmV0dXJuIEFzblBhcnNlci5wYXJzZShrZXlJbmZvLnByaXZhdGVLZXksIGNvcmUuYXNuMS5DdXJ2ZVByaXZhdGVLZXkpO1xyXG4gICAgfVxyXG4gICAgdG9KU09OKCkge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IHRoaXMuZ2V0S2V5KCk7XHJcbiAgICAgICAgY29uc3QganNvbiA9IHtcclxuICAgICAgICAgICAga3R5OiBcIk9LUFwiLFxyXG4gICAgICAgICAgICBjcnY6IHRoaXMuYWxnb3JpdGhtLm5hbWVkQ3VydmUsXHJcbiAgICAgICAgICAgIGtleV9vcHM6IHRoaXMudXNhZ2VzLFxyXG4gICAgICAgICAgICBleHQ6IHRoaXMuZXh0cmFjdGFibGUsXHJcbiAgICAgICAgfTtcclxuICAgICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihqc29uLCBKc29uU2VyaWFsaXplci50b0pTT04oa2V5KSk7XHJcbiAgICB9XHJcbiAgICBmcm9tSlNPTihqc29uKSB7XHJcbiAgICAgICAgaWYgKCFqc29uLmNydikge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihgQ2Fubm90IGdldCBuYW1lZCBjdXJ2ZSBmcm9tIEpXSy4gUHJvcGVydHkgJ2NydicgaXMgcmVxdWlyZWRgKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IG5ldyBjb3JlLmFzbjEuUHJpdmF0ZUtleUluZm8oKTtcclxuICAgICAgICBrZXlJbmZvLnByaXZhdGVLZXlBbGdvcml0aG0uYWxnb3JpdGhtID0gZ2V0T2lkQnlOYW1lZEN1cnZlKGpzb24uY3J2KTtcclxuICAgICAgICBjb25zdCBrZXkgPSBKc29uUGFyc2VyLmZyb21KU09OKGpzb24sIHsgdGFyZ2V0U2NoZW1hOiBjb3JlLmFzbjEuQ3VydmVQcml2YXRlS2V5IH0pO1xyXG4gICAgICAgIGtleUluZm8ucHJpdmF0ZUtleSA9IEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleSk7XHJcbiAgICAgICAgdGhpcy5kYXRhID0gQnVmZmVyLmZyb20oQXNuU2VyaWFsaXplci5zZXJpYWxpemUoa2V5SW5mbykpO1xyXG4gICAgICAgIHJldHVybiB0aGlzO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIEVkUHVibGljS2V5IGV4dGVuZHMgQXN5bW1ldHJpY0tleSB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMudHlwZSA9IFwicHVibGljXCI7XHJcbiAgICB9XHJcbiAgICBnZXRLZXkoKSB7XHJcbiAgICAgICAgY29uc3Qga2V5SW5mbyA9IEFzblBhcnNlci5wYXJzZSh0aGlzLmRhdGEsIGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKTtcclxuICAgICAgICByZXR1cm4ga2V5SW5mby5wdWJsaWNLZXk7XHJcbiAgICB9XHJcbiAgICB0b0pTT04oKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gdGhpcy5nZXRLZXkoKTtcclxuICAgICAgICBjb25zdCBqc29uID0ge1xyXG4gICAgICAgICAgICBrdHk6IFwiT0tQXCIsXHJcbiAgICAgICAgICAgIGNydjogdGhpcy5hbGdvcml0aG0ubmFtZWRDdXJ2ZSxcclxuICAgICAgICAgICAga2V5X29wczogdGhpcy51c2FnZXMsXHJcbiAgICAgICAgICAgIGV4dDogdGhpcy5leHRyYWN0YWJsZSxcclxuICAgICAgICB9O1xyXG4gICAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKGpzb24sIHtcclxuICAgICAgICAgICAgeDogQ29udmVydC5Ub0Jhc2U2NFVybChrZXkpXHJcbiAgICAgICAgfSk7XHJcbiAgICB9XHJcbiAgICBmcm9tSlNPTihqc29uKSB7XHJcbiAgICAgICAgaWYgKCFqc29uLmNydikge1xyXG4gICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihgQ2Fubm90IGdldCBuYW1lZCBjdXJ2ZSBmcm9tIEpXSy4gUHJvcGVydHkgJ2NydicgaXMgcmVxdWlyZWRgKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKCFqc29uLngpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoYENhbm5vdCBnZXQgcHJvcGVydHkgZnJvbSBKV0suIFByb3BlcnR5ICd4JyBpcyByZXF1aXJlZGApO1xyXG4gICAgICAgIH1cclxuICAgICAgICBjb25zdCBrZXlJbmZvID0gbmV3IGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKCk7XHJcbiAgICAgICAga2V5SW5mby5wdWJsaWNLZXlBbGdvcml0aG0uYWxnb3JpdGhtID0gZ2V0T2lkQnlOYW1lZEN1cnZlKGpzb24uY3J2KTtcclxuICAgICAgICBrZXlJbmZvLnB1YmxpY0tleSA9IENvbnZlcnQuRnJvbUJhc2U2NFVybChqc29uLngpO1xyXG4gICAgICAgIHRoaXMuZGF0YSA9IEJ1ZmZlci5mcm9tKEFzblNlcmlhbGl6ZXIuc2VyaWFsaXplKGtleUluZm8pKTtcclxuICAgICAgICByZXR1cm4gdGhpcztcclxuICAgIH1cclxufVxuXG5jbGFzcyBFZENyeXB0byB7XHJcbiAgICBzdGF0aWMgYXN5bmMgZ2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3QgcHJpdmF0ZUtleSA9IG5ldyBFZFByaXZhdGVLZXkoKTtcclxuICAgICAgICBwcml2YXRlS2V5LmFsZ29yaXRobSA9IGFsZ29yaXRobTtcclxuICAgICAgICBwcml2YXRlS2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAgcHJpdmF0ZUtleS51c2FnZXMgPSBrZXlVc2FnZXMuZmlsdGVyKCh1c2FnZSkgPT4gdGhpcy5wcml2YXRlS2V5VXNhZ2VzLmluZGV4T2YodXNhZ2UpICE9PSAtMSk7XHJcbiAgICAgICAgY29uc3QgcHVibGljS2V5ID0gbmV3IEVkUHVibGljS2V5KCk7XHJcbiAgICAgICAgcHVibGljS2V5LmFsZ29yaXRobSA9IGFsZ29yaXRobTtcclxuICAgICAgICBwdWJsaWNLZXkuZXh0cmFjdGFibGUgPSB0cnVlO1xyXG4gICAgICAgIHB1YmxpY0tleS51c2FnZXMgPSBrZXlVc2FnZXMuZmlsdGVyKCh1c2FnZSkgPT4gdGhpcy5wdWJsaWNLZXlVc2FnZXMuaW5kZXhPZih1c2FnZSkgIT09IC0xKTtcclxuICAgICAgICBjb25zdCB0eXBlID0gYWxnb3JpdGhtLm5hbWVkQ3VydmUudG9Mb3dlckNhc2UoKTtcclxuICAgICAgICBjb25zdCBrZXlzID0gY3J5cHRvX19kZWZhdWx0LmdlbmVyYXRlS2V5UGFpclN5bmModHlwZSwge1xyXG4gICAgICAgICAgICBwdWJsaWNLZXlFbmNvZGluZzoge1xyXG4gICAgICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJzcGtpXCIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHByaXZhdGVLZXlFbmNvZGluZzoge1xyXG4gICAgICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICAgICAgdHlwZTogXCJwa2NzOFwiLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHByaXZhdGVLZXkuZGF0YSA9IGtleXMucHJpdmF0ZUtleTtcclxuICAgICAgICBwdWJsaWNLZXkuZGF0YSA9IGtleXMucHVibGljS2V5O1xyXG4gICAgICAgIGNvbnN0IHJlcyA9IHtcclxuICAgICAgICAgICAgcHJpdmF0ZUtleSxcclxuICAgICAgICAgICAgcHVibGljS2V5LFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgcmV0dXJuIHJlcztcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBzaWduKGFsZ29yaXRobSwga2V5LCBkYXRhKSB7XHJcbiAgICAgICAgaWYgKCFrZXkucGVtKSB7XHJcbiAgICAgICAgICAgIGtleS5wZW0gPSBgLS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tXFxuJHtrZXkuZGF0YS50b1N0cmluZyhcImJhc2U2NFwiKX1cXG4tLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tYDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcclxuICAgICAgICAgICAga2V5OiBrZXkucGVtLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3Qgc2lnbmF0dXJlID0gY3J5cHRvX19kZWZhdWx0LnNpZ24obnVsbCwgQnVmZmVyLmZyb20oZGF0YSksIG9wdGlvbnMpO1xyXG4gICAgICAgIHJldHVybiBjb3JlLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b0FycmF5QnVmZmVyKHNpZ25hdHVyZSk7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgdmVyaWZ5KGFsZ29yaXRobSwga2V5LCBzaWduYXR1cmUsIGRhdGEpIHtcclxuICAgICAgICBpZiAoIWtleS5wZW0pIHtcclxuICAgICAgICAgICAga2V5LnBlbSA9IGAtLS0tLUJFR0lOIFBVQkxJQyBLRVktLS0tLVxcbiR7a2V5LmRhdGEudG9TdHJpbmcoXCJiYXNlNjRcIil9XFxuLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tYDtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcclxuICAgICAgICAgICAga2V5OiBrZXkucGVtLFxyXG4gICAgICAgIH07XHJcbiAgICAgICAgY29uc3Qgb2sgPSBjcnlwdG9fX2RlZmF1bHQudmVyaWZ5KG51bGwsIEJ1ZmZlci5mcm9tKGRhdGEpLCBvcHRpb25zLCBCdWZmZXIuZnJvbShzaWduYXR1cmUpKTtcclxuICAgICAgICByZXR1cm4gb2s7XHJcbiAgICB9XHJcbiAgICBzdGF0aWMgYXN5bmMgZGVyaXZlQml0cyhhbGdvcml0aG0sIGJhc2VLZXksIGxlbmd0aCkge1xyXG4gICAgICAgIGNvbnN0IHB1YmxpY0tleSA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVQdWJsaWNLZXkoe1xyXG4gICAgICAgICAgICBrZXk6IGFsZ29yaXRobS5wdWJsaWMuZGF0YSxcclxuICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICB0eXBlOiBcInNwa2lcIixcclxuICAgICAgICB9KTtcclxuICAgICAgICBjb25zdCBwcml2YXRlS2V5ID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZVByaXZhdGVLZXkoe1xyXG4gICAgICAgICAgICBrZXk6IGJhc2VLZXkuZGF0YSxcclxuICAgICAgICAgICAgZm9ybWF0OiBcImRlclwiLFxyXG4gICAgICAgICAgICB0eXBlOiBcInBrY3M4XCIsXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAgY29uc3QgYml0cyA9IGNyeXB0b19fZGVmYXVsdC5kaWZmaWVIZWxsbWFuKHtcclxuICAgICAgICAgICAgcHVibGljS2V5LFxyXG4gICAgICAgICAgICBwcml2YXRlS2V5LFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShiaXRzKS5idWZmZXIuc2xpY2UoMCwgbGVuZ3RoID4+IDMpO1xyXG4gICAgfVxyXG4gICAgc3RhdGljIGFzeW5jIGV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIHN3aXRjaCAoZm9ybWF0LnRvTG93ZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcImp3a1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIEpzb25TZXJpYWxpemVyLnRvSlNPTihrZXkpO1xyXG4gICAgICAgICAgICBjYXNlIFwicGtjczhcIjpcclxuICAgICAgICAgICAgY2FzZSBcInNwa2lcIjpcclxuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShrZXkuZGF0YSkuYnVmZmVyO1xyXG4gICAgICAgICAgICBjYXNlIFwicmF3XCI6IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHB1YmxpY0tleUluZm8gPSBBc25QYXJzZXIucGFyc2Uoa2V5LmRhdGEsIGNvcmUuYXNuMS5QdWJsaWNLZXlJbmZvKTtcclxuICAgICAgICAgICAgICAgIHJldHVybiBwdWJsaWNLZXlJbmZvLnB1YmxpY0tleTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJmb3JtYXQ6IE11c3QgYmUgJ2p3aycsICdyYXcnLCBwa2NzOCcgb3IgJ3Nwa2knXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBpbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBzd2l0Y2ggKGZvcm1hdC50b0xvd2VyQ2FzZSgpKSB7XHJcbiAgICAgICAgICAgIGNhc2UgXCJqd2tcIjoge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgandrID0ga2V5RGF0YTtcclxuICAgICAgICAgICAgICAgIGlmIChqd2suZCkge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGFzbktleSA9IEpzb25QYXJzZXIuZnJvbUpTT04oa2V5RGF0YSwgeyB0YXJnZXRTY2hlbWE6IGNvcmUuYXNuMS5DdXJ2ZVByaXZhdGVLZXkgfSk7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0UHJpdmF0ZUtleShhc25LZXksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICBpZiAoIWp3ay54KSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXlEYXRhOiBDYW5ub3QgZ2V0IHJlcXVpcmVkICd4JyBmaWxlZFwiKTtcclxuICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuaW1wb3J0UHVibGljS2V5KENvbnZlcnQuRnJvbUJhc2U2NFVybChqd2sueCksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgY2FzZSBcInJhd1wiOiB7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQdWJsaWNLZXkoa2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBjYXNlIFwic3BraVwiOiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBrZXlJbmZvID0gQXNuUGFyc2VyLnBhcnNlKG5ldyBVaW50OEFycmF5KGtleURhdGEpLCBjb3JlLmFzbjEuUHVibGljS2V5SW5mbyk7XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdGhpcy5pbXBvcnRQdWJsaWNLZXkoa2V5SW5mby5wdWJsaWNLZXksIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgY2FzZSBcInBrY3M4XCI6IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGtleUluZm8gPSBBc25QYXJzZXIucGFyc2UobmV3IFVpbnQ4QXJyYXkoa2V5RGF0YSksIGNvcmUuYXNuMS5Qcml2YXRlS2V5SW5mbyk7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBhc25LZXkgPSBBc25QYXJzZXIucGFyc2Uoa2V5SW5mby5wcml2YXRlS2V5LCBjb3JlLmFzbjEuQ3VydmVQcml2YXRlS2V5KTtcclxuICAgICAgICAgICAgICAgIHJldHVybiB0aGlzLmltcG9ydFByaXZhdGVLZXkoYXNuS2V5LCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgY29yZS5PcGVyYXRpb25FcnJvcihcImZvcm1hdDogTXVzdCBiZSAnandrJywgJ3JhdycsICdwa2NzOCcgb3IgJ3Nwa2knXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIHN0YXRpYyBpbXBvcnRQcml2YXRlS2V5KGFzbktleSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5ID0gbmV3IEVkUHJpdmF0ZUtleSgpO1xyXG4gICAgICAgIGtleS5mcm9tSlNPTih7XHJcbiAgICAgICAgICAgIGNydjogYWxnb3JpdGhtLm5hbWVkQ3VydmUsXHJcbiAgICAgICAgICAgIGQ6IENvbnZlcnQuVG9CYXNlNjRVcmwoYXNuS2V5LmQpLFxyXG4gICAgICAgIH0pO1xyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSBPYmplY3QuYXNzaWduKHt9LCBhbGdvcml0aG0pO1xyXG4gICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIGtleS51c2FnZXMgPSBrZXlVc2FnZXM7XHJcbiAgICAgICAgcmV0dXJuIGtleTtcclxuICAgIH1cclxuICAgIHN0YXRpYyBhc3luYyBpbXBvcnRQdWJsaWNLZXkoYXNuS2V5LCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBuZXcgRWRQdWJsaWNLZXkoKTtcclxuICAgICAgICBrZXkuZnJvbUpTT04oe1xyXG4gICAgICAgICAgICBjcnY6IGFsZ29yaXRobS5uYW1lZEN1cnZlLFxyXG4gICAgICAgICAgICB4OiBDb252ZXJ0LlRvQmFzZTY0VXJsKGFzbktleSksXHJcbiAgICAgICAgfSk7XHJcbiAgICAgICAga2V5LmFsZ29yaXRobSA9IE9iamVjdC5hc3NpZ24oe30sIGFsZ29yaXRobSk7XHJcbiAgICAgICAga2V5LmV4dHJhY3RhYmxlID0gZXh0cmFjdGFibGU7XHJcbiAgICAgICAga2V5LnVzYWdlcyA9IGtleVVzYWdlcztcclxuICAgICAgICByZXR1cm4ga2V5O1xyXG4gICAgfVxyXG59XHJcbkVkQ3J5cHRvLnB1YmxpY0tleVVzYWdlcyA9IFtcInZlcmlmeVwiXTtcclxuRWRDcnlwdG8ucHJpdmF0ZUtleVVzYWdlcyA9IFtcInNpZ25cIiwgXCJkZXJpdmVLZXlcIiwgXCJkZXJpdmVCaXRzXCJdO1xuXG5jbGFzcyBFZERzYVByb3ZpZGVyIGV4dGVuZHMgY29yZS5FZERzYVByb3ZpZGVyIHtcclxuICAgIGFzeW5jIG9uR2VuZXJhdGVLZXkoYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgY29uc3Qga2V5cyA9IGF3YWl0IEVkQ3J5cHRvLmdlbmVyYXRlS2V5KHtcclxuICAgICAgICAgICAgbmFtZTogdGhpcy5uYW1lLFxyXG4gICAgICAgICAgICBuYW1lZEN1cnZlOiBhbGdvcml0aG0ubmFtZWRDdXJ2ZS5yZXBsYWNlKC9eZWQvaSwgXCJFZFwiKSxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBwcml2YXRlS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wcml2YXRlS2V5KSxcclxuICAgICAgICAgICAgcHVibGljS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wdWJsaWNLZXkpLFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICBhc3luYyBvblNpZ24oYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICByZXR1cm4gRWRDcnlwdG8uc2lnbihhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvblZlcmlmeShhbGdvcml0aG0sIGtleSwgc2lnbmF0dXJlLCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIEVkQ3J5cHRvLnZlcmlmeShhbGdvcml0aG0sIGdldENyeXB0b0tleShrZXkpLCBuZXcgVWludDhBcnJheShzaWduYXR1cmUpLCBuZXcgVWludDhBcnJheShkYXRhKSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkV4cG9ydEtleShmb3JtYXQsIGtleSkge1xyXG4gICAgICAgIHJldHVybiBFZENyeXB0by5leHBvcnRLZXkoZm9ybWF0LCBnZXRDcnlwdG9LZXkoa2V5KSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleSA9IGF3YWl0IEVkQ3J5cHRvLmltcG9ydEtleShmb3JtYXQsIGtleURhdGEsIHsgLi4uYWxnb3JpdGhtLCBuYW1lOiB0aGlzLm5hbWUgfSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcyk7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIEVjZGhFc1Byb3ZpZGVyIGV4dGVuZHMgY29yZS5FY2RoRXNQcm92aWRlciB7XHJcbiAgICBhc3luYyBvbkdlbmVyYXRlS2V5KGFsZ29yaXRobSwgZXh0cmFjdGFibGUsIGtleVVzYWdlcykge1xyXG4gICAgICAgIGNvbnN0IGtleXMgPSBhd2FpdCBFZENyeXB0by5nZW5lcmF0ZUtleSh7XHJcbiAgICAgICAgICAgIG5hbWU6IHRoaXMubmFtZSxcclxuICAgICAgICAgICAgbmFtZWRDdXJ2ZTogYWxnb3JpdGhtLm5hbWVkQ3VydmUudG9VcHBlckNhc2UoKSxcclxuICAgICAgICB9LCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKTtcclxuICAgICAgICByZXR1cm4ge1xyXG4gICAgICAgICAgICBwcml2YXRlS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wcml2YXRlS2V5KSxcclxuICAgICAgICAgICAgcHVibGljS2V5OiBzZXRDcnlwdG9LZXkoa2V5cy5wdWJsaWNLZXkpLFxyXG4gICAgICAgIH07XHJcbiAgICB9XHJcbiAgICBhc3luYyBvbkRlcml2ZUJpdHMoYWxnb3JpdGhtLCBiYXNlS2V5LCBsZW5ndGgpIHtcclxuICAgICAgICBjb25zdCBiaXRzID0gYXdhaXQgRWRDcnlwdG8uZGVyaXZlQml0cyh7IC4uLmFsZ29yaXRobSwgcHVibGljOiBnZXRDcnlwdG9LZXkoYWxnb3JpdGhtLnB1YmxpYykgfSwgZ2V0Q3J5cHRvS2V5KGJhc2VLZXkpLCBsZW5ndGgpO1xyXG4gICAgICAgIHJldHVybiBiaXRzO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25FeHBvcnRLZXkoZm9ybWF0LCBrZXkpIHtcclxuICAgICAgICByZXR1cm4gRWRDcnlwdG8uZXhwb3J0S2V5KGZvcm1hdCwgZ2V0Q3J5cHRvS2V5KGtleSkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBrZXkgPSBhd2FpdCBFZENyeXB0by5pbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCB7IC4uLmFsZ29yaXRobSwgbmFtZTogdGhpcy5uYW1lIH0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxufVxuXG5jbGFzcyBQYmtkZkNyeXB0b0tleSBleHRlbmRzIENyeXB0b0tleSB7XHJcbn1cblxuY2xhc3MgUGJrZGYyUHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlBia2RmMlByb3ZpZGVyIHtcclxuICAgIGFzeW5jIG9uRGVyaXZlQml0cyhhbGdvcml0aG0sIGJhc2VLZXksIGxlbmd0aCkge1xyXG4gICAgICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHNhbHQgPSBjb3JlLkJ1ZmZlclNvdXJjZUNvbnZlcnRlci50b0FycmF5QnVmZmVyKGFsZ29yaXRobS5zYWx0KTtcclxuICAgICAgICAgICAgY29uc3QgaGFzaCA9IGFsZ29yaXRobS5oYXNoLm5hbWUucmVwbGFjZShcIi1cIiwgXCJcIik7XHJcbiAgICAgICAgICAgIGNyeXB0b19fZGVmYXVsdC5wYmtkZjIoZ2V0Q3J5cHRvS2V5KGJhc2VLZXkpLmRhdGEsIEJ1ZmZlci5mcm9tKHNhbHQpLCBhbGdvcml0aG0uaXRlcmF0aW9ucywgbGVuZ3RoID4+IDMsIGhhc2gsIChlcnIsIGRlcml2ZWRCaXRzKSA9PiB7XHJcbiAgICAgICAgICAgICAgICBpZiAoZXJyKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmVqZWN0KGVycik7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICByZXNvbHZlKG5ldyBVaW50OEFycmF5KGRlcml2ZWRCaXRzKS5idWZmZXIpO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9KTtcclxuICAgICAgICB9KTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uSW1wb3J0S2V5KGZvcm1hdCwga2V5RGF0YSwgYWxnb3JpdGhtLCBleHRyYWN0YWJsZSwga2V5VXNhZ2VzKSB7XHJcbiAgICAgICAgaWYgKGZvcm1hdCA9PT0gXCJyYXdcIikge1xyXG4gICAgICAgICAgICBjb25zdCBrZXkgPSBuZXcgUGJrZGZDcnlwdG9LZXkoKTtcclxuICAgICAgICAgICAga2V5LmRhdGEgPSBCdWZmZXIuZnJvbShrZXlEYXRhKTtcclxuICAgICAgICAgICAga2V5LmFsZ29yaXRobSA9IHsgbmFtZTogdGhpcy5uYW1lIH07XHJcbiAgICAgICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGZhbHNlO1xyXG4gICAgICAgICAgICBrZXkudXNhZ2VzID0ga2V5VXNhZ2VzO1xyXG4gICAgICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiZm9ybWF0OiBNdXN0IGJlICdyYXcnXCIpO1xyXG4gICAgfVxyXG4gICAgY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSkge1xyXG4gICAgICAgIHN1cGVyLmNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpO1xyXG4gICAgICAgIGlmICghKGdldENyeXB0b0tleShrZXkpIGluc3RhbmNlb2YgUGJrZGZDcnlwdG9LZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBQQktERiBDcnlwdG9LZXlcIik7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XG5cbmNsYXNzIEhtYWNDcnlwdG9LZXkgZXh0ZW5kcyBDcnlwdG9LZXkge1xyXG4gICAgZ2V0IGFsZygpIHtcclxuICAgICAgICBjb25zdCBoYXNoID0gdGhpcy5hbGdvcml0aG0uaGFzaC5uYW1lLnRvVXBwZXJDYXNlKCk7XHJcbiAgICAgICAgcmV0dXJuIGBIUyR7aGFzaC5yZXBsYWNlKFwiU0hBLVwiLCBcIlwiKX1gO1xyXG4gICAgfVxyXG4gICAgc2V0IGFsZyh2YWx1ZSkge1xyXG4gICAgfVxyXG59XHJcbl9fZGVjb3JhdGUoW1xyXG4gICAgSnNvblByb3AoeyBuYW1lOiBcImtcIiwgY29udmVydGVyOiBKc29uQmFzZTY0VXJsQ29udmVydGVyIH0pXHJcbl0sIEhtYWNDcnlwdG9LZXkucHJvdG90eXBlLCBcImRhdGFcIiwgdm9pZCAwKTtcblxuY2xhc3MgSG1hY1Byb3ZpZGVyIGV4dGVuZHMgY29yZS5IbWFjUHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25HZW5lcmF0ZUtleShhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBjb25zdCBsZW5ndGggPSAoYWxnb3JpdGhtLmxlbmd0aCB8fCB0aGlzLmdldERlZmF1bHRMZW5ndGgoYWxnb3JpdGhtLmhhc2gubmFtZSkpID4+IDMgPDwgMztcclxuICAgICAgICBjb25zdCBrZXkgPSBuZXcgSG1hY0NyeXB0b0tleSgpO1xyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSB7XHJcbiAgICAgICAgICAgIC4uLmFsZ29yaXRobSxcclxuICAgICAgICAgICAgbGVuZ3RoLFxyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgfTtcclxuICAgICAgICBrZXkuZXh0cmFjdGFibGUgPSBleHRyYWN0YWJsZTtcclxuICAgICAgICBrZXkudXNhZ2VzID0ga2V5VXNhZ2VzO1xyXG4gICAgICAgIGtleS5kYXRhID0gY3J5cHRvX19kZWZhdWx0LnJhbmRvbUJ5dGVzKGxlbmd0aCA+PiAzKTtcclxuICAgICAgICByZXR1cm4gc2V0Q3J5cHRvS2V5KGtleSk7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvblNpZ24oYWxnb3JpdGhtLCBrZXksIGRhdGEpIHtcclxuICAgICAgICBjb25zdCBjcnlwdG9BbGcgPSBTaGFDcnlwdG8uZ2V0QWxnb3JpdGhtTmFtZShrZXkuYWxnb3JpdGhtLmhhc2gpO1xyXG4gICAgICAgIGNvbnN0IGhtYWMgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlSG1hYyhjcnlwdG9BbGcsIGdldENyeXB0b0tleShrZXkpLmRhdGEpXHJcbiAgICAgICAgICAgIC51cGRhdGUoQnVmZmVyLmZyb20oZGF0YSkpLmRpZ2VzdCgpO1xyXG4gICAgICAgIHJldHVybiBuZXcgVWludDhBcnJheShobWFjKS5idWZmZXI7XHJcbiAgICB9XHJcbiAgICBhc3luYyBvblZlcmlmeShhbGdvcml0aG0sIGtleSwgc2lnbmF0dXJlLCBkYXRhKSB7XHJcbiAgICAgICAgY29uc3QgY3J5cHRvQWxnID0gU2hhQ3J5cHRvLmdldEFsZ29yaXRobU5hbWUoa2V5LmFsZ29yaXRobS5oYXNoKTtcclxuICAgICAgICBjb25zdCBobWFjID0gY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUhtYWMoY3J5cHRvQWxnLCBnZXRDcnlwdG9LZXkoa2V5KS5kYXRhKVxyXG4gICAgICAgICAgICAudXBkYXRlKEJ1ZmZlci5mcm9tKGRhdGEpKS5kaWdlc3QoKTtcclxuICAgICAgICByZXR1cm4gaG1hYy5jb21wYXJlKEJ1ZmZlci5mcm9tKHNpZ25hdHVyZSkpID09PSAwO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBsZXQga2V5O1xyXG4gICAgICAgIHN3aXRjaCAoZm9ybWF0LnRvTG93ZXJDYXNlKCkpIHtcclxuICAgICAgICAgICAgY2FzZSBcImp3a1wiOlxyXG4gICAgICAgICAgICAgICAga2V5ID0gSnNvblBhcnNlci5mcm9tSlNPTihrZXlEYXRhLCB7IHRhcmdldFNjaGVtYTogSG1hY0NyeXB0b0tleSB9KTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIFwicmF3XCI6XHJcbiAgICAgICAgICAgICAgICBrZXkgPSBuZXcgSG1hY0NyeXB0b0tleSgpO1xyXG4gICAgICAgICAgICAgICAga2V5LmRhdGEgPSBCdWZmZXIuZnJvbShrZXlEYXRhKTtcclxuICAgICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBkZWZhdWx0OlxyXG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJmb3JtYXQ6IE11c3QgYmUgJ2p3aycgb3IgJ3JhdydcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSB7XHJcbiAgICAgICAgICAgIGhhc2g6IHsgbmFtZTogYWxnb3JpdGhtLmhhc2gubmFtZSB9LFxyXG4gICAgICAgICAgICBuYW1lOiB0aGlzLm5hbWUsXHJcbiAgICAgICAgICAgIGxlbmd0aDoga2V5LmRhdGEubGVuZ3RoIDw8IDMsXHJcbiAgICAgICAgfTtcclxuICAgICAgICBrZXkuZXh0cmFjdGFibGUgPSBleHRyYWN0YWJsZTtcclxuICAgICAgICBrZXkudXNhZ2VzID0ga2V5VXNhZ2VzO1xyXG4gICAgICAgIHJldHVybiBzZXRDcnlwdG9LZXkoa2V5KTtcclxuICAgIH1cclxuICAgIGFzeW5jIG9uRXhwb3J0S2V5KGZvcm1hdCwga2V5KSB7XHJcbiAgICAgICAgc3dpdGNoIChmb3JtYXQudG9Mb3dlckNhc2UoKSkge1xyXG4gICAgICAgICAgICBjYXNlIFwiandrXCI6XHJcbiAgICAgICAgICAgICAgICByZXR1cm4gSnNvblNlcmlhbGl6ZXIudG9KU09OKGdldENyeXB0b0tleShrZXkpKTtcclxuICAgICAgICAgICAgY2FzZSBcInJhd1wiOlxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBVaW50OEFycmF5KGdldENyeXB0b0tleShrZXkpLmRhdGEpLmJ1ZmZlcjtcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBjb3JlLk9wZXJhdGlvbkVycm9yKFwiZm9ybWF0OiBNdXN0IGJlICdqd2snIG9yICdyYXcnXCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIGNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpIHtcclxuICAgICAgICBzdXBlci5jaGVja0NyeXB0b0tleShrZXksIGtleVVzYWdlKTtcclxuICAgICAgICBpZiAoIShnZXRDcnlwdG9LZXkoa2V5KSBpbnN0YW5jZW9mIEhtYWNDcnlwdG9LZXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJrZXk6IElzIG5vdCBITUFDIENyeXB0b0tleVwiKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cblxuY2xhc3MgSGtkZkNyeXB0b0tleSBleHRlbmRzIENyeXB0b0tleSB7XHJcbn1cblxuY2xhc3MgSGtkZlByb3ZpZGVyIGV4dGVuZHMgY29yZS5Ia2RmUHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25JbXBvcnRLZXkoZm9ybWF0LCBrZXlEYXRhLCBhbGdvcml0aG0sIGV4dHJhY3RhYmxlLCBrZXlVc2FnZXMpIHtcclxuICAgICAgICBpZiAoZm9ybWF0LnRvTG93ZXJDYXNlKCkgIT09IFwicmF3XCIpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IGNvcmUuT3BlcmF0aW9uRXJyb3IoXCJPcGVyYXRpb24gbm90IHN1cHBvcnRlZFwiKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgY29uc3Qga2V5ID0gbmV3IEhrZGZDcnlwdG9LZXkoKTtcclxuICAgICAgICBrZXkuZGF0YSA9IEJ1ZmZlci5mcm9tKGtleURhdGEpO1xyXG4gICAgICAgIGtleS5hbGdvcml0aG0gPSB7IG5hbWU6IHRoaXMubmFtZSB9O1xyXG4gICAgICAgIGtleS5leHRyYWN0YWJsZSA9IGV4dHJhY3RhYmxlO1xyXG4gICAgICAgIGtleS51c2FnZXMgPSBrZXlVc2FnZXM7XHJcbiAgICAgICAgcmV0dXJuIHNldENyeXB0b0tleShrZXkpO1xyXG4gICAgfVxyXG4gICAgYXN5bmMgb25EZXJpdmVCaXRzKHBhcmFtcywgYmFzZUtleSwgbGVuZ3RoKSB7XHJcbiAgICAgICAgY29uc3QgaGFzaCA9IHBhcmFtcy5oYXNoLm5hbWUucmVwbGFjZShcIi1cIiwgXCJcIik7XHJcbiAgICAgICAgY29uc3QgaGFzaExlbmd0aCA9IGNyeXB0b19fZGVmYXVsdC5jcmVhdGVIYXNoKGhhc2gpLmRpZ2VzdCgpLmxlbmd0aDtcclxuICAgICAgICBjb25zdCBieXRlTGVuZ3RoID0gbGVuZ3RoIC8gODtcclxuICAgICAgICBjb25zdCBpbmZvID0gQnVmZmVyU291cmNlQ29udmVydGVyJDEudG9VaW50OEFycmF5KHBhcmFtcy5pbmZvKTtcclxuICAgICAgICBjb25zdCBQUksgPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlSG1hYyhoYXNoLCBCdWZmZXJTb3VyY2VDb252ZXJ0ZXIkMS50b1VpbnQ4QXJyYXkocGFyYW1zLnNhbHQpKVxyXG4gICAgICAgICAgICAudXBkYXRlKEJ1ZmZlclNvdXJjZUNvbnZlcnRlciQxLnRvVWludDhBcnJheShnZXRDcnlwdG9LZXkoYmFzZUtleSkuZGF0YSkpXHJcbiAgICAgICAgICAgIC5kaWdlc3QoKTtcclxuICAgICAgICBjb25zdCBibG9ja3MgPSBbQnVmZmVyLmFsbG9jKDApXTtcclxuICAgICAgICBjb25zdCBibG9ja0NvdW50ID0gTWF0aC5jZWlsKGJ5dGVMZW5ndGggLyBoYXNoTGVuZ3RoKSArIDE7XHJcbiAgICAgICAgZm9yIChsZXQgaSA9IDE7IGkgPCBibG9ja0NvdW50OyArK2kpIHtcclxuICAgICAgICAgICAgYmxvY2tzLnB1c2goY3J5cHRvX19kZWZhdWx0LmNyZWF0ZUhtYWMoaGFzaCwgUFJLKVxyXG4gICAgICAgICAgICAgICAgLnVwZGF0ZShCdWZmZXIuY29uY2F0KFtibG9ja3NbaSAtIDFdLCBpbmZvLCBCdWZmZXIuZnJvbShbaV0pXSkpXHJcbiAgICAgICAgICAgICAgICAuZGlnZXN0KCkpO1xyXG4gICAgICAgIH1cclxuICAgICAgICByZXR1cm4gQnVmZmVyLmNvbmNhdChibG9ja3MpLnNsaWNlKDAsIGJ5dGVMZW5ndGgpO1xyXG4gICAgfVxyXG4gICAgY2hlY2tDcnlwdG9LZXkoa2V5LCBrZXlVc2FnZSkge1xyXG4gICAgICAgIHN1cGVyLmNoZWNrQ3J5cHRvS2V5KGtleSwga2V5VXNhZ2UpO1xyXG4gICAgICAgIGlmICghKGdldENyeXB0b0tleShrZXkpIGluc3RhbmNlb2YgSGtkZkNyeXB0b0tleSkpIHtcclxuICAgICAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihcImtleTogSXMgbm90IEhLREYgQ3J5cHRvS2V5XCIpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBTaGFrZUNyeXB0byB7XHJcbiAgICBzdGF0aWMgZGlnZXN0KGFsZ29yaXRobSwgZGF0YSkge1xyXG4gICAgICAgIGNvbnN0IGhhc2ggPSBjcnlwdG9fX2RlZmF1bHQuY3JlYXRlSGFzaChhbGdvcml0aG0ubmFtZS50b0xvd2VyQ2FzZSgpLCB7IG91dHB1dExlbmd0aDogYWxnb3JpdGhtLmxlbmd0aCB9KVxyXG4gICAgICAgICAgICAudXBkYXRlKEJ1ZmZlci5mcm9tKGRhdGEpKS5kaWdlc3QoKTtcclxuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoaGFzaCkuYnVmZmVyO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIFNoYWtlMTI4UHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlNoYWtlMTI4UHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25EaWdlc3QoYWxnb3JpdGhtLCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIFNoYWtlQ3J5cHRvLmRpZ2VzdChhbGdvcml0aG0sIGRhdGEpO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIFNoYWtlMjU2UHJvdmlkZXIgZXh0ZW5kcyBjb3JlLlNoYWtlMjU2UHJvdmlkZXIge1xyXG4gICAgYXN5bmMgb25EaWdlc3QoYWxnb3JpdGhtLCBkYXRhKSB7XHJcbiAgICAgICAgcmV0dXJuIFNoYWtlQ3J5cHRvLmRpZ2VzdChhbGdvcml0aG0sIGRhdGEpO1xyXG4gICAgfVxyXG59XG5cbmNsYXNzIFN1YnRsZUNyeXB0byBleHRlbmRzIGNvcmUuU3VidGxlQ3J5cHRvIHtcclxuICAgIGNvbnN0cnVjdG9yKCkge1xyXG4gICAgICAgIHZhciBfYTtcclxuICAgICAgICBzdXBlcigpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgQWVzQ2JjUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBBZXNDdHJQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEFlc0djbVByb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgQWVzQ21hY1Byb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgQWVzS3dQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEFlc0VjYlByb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgRGVzQ2JjUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBEZXNFZGUzQ2JjUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBSc2FTc2FQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IFJzYVBzc1Byb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgUnNhT2FlcFByb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgUnNhRXNQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEVjZHNhUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBFY2RoUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBTaGExUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBTaGEyNTZQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IFNoYTM4NFByb3ZpZGVyKCkpO1xyXG4gICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgU2hhNTEyUHJvdmlkZXIoKSk7XHJcbiAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBQYmtkZjJQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEhtYWNQcm92aWRlcigpKTtcclxuICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEhrZGZQcm92aWRlcigpKTtcclxuICAgICAgICBjb25zdCBub2RlTWFqb3JWZXJzaW9uID0gKF9hID0gL152KFxcZCspLy5leGVjKHByb2Nlc3MudmVyc2lvbikpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYVsxXTtcclxuICAgICAgICBpZiAobm9kZU1ham9yVmVyc2lvbiAmJiBwYXJzZUludChub2RlTWFqb3JWZXJzaW9uLCAxMCkgPj0gMTIpIHtcclxuICAgICAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBTaGFrZTEyOFByb3ZpZGVyKCkpO1xyXG4gICAgICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IFNoYWtlMjU2UHJvdmlkZXIoKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGhhc2hlcyA9IGNyeXB0by5nZXRIYXNoZXMoKTtcclxuICAgICAgICBpZiAoaGFzaGVzLmluY2x1ZGVzKFwic2hhMy0yNTZcIikpIHtcclxuICAgICAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBTaGEzMjU2UHJvdmlkZXIoKSk7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmIChoYXNoZXMuaW5jbHVkZXMoXCJzaGEzLTM4NFwiKSkge1xyXG4gICAgICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IFNoYTMzODRQcm92aWRlcigpKTtcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGhhc2hlcy5pbmNsdWRlcyhcInNoYTMtNTEyXCIpKSB7XHJcbiAgICAgICAgICAgIHRoaXMucHJvdmlkZXJzLnNldChuZXcgU2hhMzUxMlByb3ZpZGVyKCkpO1xyXG4gICAgICAgIH1cclxuICAgICAgICBpZiAobm9kZU1ham9yVmVyc2lvbiAmJiBwYXJzZUludChub2RlTWFqb3JWZXJzaW9uLCAxMCkgPj0gMTQpIHtcclxuICAgICAgICAgICAgdGhpcy5wcm92aWRlcnMuc2V0KG5ldyBFZERzYVByb3ZpZGVyKCkpO1xyXG4gICAgICAgICAgICB0aGlzLnByb3ZpZGVycy5zZXQobmV3IEVjZGhFc1Byb3ZpZGVyKCkpO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxuXG5jbGFzcyBDcnlwdG8gZXh0ZW5kcyBjb3JlLkNyeXB0byB7XHJcbiAgICBjb25zdHJ1Y3RvcigpIHtcclxuICAgICAgICBzdXBlciguLi5hcmd1bWVudHMpO1xyXG4gICAgICAgIHRoaXMuc3VidGxlID0gbmV3IFN1YnRsZUNyeXB0bygpO1xyXG4gICAgfVxyXG4gICAgZ2V0UmFuZG9tVmFsdWVzKGFycmF5KSB7XHJcbiAgICAgICAgaWYgKCFBcnJheUJ1ZmZlci5pc1ZpZXcoYXJyYXkpKSB7XHJcbiAgICAgICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJGYWlsZWQgdG8gZXhlY3V0ZSAnZ2V0UmFuZG9tVmFsdWVzJyBvbiAnQ3J5cHRvJzogcGFyYW1ldGVyIDEgaXMgbm90IG9mIHR5cGUgJ0FycmF5QnVmZmVyVmlldydcIik7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGNvbnN0IGJ1ZmZlciA9IEJ1ZmZlci5mcm9tKGFycmF5LmJ1ZmZlciwgYXJyYXkuYnl0ZU9mZnNldCwgYXJyYXkuYnl0ZUxlbmd0aCk7XHJcbiAgICAgICAgY3J5cHRvX19kZWZhdWx0LnJhbmRvbUZpbGxTeW5jKGJ1ZmZlcik7XHJcbiAgICAgICAgcmV0dXJuIGFycmF5O1xyXG4gICAgfVxyXG59XG5cbmV4cG9ydCB7IENyeXB0byB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/@peculiar/webcrypto/build/webcrypto.es.js\n");

/***/ })

};
;