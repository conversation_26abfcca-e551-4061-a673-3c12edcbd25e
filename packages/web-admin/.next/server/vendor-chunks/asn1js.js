"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/asn1js";
exports.ids = ["vendor-chunks/asn1js"];
exports.modules = {

/***/ "(rsc)/../../node_modules/asn1js/build/index.es.js":
/*!***************************************************!*\
  !*** ../../node_modules/asn1js/build/index.es.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Any: () => (/* binding */ Any),\n/* harmony export */   BaseBlock: () => (/* binding */ BaseBlock),\n/* harmony export */   BaseStringBlock: () => (/* binding */ BaseStringBlock),\n/* harmony export */   BitString: () => (/* binding */ BitString),\n/* harmony export */   BmpString: () => (/* binding */ BmpString),\n/* harmony export */   Boolean: () => (/* binding */ Boolean),\n/* harmony export */   CharacterString: () => (/* binding */ CharacterString),\n/* harmony export */   Choice: () => (/* binding */ Choice),\n/* harmony export */   Constructed: () => (/* binding */ Constructed),\n/* harmony export */   DATE: () => (/* binding */ DATE),\n/* harmony export */   DateTime: () => (/* binding */ DateTime),\n/* harmony export */   Duration: () => (/* binding */ Duration),\n/* harmony export */   EndOfContent: () => (/* binding */ EndOfContent),\n/* harmony export */   Enumerated: () => (/* binding */ Enumerated),\n/* harmony export */   GeneralString: () => (/* binding */ GeneralString),\n/* harmony export */   GeneralizedTime: () => (/* binding */ GeneralizedTime),\n/* harmony export */   GraphicString: () => (/* binding */ GraphicString),\n/* harmony export */   HexBlock: () => (/* binding */ HexBlock),\n/* harmony export */   IA5String: () => (/* binding */ IA5String),\n/* harmony export */   Integer: () => (/* binding */ Integer),\n/* harmony export */   Null: () => (/* binding */ Null),\n/* harmony export */   NumericString: () => (/* binding */ NumericString),\n/* harmony export */   ObjectIdentifier: () => (/* binding */ ObjectIdentifier),\n/* harmony export */   OctetString: () => (/* binding */ OctetString),\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   PrintableString: () => (/* binding */ PrintableString),\n/* harmony export */   RawData: () => (/* binding */ RawData),\n/* harmony export */   RelativeObjectIdentifier: () => (/* binding */ RelativeObjectIdentifier),\n/* harmony export */   Repeated: () => (/* binding */ Repeated),\n/* harmony export */   Sequence: () => (/* binding */ Sequence),\n/* harmony export */   Set: () => (/* binding */ Set),\n/* harmony export */   TIME: () => (/* binding */ TIME),\n/* harmony export */   TeletexString: () => (/* binding */ TeletexString),\n/* harmony export */   TimeOfDay: () => (/* binding */ TimeOfDay),\n/* harmony export */   UTCTime: () => (/* binding */ UTCTime),\n/* harmony export */   UniversalString: () => (/* binding */ UniversalString),\n/* harmony export */   Utf8String: () => (/* binding */ Utf8String),\n/* harmony export */   ValueBlock: () => (/* binding */ ValueBlock),\n/* harmony export */   VideotexString: () => (/* binding */ VideotexString),\n/* harmony export */   ViewWriter: () => (/* binding */ ViewWriter),\n/* harmony export */   VisibleString: () => (/* binding */ VisibleString),\n/* harmony export */   compareSchema: () => (/* binding */ compareSchema),\n/* harmony export */   fromBER: () => (/* binding */ fromBER),\n/* harmony export */   verifySchema: () => (/* binding */ verifySchema)\n/* harmony export */ });\n/* harmony import */ var pvtsutils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pvtsutils */ \"(rsc)/../../node_modules/pvtsutils/build/index.es.js\");\n/* harmony import */ var pvutils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pvutils */ \"(rsc)/../../node_modules/pvutils/build/utils.es.js\");\n/*!\n * Copyright (c) 2014, GMO GlobalSign\n * Copyright (c) 2015-2022, Peculiar Ventures\n * All rights reserved.\n * \n * Author 2014-2019, Yury Strozhevsky\n * \n * Redistribution and use in source and binary forms, with or without modification,\n * are permitted provided that the following conditions are met:\n * \n * * Redistributions of source code must retain the above copyright notice, this\n *   list of conditions and the following disclaimer.\n * \n * * Redistributions in binary form must reproduce the above copyright notice, this\n *   list of conditions and the following disclaimer in the documentation and/or\n *   other materials provided with the distribution.\n * \n * * Neither the name of the copyright holder nor the names of its\n *   contributors may be used to endorse or promote products derived from\n *   this software without specific prior written permission.\n * \n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\" AND\n * ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED\n * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE\n * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR\n * ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES\n * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;\n * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT\n * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n * \n */\n\n\n\n\nfunction assertBigInt() {\n    if (typeof BigInt === \"undefined\") {\n        throw new Error(\"BigInt is not defined. Your environment doesn't implement BigInt.\");\n    }\n}\nfunction concat(buffers) {\n    let outputLength = 0;\n    let prevLength = 0;\n    for (let i = 0; i < buffers.length; i++) {\n        const buffer = buffers[i];\n        outputLength += buffer.byteLength;\n    }\n    const retView = new Uint8Array(outputLength);\n    for (let i = 0; i < buffers.length; i++) {\n        const buffer = buffers[i];\n        retView.set(new Uint8Array(buffer), prevLength);\n        prevLength += buffer.byteLength;\n    }\n    return retView.buffer;\n}\nfunction checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength) {\n    if (!(inputBuffer instanceof Uint8Array)) {\n        baseBlock.error = \"Wrong parameter: inputBuffer must be 'Uint8Array'\";\n        return false;\n    }\n    if (!inputBuffer.byteLength) {\n        baseBlock.error = \"Wrong parameter: inputBuffer has zero length\";\n        return false;\n    }\n    if (inputOffset < 0) {\n        baseBlock.error = \"Wrong parameter: inputOffset less than zero\";\n        return false;\n    }\n    if (inputLength < 0) {\n        baseBlock.error = \"Wrong parameter: inputLength less than zero\";\n        return false;\n    }\n    if ((inputBuffer.byteLength - inputOffset - inputLength) < 0) {\n        baseBlock.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n        return false;\n    }\n    return true;\n}\n\nclass ViewWriter {\n    constructor() {\n        this.items = [];\n    }\n    write(buf) {\n        this.items.push(buf);\n    }\n    final() {\n        return concat(this.items);\n    }\n}\n\nconst powers2 = [new Uint8Array([1])];\nconst digitsString = \"0123456789\";\nconst NAME = \"name\";\nconst VALUE_HEX_VIEW = \"valueHexView\";\nconst IS_HEX_ONLY = \"isHexOnly\";\nconst ID_BLOCK = \"idBlock\";\nconst TAG_CLASS = \"tagClass\";\nconst TAG_NUMBER = \"tagNumber\";\nconst IS_CONSTRUCTED = \"isConstructed\";\nconst FROM_BER = \"fromBER\";\nconst TO_BER = \"toBER\";\nconst LOCAL = \"local\";\nconst EMPTY_STRING = \"\";\nconst EMPTY_BUFFER = new ArrayBuffer(0);\nconst EMPTY_VIEW = new Uint8Array(0);\nconst END_OF_CONTENT_NAME = \"EndOfContent\";\nconst OCTET_STRING_NAME = \"OCTET STRING\";\nconst BIT_STRING_NAME = \"BIT STRING\";\n\nfunction HexBlock(BaseClass) {\n    var _a;\n    return _a = class Some extends BaseClass {\n            get valueHex() {\n                return this.valueHexView.slice().buffer;\n            }\n            set valueHex(value) {\n                this.valueHexView = new Uint8Array(value);\n            }\n            constructor(...args) {\n                var _b;\n                super(...args);\n                const params = args[0] || {};\n                this.isHexOnly = (_b = params.isHexOnly) !== null && _b !== void 0 ? _b : false;\n                this.valueHexView = params.valueHex ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(params.valueHex) : EMPTY_VIEW;\n            }\n            fromBER(inputBuffer, inputOffset, inputLength) {\n                const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n                if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n                    return -1;\n                }\n                const endLength = inputOffset + inputLength;\n                this.valueHexView = view.subarray(inputOffset, endLength);\n                if (!this.valueHexView.length) {\n                    this.warnings.push(\"Zero buffer length\");\n                    return inputOffset;\n                }\n                this.blockLength = inputLength;\n                return endLength;\n            }\n            toBER(sizeOnly = false) {\n                if (!this.isHexOnly) {\n                    this.error = \"Flag 'isHexOnly' is not set, abort\";\n                    return EMPTY_BUFFER;\n                }\n                if (sizeOnly) {\n                    return new ArrayBuffer(this.valueHexView.byteLength);\n                }\n                return (this.valueHexView.byteLength === this.valueHexView.buffer.byteLength)\n                    ? this.valueHexView.buffer\n                    : this.valueHexView.slice().buffer;\n            }\n            toJSON() {\n                return {\n                    ...super.toJSON(),\n                    isHexOnly: this.isHexOnly,\n                    valueHex: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView),\n                };\n            }\n        },\n        _a.NAME = \"hexBlock\",\n        _a;\n}\n\nclass LocalBaseBlock {\n    static blockName() {\n        return this.NAME;\n    }\n    get valueBeforeDecode() {\n        return this.valueBeforeDecodeView.slice().buffer;\n    }\n    set valueBeforeDecode(value) {\n        this.valueBeforeDecodeView = new Uint8Array(value);\n    }\n    constructor({ blockLength = 0, error = EMPTY_STRING, warnings = [], valueBeforeDecode = EMPTY_VIEW, } = {}) {\n        this.blockLength = blockLength;\n        this.error = error;\n        this.warnings = warnings;\n        this.valueBeforeDecodeView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(valueBeforeDecode);\n    }\n    toJSON() {\n        return {\n            blockName: this.constructor.NAME,\n            blockLength: this.blockLength,\n            error: this.error,\n            warnings: this.warnings,\n            valueBeforeDecode: pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBeforeDecodeView),\n        };\n    }\n}\nLocalBaseBlock.NAME = \"baseBlock\";\n\nclass ValueBlock extends LocalBaseBlock {\n    fromBER(_inputBuffer, _inputOffset, _inputLength) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n    toBER(_sizeOnly, _writer) {\n        throw TypeError(\"User need to make a specific function in a class which extends 'ValueBlock'\");\n    }\n}\nValueBlock.NAME = \"valueBlock\";\n\nclass LocalIdentificationBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ idBlock = {} } = {}) {\n        var _a, _b, _c, _d;\n        super();\n        if (idBlock) {\n            this.isHexOnly = (_a = idBlock.isHexOnly) !== null && _a !== void 0 ? _a : false;\n            this.valueHexView = idBlock.valueHex\n                ? pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(idBlock.valueHex)\n                : EMPTY_VIEW;\n            this.tagClass = (_b = idBlock.tagClass) !== null && _b !== void 0 ? _b : -1;\n            this.tagNumber = (_c = idBlock.tagNumber) !== null && _c !== void 0 ? _c : -1;\n            this.isConstructed = (_d = idBlock.isConstructed) !== null && _d !== void 0 ? _d : false;\n        }\n        else {\n            this.tagClass = -1;\n            this.tagNumber = -1;\n            this.isConstructed = false;\n        }\n    }\n    toBER(sizeOnly = false) {\n        let firstOctet = 0;\n        switch (this.tagClass) {\n            case 1:\n                firstOctet |= 0x00;\n                break;\n            case 2:\n                firstOctet |= 0x40;\n                break;\n            case 3:\n                firstOctet |= 0x80;\n                break;\n            case 4:\n                firstOctet |= 0xC0;\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return EMPTY_BUFFER;\n        }\n        if (this.isConstructed)\n            firstOctet |= 0x20;\n        if (this.tagNumber < 31 && !this.isHexOnly) {\n            const retView = new Uint8Array(1);\n            if (!sizeOnly) {\n                let number = this.tagNumber;\n                number &= 0x1F;\n                firstOctet |= number;\n                retView[0] = firstOctet;\n            }\n            return retView.buffer;\n        }\n        if (!this.isHexOnly) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.tagNumber, 7);\n            const encodedView = new Uint8Array(encodedBuf);\n            const size = encodedBuf.byteLength;\n            const retView = new Uint8Array(size + 1);\n            retView[0] = (firstOctet | 0x1F);\n            if (!sizeOnly) {\n                for (let i = 0; i < (size - 1); i++)\n                    retView[i + 1] = encodedView[i] | 0x80;\n                retView[size] = encodedView[size - 1];\n            }\n            return retView.buffer;\n        }\n        const retView = new Uint8Array(this.valueHexView.byteLength + 1);\n        retView[0] = (firstOctet | 0x1F);\n        if (!sizeOnly) {\n            const curView = this.valueHexView;\n            for (let i = 0; i < (curView.length - 1); i++)\n                retView[i + 1] = curView[i] | 0x80;\n            retView[this.valueHexView.byteLength] = curView[curView.length - 1];\n        }\n        return retView.buffer;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        const tagClassMask = intBuffer[0] & 0xC0;\n        switch (tagClassMask) {\n            case 0x00:\n                this.tagClass = (1);\n                break;\n            case 0x40:\n                this.tagClass = (2);\n                break;\n            case 0x80:\n                this.tagClass = (3);\n                break;\n            case 0xC0:\n                this.tagClass = (4);\n                break;\n            default:\n                this.error = \"Unknown tag class\";\n                return -1;\n        }\n        this.isConstructed = (intBuffer[0] & 0x20) === 0x20;\n        this.isHexOnly = false;\n        const tagNumberMask = intBuffer[0] & 0x1F;\n        if (tagNumberMask !== 0x1F) {\n            this.tagNumber = (tagNumberMask);\n            this.blockLength = 1;\n        }\n        else {\n            let count = 1;\n            let intTagNumberBuffer = this.valueHexView = new Uint8Array(255);\n            let tagNumberBufferMaxLength = 255;\n            while (intBuffer[count] & 0x80) {\n                intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n                count++;\n                if (count >= intBuffer.length) {\n                    this.error = \"End of input reached before message was fully decoded\";\n                    return -1;\n                }\n                if (count === tagNumberBufferMaxLength) {\n                    tagNumberBufferMaxLength += 255;\n                    const tempBufferView = new Uint8Array(tagNumberBufferMaxLength);\n                    for (let i = 0; i < intTagNumberBuffer.length; i++)\n                        tempBufferView[i] = intTagNumberBuffer[i];\n                    intTagNumberBuffer = this.valueHexView = new Uint8Array(tagNumberBufferMaxLength);\n                }\n            }\n            this.blockLength = (count + 1);\n            intTagNumberBuffer[count - 1] = intBuffer[count] & 0x7F;\n            const tempBufferView = new Uint8Array(count);\n            for (let i = 0; i < count; i++)\n                tempBufferView[i] = intTagNumberBuffer[i];\n            intTagNumberBuffer = this.valueHexView = new Uint8Array(count);\n            intTagNumberBuffer.set(tempBufferView);\n            if (this.blockLength <= 9)\n                this.tagNumber = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(intTagNumberBuffer, 7);\n            else {\n                this.isHexOnly = true;\n                this.warnings.push(\"Tag too long, represented as hex-coded\");\n            }\n        }\n        if (((this.tagClass === 1))\n            && (this.isConstructed)) {\n            switch (this.tagNumber) {\n                case 1:\n                case 2:\n                case 5:\n                case 6:\n                case 9:\n                case 13:\n                case 14:\n                case 23:\n                case 24:\n                case 31:\n                case 32:\n                case 33:\n                case 34:\n                    this.error = \"Constructed encoding used for primitive type\";\n                    return -1;\n            }\n        }\n        return (inputOffset + this.blockLength);\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            tagClass: this.tagClass,\n            tagNumber: this.tagNumber,\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalIdentificationBlock.NAME = \"identificationBlock\";\n\nclass LocalLengthBlock extends LocalBaseBlock {\n    constructor({ lenBlock = {} } = {}) {\n        var _a, _b, _c;\n        super();\n        this.isIndefiniteForm = (_a = lenBlock.isIndefiniteForm) !== null && _a !== void 0 ? _a : false;\n        this.longFormUsed = (_b = lenBlock.longFormUsed) !== null && _b !== void 0 ? _b : false;\n        this.length = (_c = lenBlock.length) !== null && _c !== void 0 ? _c : 0;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = view.subarray(inputOffset, inputOffset + inputLength);\n        if (intBuffer.length === 0) {\n            this.error = \"Zero buffer length\";\n            return -1;\n        }\n        if (intBuffer[0] === 0xFF) {\n            this.error = \"Length block 0xFF is reserved by standard\";\n            return -1;\n        }\n        this.isIndefiniteForm = intBuffer[0] === 0x80;\n        if (this.isIndefiniteForm) {\n            this.blockLength = 1;\n            return (inputOffset + this.blockLength);\n        }\n        this.longFormUsed = !!(intBuffer[0] & 0x80);\n        if (this.longFormUsed === false) {\n            this.length = (intBuffer[0]);\n            this.blockLength = 1;\n            return (inputOffset + this.blockLength);\n        }\n        const count = intBuffer[0] & 0x7F;\n        if (count > 8) {\n            this.error = \"Too big integer\";\n            return -1;\n        }\n        if ((count + 1) > intBuffer.length) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        const lenOffset = inputOffset + 1;\n        const lengthBufferView = view.subarray(lenOffset, lenOffset + count);\n        if (lengthBufferView[count - 1] === 0x00)\n            this.warnings.push(\"Needlessly long encoded length\");\n        this.length = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(lengthBufferView, 8);\n        if (this.longFormUsed && (this.length <= 127))\n            this.warnings.push(\"Unnecessary usage of long length form\");\n        this.blockLength = count + 1;\n        return (inputOffset + this.blockLength);\n    }\n    toBER(sizeOnly = false) {\n        let retBuf;\n        let retView;\n        if (this.length > 127)\n            this.longFormUsed = true;\n        if (this.isIndefiniteForm) {\n            retBuf = new ArrayBuffer(1);\n            if (sizeOnly === false) {\n                retView = new Uint8Array(retBuf);\n                retView[0] = 0x80;\n            }\n            return retBuf;\n        }\n        if (this.longFormUsed) {\n            const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.length, 8);\n            if (encodedBuf.byteLength > 127) {\n                this.error = \"Too big length\";\n                return (EMPTY_BUFFER);\n            }\n            retBuf = new ArrayBuffer(encodedBuf.byteLength + 1);\n            if (sizeOnly)\n                return retBuf;\n            const encodedView = new Uint8Array(encodedBuf);\n            retView = new Uint8Array(retBuf);\n            retView[0] = encodedBuf.byteLength | 0x80;\n            for (let i = 0; i < encodedBuf.byteLength; i++)\n                retView[i + 1] = encodedView[i];\n            return retBuf;\n        }\n        retBuf = new ArrayBuffer(1);\n        if (sizeOnly === false) {\n            retView = new Uint8Array(retBuf);\n            retView[0] = this.length;\n        }\n        return retBuf;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            longFormUsed: this.longFormUsed,\n            length: this.length,\n        };\n    }\n}\nLocalLengthBlock.NAME = \"lengthBlock\";\n\nconst typeStore = {};\n\nclass BaseBlock extends LocalBaseBlock {\n    constructor({ name = EMPTY_STRING, optional = false, primitiveSchema, ...parameters } = {}, valueBlockType) {\n        super(parameters);\n        this.name = name;\n        this.optional = optional;\n        if (primitiveSchema) {\n            this.primitiveSchema = primitiveSchema;\n        }\n        this.idBlock = new LocalIdentificationBlock(parameters);\n        this.lenBlock = new LocalLengthBlock(parameters);\n        this.valueBlock = valueBlockType ? new valueBlockType(parameters) : new ValueBlock(parameters);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm)\n            ? inputLength\n            : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        if (!writer) {\n            prepareIndefiniteForm(this);\n        }\n        const idBlockBuf = this.idBlock.toBER(sizeOnly);\n        _writer.write(idBlockBuf);\n        if (this.lenBlock.isIndefiniteForm) {\n            _writer.write(new Uint8Array([0x80]).buffer);\n            this.valueBlock.toBER(sizeOnly, _writer);\n            _writer.write(new ArrayBuffer(2));\n        }\n        else {\n            const valueBlockBuf = this.valueBlock.toBER(sizeOnly);\n            this.lenBlock.length = valueBlockBuf.byteLength;\n            const lenBlockBuf = this.lenBlock.toBER(sizeOnly);\n            _writer.write(lenBlockBuf);\n            _writer.write(valueBlockBuf);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            idBlock: this.idBlock.toJSON(),\n            lenBlock: this.lenBlock.toJSON(),\n            valueBlock: this.valueBlock.toJSON(),\n            name: this.name,\n            optional: this.optional,\n        };\n        if (this.primitiveSchema)\n            object.primitiveSchema = this.primitiveSchema.toJSON();\n        return object;\n    }\n    toString(encoding = \"ascii\") {\n        if (encoding === \"ascii\") {\n            return this.onAsciiEncoding();\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.toBER());\n    }\n    onAsciiEncoding() {\n        const name = this.constructor.NAME;\n        const value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueBeforeDecodeView);\n        return `${name} : ${value}`;\n    }\n    isEqual(other) {\n        if (this === other) {\n            return true;\n        }\n        if (!(other instanceof this.constructor)) {\n            return false;\n        }\n        const thisRaw = this.toBER();\n        const otherRaw = other.toBER();\n        return pvutils__WEBPACK_IMPORTED_MODULE_1__.isEqualBuffer(thisRaw, otherRaw);\n    }\n}\nBaseBlock.NAME = \"BaseBlock\";\nfunction prepareIndefiniteForm(baseBlock) {\n    var _a;\n    if (baseBlock instanceof typeStore.Constructed) {\n        for (const value of baseBlock.valueBlock.value) {\n            if (prepareIndefiniteForm(value)) {\n                baseBlock.lenBlock.isIndefiniteForm = true;\n            }\n        }\n    }\n    return !!((_a = baseBlock.lenBlock) === null || _a === void 0 ? void 0 : _a.isIndefiniteForm);\n}\n\nclass BaseStringBlock extends BaseBlock {\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    constructor({ value = EMPTY_STRING, ...parameters } = {}, stringValueBlockType) {\n        super(parameters, stringValueBlockType);\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm)\n            ? inputLength\n            : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        this.fromBuffer(this.valueBlock.valueHexView);\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : '${this.valueBlock.value}'`;\n    }\n}\nBaseStringBlock.NAME = \"BaseStringBlock\";\n\nclass LocalPrimitiveValueBlock extends HexBlock(ValueBlock) {\n    constructor({ isHexOnly = true, ...parameters } = {}) {\n        super(parameters);\n        this.isHexOnly = isHexOnly;\n    }\n}\nLocalPrimitiveValueBlock.NAME = \"PrimitiveValueBlock\";\n\nvar _a$w;\nclass Primitive extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalPrimitiveValueBlock);\n        this.idBlock.isConstructed = false;\n    }\n}\n_a$w = Primitive;\n(() => {\n    typeStore.Primitive = _a$w;\n})();\nPrimitive.NAME = \"PRIMITIVE\";\n\nfunction localChangeType(inputObject, newType) {\n    if (inputObject instanceof newType) {\n        return inputObject;\n    }\n    const newObject = new newType();\n    newObject.idBlock = inputObject.idBlock;\n    newObject.lenBlock = inputObject.lenBlock;\n    newObject.warnings = inputObject.warnings;\n    newObject.valueBeforeDecodeView = inputObject.valueBeforeDecodeView;\n    return newObject;\n}\nfunction localFromBER(inputBuffer, inputOffset = 0, inputLength = inputBuffer.length) {\n    const incomingOffset = inputOffset;\n    let returnObject = new BaseBlock({}, ValueBlock);\n    const baseBlock = new LocalBaseBlock();\n    if (!checkBufferParams(baseBlock, inputBuffer, inputOffset, inputLength)) {\n        returnObject.error = baseBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    const intBuffer = inputBuffer.subarray(inputOffset, inputOffset + inputLength);\n    if (!intBuffer.length) {\n        returnObject.error = \"Zero buffer length\";\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    let resultOffset = returnObject.idBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.idBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.idBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.idBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.idBlock.blockLength;\n    resultOffset = returnObject.lenBlock.fromBER(inputBuffer, inputOffset, inputLength);\n    if (returnObject.lenBlock.warnings.length) {\n        returnObject.warnings.concat(returnObject.lenBlock.warnings);\n    }\n    if (resultOffset === -1) {\n        returnObject.error = returnObject.lenBlock.error;\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    inputOffset = resultOffset;\n    inputLength -= returnObject.lenBlock.blockLength;\n    if (!returnObject.idBlock.isConstructed\n        && returnObject.lenBlock.isIndefiniteForm) {\n        returnObject.error = \"Indefinite length form used for primitive encoding form\";\n        return {\n            offset: -1,\n            result: returnObject,\n        };\n    }\n    let newASN1Type = BaseBlock;\n    switch (returnObject.idBlock.tagClass) {\n        case 1:\n            if ((returnObject.idBlock.tagNumber >= 37)\n                && (returnObject.idBlock.isHexOnly === false)) {\n                returnObject.error = \"UNIVERSAL 37 and upper tags are reserved by ASN.1 standard\";\n                return {\n                    offset: -1,\n                    result: returnObject,\n                };\n            }\n            switch (returnObject.idBlock.tagNumber) {\n                case 0:\n                    if ((returnObject.idBlock.isConstructed)\n                        && (returnObject.lenBlock.length > 0)) {\n                        returnObject.error = \"Type [UNIVERSAL 0] is reserved\";\n                        return {\n                            offset: -1,\n                            result: returnObject,\n                        };\n                    }\n                    newASN1Type = typeStore.EndOfContent;\n                    break;\n                case 1:\n                    newASN1Type = typeStore.Boolean;\n                    break;\n                case 2:\n                    newASN1Type = typeStore.Integer;\n                    break;\n                case 3:\n                    newASN1Type = typeStore.BitString;\n                    break;\n                case 4:\n                    newASN1Type = typeStore.OctetString;\n                    break;\n                case 5:\n                    newASN1Type = typeStore.Null;\n                    break;\n                case 6:\n                    newASN1Type = typeStore.ObjectIdentifier;\n                    break;\n                case 10:\n                    newASN1Type = typeStore.Enumerated;\n                    break;\n                case 12:\n                    newASN1Type = typeStore.Utf8String;\n                    break;\n                case 13:\n                    newASN1Type = typeStore.RelativeObjectIdentifier;\n                    break;\n                case 14:\n                    newASN1Type = typeStore.TIME;\n                    break;\n                case 15:\n                    returnObject.error = \"[UNIVERSAL 15] is reserved by ASN.1 standard\";\n                    return {\n                        offset: -1,\n                        result: returnObject,\n                    };\n                case 16:\n                    newASN1Type = typeStore.Sequence;\n                    break;\n                case 17:\n                    newASN1Type = typeStore.Set;\n                    break;\n                case 18:\n                    newASN1Type = typeStore.NumericString;\n                    break;\n                case 19:\n                    newASN1Type = typeStore.PrintableString;\n                    break;\n                case 20:\n                    newASN1Type = typeStore.TeletexString;\n                    break;\n                case 21:\n                    newASN1Type = typeStore.VideotexString;\n                    break;\n                case 22:\n                    newASN1Type = typeStore.IA5String;\n                    break;\n                case 23:\n                    newASN1Type = typeStore.UTCTime;\n                    break;\n                case 24:\n                    newASN1Type = typeStore.GeneralizedTime;\n                    break;\n                case 25:\n                    newASN1Type = typeStore.GraphicString;\n                    break;\n                case 26:\n                    newASN1Type = typeStore.VisibleString;\n                    break;\n                case 27:\n                    newASN1Type = typeStore.GeneralString;\n                    break;\n                case 28:\n                    newASN1Type = typeStore.UniversalString;\n                    break;\n                case 29:\n                    newASN1Type = typeStore.CharacterString;\n                    break;\n                case 30:\n                    newASN1Type = typeStore.BmpString;\n                    break;\n                case 31:\n                    newASN1Type = typeStore.DATE;\n                    break;\n                case 32:\n                    newASN1Type = typeStore.TimeOfDay;\n                    break;\n                case 33:\n                    newASN1Type = typeStore.DateTime;\n                    break;\n                case 34:\n                    newASN1Type = typeStore.Duration;\n                    break;\n                default: {\n                    const newObject = returnObject.idBlock.isConstructed\n                        ? new typeStore.Constructed()\n                        : new typeStore.Primitive();\n                    newObject.idBlock = returnObject.idBlock;\n                    newObject.lenBlock = returnObject.lenBlock;\n                    newObject.warnings = returnObject.warnings;\n                    returnObject = newObject;\n                }\n            }\n            break;\n        case 2:\n        case 3:\n        case 4:\n        default: {\n            newASN1Type = returnObject.idBlock.isConstructed\n                ? typeStore.Constructed\n                : typeStore.Primitive;\n        }\n    }\n    returnObject = localChangeType(returnObject, newASN1Type);\n    resultOffset = returnObject.fromBER(inputBuffer, inputOffset, returnObject.lenBlock.isIndefiniteForm ? inputLength : returnObject.lenBlock.length);\n    returnObject.valueBeforeDecodeView = inputBuffer.subarray(incomingOffset, incomingOffset + returnObject.blockLength);\n    return {\n        offset: resultOffset,\n        result: returnObject,\n    };\n}\nfunction fromBER(inputBuffer) {\n    if (!inputBuffer.byteLength) {\n        const result = new BaseBlock({}, ValueBlock);\n        result.error = \"Input buffer has zero length\";\n        return {\n            offset: -1,\n            result,\n        };\n    }\n    return localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).slice(), 0, inputBuffer.byteLength);\n}\n\nfunction checkLen(indefiniteLength, length) {\n    if (indefiniteLength) {\n        return 1;\n    }\n    return length;\n}\nclass LocalConstructedValueBlock extends ValueBlock {\n    constructor({ value = [], isIndefiniteForm = false, ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n        this.isIndefiniteForm = isIndefiniteForm;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const view = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, view, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueBeforeDecodeView = view.subarray(inputOffset, inputOffset + inputLength);\n        if (this.valueBeforeDecodeView.length === 0) {\n            this.warnings.push(\"Zero buffer length\");\n            return inputOffset;\n        }\n        let currentOffset = inputOffset;\n        while (checkLen(this.isIndefiniteForm, inputLength) > 0) {\n            const returnObject = localFromBER(view, currentOffset, inputLength);\n            if (returnObject.offset === -1) {\n                this.error = returnObject.result.error;\n                this.warnings.concat(returnObject.result.warnings);\n                return -1;\n            }\n            currentOffset = returnObject.offset;\n            this.blockLength += returnObject.result.blockLength;\n            inputLength -= returnObject.result.blockLength;\n            this.value.push(returnObject.result);\n            if (this.isIndefiniteForm && returnObject.result.constructor.NAME === END_OF_CONTENT_NAME) {\n                break;\n            }\n        }\n        if (this.isIndefiniteForm) {\n            if (this.value[this.value.length - 1].constructor.NAME === END_OF_CONTENT_NAME) {\n                this.value.pop();\n            }\n            else {\n                this.warnings.push(\"No EndOfContent block encoded\");\n            }\n        }\n        return currentOffset;\n    }\n    toBER(sizeOnly, writer) {\n        const _writer = writer || new ViewWriter();\n        for (let i = 0; i < this.value.length; i++) {\n            this.value[i].toBER(sizeOnly, _writer);\n        }\n        if (!writer) {\n            return _writer.final();\n        }\n        return EMPTY_BUFFER;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            isIndefiniteForm: this.isIndefiniteForm,\n            value: [],\n        };\n        for (const value of this.value) {\n            object.value.push(value.toJSON());\n        }\n        return object;\n    }\n}\nLocalConstructedValueBlock.NAME = \"ConstructedValueBlock\";\n\nvar _a$v;\nclass Constructed extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalConstructedValueBlock);\n        this.idBlock.isConstructed = true;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        const resultOffset = this.valueBlock.fromBER(inputBuffer, inputOffset, (this.lenBlock.isIndefiniteForm) ? inputLength : this.lenBlock.length);\n        if (resultOffset === -1) {\n            this.error = this.valueBlock.error;\n            return resultOffset;\n        }\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        if (!this.valueBlock.error.length)\n            this.blockLength += this.valueBlock.blockLength;\n        return resultOffset;\n    }\n    onAsciiEncoding() {\n        const values = [];\n        for (const value of this.valueBlock.value) {\n            values.push(value.toString(\"ascii\").split(\"\\n\").map((o) => `  ${o}`).join(\"\\n\"));\n        }\n        const blockName = this.idBlock.tagClass === 3\n            ? `[${this.idBlock.tagNumber}]`\n            : this.constructor.NAME;\n        return values.length\n            ? `${blockName} :\\n${values.join(\"\\n\")}`\n            : `${blockName} :`;\n    }\n}\n_a$v = Constructed;\n(() => {\n    typeStore.Constructed = _a$v;\n})();\nConstructed.NAME = \"CONSTRUCTED\";\n\nclass LocalEndOfContentValueBlock extends ValueBlock {\n    fromBER(inputBuffer, inputOffset, _inputLength) {\n        return inputOffset;\n    }\n    toBER(_sizeOnly) {\n        return EMPTY_BUFFER;\n    }\n}\nLocalEndOfContentValueBlock.override = \"EndOfContentValueBlock\";\n\nvar _a$u;\nclass EndOfContent extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalEndOfContentValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 0;\n    }\n}\n_a$u = EndOfContent;\n(() => {\n    typeStore.EndOfContent = _a$u;\n})();\nEndOfContent.NAME = END_OF_CONTENT_NAME;\n\nvar _a$t;\nclass Null extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, ValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 5;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (this.lenBlock.length > 0)\n            this.warnings.push(\"Non-zero length of value block for Null type\");\n        if (!this.idBlock.error.length)\n            this.blockLength += this.idBlock.blockLength;\n        if (!this.lenBlock.error.length)\n            this.blockLength += this.lenBlock.blockLength;\n        this.blockLength += inputLength;\n        if ((inputOffset + inputLength) > inputBuffer.byteLength) {\n            this.error = \"End of input reached before message was fully decoded (inconsistent offset and length values)\";\n            return -1;\n        }\n        return (inputOffset + inputLength);\n    }\n    toBER(sizeOnly, writer) {\n        const retBuf = new ArrayBuffer(2);\n        if (!sizeOnly) {\n            const retView = new Uint8Array(retBuf);\n            retView[0] = 0x05;\n            retView[1] = 0x00;\n        }\n        if (writer) {\n            writer.write(retBuf);\n        }\n        return retBuf;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME}`;\n    }\n}\n_a$t = Null;\n(() => {\n    typeStore.Null = _a$t;\n})();\nNull.NAME = \"NULL\";\n\nclass LocalBooleanValueBlock extends HexBlock(ValueBlock) {\n    get value() {\n        for (const octet of this.valueHexView) {\n            if (octet > 0) {\n                return true;\n            }\n        }\n        return false;\n    }\n    set value(value) {\n        this.valueHexView[0] = value ? 0xFF : 0x00;\n    }\n    constructor({ value, ...parameters } = {}) {\n        super(parameters);\n        if (parameters.valueHex) {\n            this.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(parameters.valueHex);\n        }\n        else {\n            this.valueHexView = new Uint8Array(1);\n        }\n        if (value) {\n            this.value = value;\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        this.valueHexView = inputView.subarray(inputOffset, inputOffset + inputLength);\n        if (inputLength > 1)\n            this.warnings.push(\"Boolean value encoded in more then 1 octet\");\n        this.isHexOnly = true;\n        pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n        this.blockLength = inputLength;\n        return (inputOffset + inputLength);\n    }\n    toBER() {\n        return this.valueHexView.slice();\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value,\n        };\n    }\n}\nLocalBooleanValueBlock.NAME = \"BooleanValueBlock\";\n\nvar _a$s;\nclass Boolean extends BaseBlock {\n    getValue() {\n        return this.valueBlock.value;\n    }\n    setValue(value) {\n        this.valueBlock.value = value;\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalBooleanValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 1;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.getValue}`;\n    }\n}\n_a$s = Boolean;\n(() => {\n    typeStore.Boolean = _a$s;\n})();\nBoolean.NAME = \"BOOLEAN\";\n\nclass LocalOctetStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ isConstructed = false, ...parameters } = {}) {\n        super(parameters);\n        this.isConstructed = isConstructed;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = 0;\n        if (this.isConstructed) {\n            this.isHexOnly = false;\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1)\n                return resultOffset;\n            for (let i = 0; i < this.value.length; i++) {\n                const currentBlockName = this.value[i].constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm)\n                        break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, OCTET STRING may consists of OCTET STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== OCTET_STRING_NAME) {\n                    this.error = \"OCTET STRING may consists of OCTET STRINGs only\";\n                    return -1;\n                }\n            }\n        }\n        else {\n            this.isHexOnly = true;\n            resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n            this.blockLength = inputLength;\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed)\n            return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        return sizeOnly\n            ? new ArrayBuffer(this.valueHexView.byteLength)\n            : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalOctetStringValueBlock.NAME = \"OctetStringValueBlock\";\n\nvar _a$r;\nclass OctetString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}) {\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : (parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length));\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock,\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm,\n            },\n            ...parameters,\n        }, LocalOctetStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 4;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        if (inputLength === 0) {\n            if (this.idBlock.error.length === 0)\n                this.blockLength += this.idBlock.blockLength;\n            if (this.lenBlock.error.length === 0)\n                this.blockLength += this.lenBlock.blockLength;\n            return inputOffset;\n        }\n        if (!this.valueBlock.isConstructed) {\n            const view = inputBuffer instanceof ArrayBuffer ? new Uint8Array(inputBuffer) : inputBuffer;\n            const buf = view.subarray(inputOffset, inputOffset + inputLength);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === inputLength) {\n                        this.valueBlock.value = [asn.result];\n                    }\n                }\n            }\n            catch {\n            }\n        }\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || (this.valueBlock.value && this.valueBlock.value.length)) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        }\n        const name = this.constructor.NAME;\n        const value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueBlock.valueHexView);\n        return `${name} : ${value}`;\n    }\n    getValue() {\n        if (!this.idBlock.isConstructed) {\n            return this.valueBlock.valueHexView.slice().buffer;\n        }\n        const array = [];\n        for (const content of this.valueBlock.value) {\n            if (content instanceof _a$r) {\n                array.push(content.valueBlock.valueHexView);\n            }\n        }\n        return pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.concat(array);\n    }\n}\n_a$r = OctetString;\n(() => {\n    typeStore.OctetString = _a$r;\n})();\nOctetString.NAME = OCTET_STRING_NAME;\n\nclass LocalBitStringValueBlock extends HexBlock(LocalConstructedValueBlock) {\n    constructor({ unusedBits = 0, isConstructed = false, ...parameters } = {}) {\n        super(parameters);\n        this.unusedBits = unusedBits;\n        this.isConstructed = isConstructed;\n        this.blockLength = this.valueHexView.byteLength;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        let resultOffset = -1;\n        if (this.isConstructed) {\n            resultOffset = LocalConstructedValueBlock.prototype.fromBER.call(this, inputBuffer, inputOffset, inputLength);\n            if (resultOffset === -1)\n                return resultOffset;\n            for (const value of this.value) {\n                const currentBlockName = value.constructor.NAME;\n                if (currentBlockName === END_OF_CONTENT_NAME) {\n                    if (this.isIndefiniteForm)\n                        break;\n                    else {\n                        this.error = \"EndOfContent is unexpected, BIT STRING may consists of BIT STRINGs only\";\n                        return -1;\n                    }\n                }\n                if (currentBlockName !== BIT_STRING_NAME) {\n                    this.error = \"BIT STRING may consists of BIT STRINGs only\";\n                    return -1;\n                }\n                const valueBlock = value.valueBlock;\n                if ((this.unusedBits > 0) && (valueBlock.unusedBits > 0)) {\n                    this.error = \"Using of \\\"unused bits\\\" inside constructive BIT STRING allowed for least one only\";\n                    return -1;\n                }\n                this.unusedBits = valueBlock.unusedBits;\n            }\n            return resultOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.unusedBits = intBuffer[0];\n        if (this.unusedBits > 7) {\n            this.error = \"Unused bits for BitString must be in range 0-7\";\n            return -1;\n        }\n        if (!this.unusedBits) {\n            const buf = intBuffer.subarray(1);\n            try {\n                if (buf.byteLength) {\n                    const asn = localFromBER(buf, 0, buf.byteLength);\n                    if (asn.offset !== -1 && asn.offset === (inputLength - 1)) {\n                        this.value = [asn.result];\n                    }\n                }\n            }\n            catch {\n            }\n        }\n        this.valueHexView = intBuffer.subarray(1);\n        this.blockLength = intBuffer.length;\n        return (inputOffset + inputLength);\n    }\n    toBER(sizeOnly, writer) {\n        if (this.isConstructed) {\n            return LocalConstructedValueBlock.prototype.toBER.call(this, sizeOnly, writer);\n        }\n        if (sizeOnly) {\n            return new ArrayBuffer(this.valueHexView.byteLength + 1);\n        }\n        if (!this.valueHexView.byteLength) {\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(this.valueHexView.length + 1);\n        retView[0] = this.unusedBits;\n        retView.set(this.valueHexView, 1);\n        return retView.buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            unusedBits: this.unusedBits,\n            isConstructed: this.isConstructed,\n        };\n    }\n}\nLocalBitStringValueBlock.NAME = \"BitStringValueBlock\";\n\nvar _a$q;\nclass BitString extends BaseBlock {\n    constructor({ idBlock = {}, lenBlock = {}, ...parameters } = {}) {\n        var _b, _c;\n        (_b = parameters.isConstructed) !== null && _b !== void 0 ? _b : (parameters.isConstructed = !!((_c = parameters.value) === null || _c === void 0 ? void 0 : _c.length));\n        super({\n            idBlock: {\n                isConstructed: parameters.isConstructed,\n                ...idBlock,\n            },\n            lenBlock: {\n                ...lenBlock,\n                isIndefiniteForm: !!parameters.isIndefiniteForm,\n            },\n            ...parameters,\n        }, LocalBitStringValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 3;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        this.valueBlock.isConstructed = this.idBlock.isConstructed;\n        this.valueBlock.isIndefiniteForm = this.lenBlock.isIndefiniteForm;\n        return super.fromBER(inputBuffer, inputOffset, inputLength);\n    }\n    onAsciiEncoding() {\n        if (this.valueBlock.isConstructed || (this.valueBlock.value && this.valueBlock.value.length)) {\n            return Constructed.prototype.onAsciiEncoding.call(this);\n        }\n        else {\n            const bits = [];\n            const valueHex = this.valueBlock.valueHexView;\n            for (const byte of valueHex) {\n                bits.push(byte.toString(2).padStart(8, \"0\"));\n            }\n            const bitsStr = bits.join(\"\");\n            const name = this.constructor.NAME;\n            const value = bitsStr.substring(0, bitsStr.length - this.valueBlock.unusedBits);\n            return `${name} : ${value}`;\n        }\n    }\n}\n_a$q = BitString;\n(() => {\n    typeStore.BitString = _a$q;\n})();\nBitString.NAME = BIT_STRING_NAME;\n\nvar _a$p;\nfunction viewAdd(first, second) {\n    const c = new Uint8Array([0]);\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    let firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value = 0;\n    const max = (secondViewCopyLength < firstViewCopyLength) ? firstViewCopyLength : secondViewCopyLength;\n    let counter = 0;\n    for (let i = max; i >= 0; i--, counter++) {\n        switch (true) {\n            case (counter < secondViewCopy.length):\n                value = firstViewCopy[firstViewCopyLength - counter] + secondViewCopy[secondViewCopyLength - counter] + c[0];\n                break;\n            default:\n                value = firstViewCopy[firstViewCopyLength - counter] + c[0];\n        }\n        c[0] = value / 10;\n        switch (true) {\n            case (counter >= firstViewCopy.length):\n                firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(new Uint8Array([value % 10]), firstViewCopy);\n                break;\n            default:\n                firstViewCopy[firstViewCopyLength - counter] = value % 10;\n        }\n    }\n    if (c[0] > 0)\n        firstViewCopy = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, firstViewCopy);\n    return firstViewCopy;\n}\nfunction power2(n) {\n    if (n >= powers2.length) {\n        for (let p = powers2.length; p <= n; p++) {\n            const c = new Uint8Array([0]);\n            let digits = (powers2[p - 1]).slice(0);\n            for (let i = (digits.length - 1); i >= 0; i--) {\n                const newValue = new Uint8Array([(digits[i] << 1) + c[0]]);\n                c[0] = newValue[0] / 10;\n                digits[i] = newValue[0] % 10;\n            }\n            if (c[0] > 0)\n                digits = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilConcatView(c, digits);\n            powers2.push(digits);\n        }\n    }\n    return powers2[n];\n}\nfunction viewSub(first, second) {\n    let b = 0;\n    const firstView = new Uint8Array(first);\n    const secondView = new Uint8Array(second);\n    const firstViewCopy = firstView.slice(0);\n    const firstViewCopyLength = firstViewCopy.length - 1;\n    const secondViewCopy = secondView.slice(0);\n    const secondViewCopyLength = secondViewCopy.length - 1;\n    let value;\n    let counter = 0;\n    for (let i = secondViewCopyLength; i >= 0; i--, counter++) {\n        value = firstViewCopy[firstViewCopyLength - counter] - secondViewCopy[secondViewCopyLength - counter] - b;\n        switch (true) {\n            case (value < 0):\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n                break;\n            default:\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n        }\n    }\n    if (b > 0) {\n        for (let i = (firstViewCopyLength - secondViewCopyLength + 1); i >= 0; i--, counter++) {\n            value = firstViewCopy[firstViewCopyLength - counter] - b;\n            if (value < 0) {\n                b = 1;\n                firstViewCopy[firstViewCopyLength - counter] = value + 10;\n            }\n            else {\n                b = 0;\n                firstViewCopy[firstViewCopyLength - counter] = value;\n                break;\n            }\n        }\n    }\n    return firstViewCopy.slice();\n}\nclass LocalIntegerValueBlock extends HexBlock(ValueBlock) {\n    setValueHex() {\n        if (this.valueHexView.length >= 4) {\n            this.warnings.push(\"Too big Integer for decoding, hex only\");\n            this.isHexOnly = true;\n            this._valueDec = 0;\n        }\n        else {\n            this.isHexOnly = false;\n            if (this.valueHexView.length > 0) {\n                this._valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilDecodeTC.call(this);\n            }\n        }\n    }\n    constructor({ value, ...parameters } = {}) {\n        super(parameters);\n        this._valueDec = 0;\n        if (parameters.valueHex) {\n            this.setValueHex();\n        }\n        if (value !== undefined) {\n            this.valueDec = value;\n        }\n    }\n    set valueDec(v) {\n        this._valueDec = v;\n        this.isHexOnly = false;\n        this.valueHexView = new Uint8Array(pvutils__WEBPACK_IMPORTED_MODULE_1__.utilEncodeTC(v));\n    }\n    get valueDec() {\n        return this._valueDec;\n    }\n    fromDER(inputBuffer, inputOffset, inputLength, expectedLength = 0) {\n        const offset = this.fromBER(inputBuffer, inputOffset, inputLength);\n        if (offset === -1)\n            return offset;\n        const view = this.valueHexView;\n        if ((view[0] === 0x00) && ((view[1] & 0x80) !== 0)) {\n            this.valueHexView = view.subarray(1);\n        }\n        else {\n            if (expectedLength !== 0) {\n                if (view.length < expectedLength) {\n                    if ((expectedLength - view.length) > 1)\n                        expectedLength = view.length + 1;\n                    this.valueHexView = view.subarray(expectedLength - view.length);\n                }\n            }\n        }\n        return offset;\n    }\n    toDER(sizeOnly = false) {\n        const view = this.valueHexView;\n        switch (true) {\n            case ((view[0] & 0x80) !== 0):\n                {\n                    const updatedView = new Uint8Array(this.valueHexView.length + 1);\n                    updatedView[0] = 0x00;\n                    updatedView.set(view, 1);\n                    this.valueHexView = updatedView;\n                }\n                break;\n            case ((view[0] === 0x00) && ((view[1] & 0x80) === 0)):\n                {\n                    this.valueHexView = this.valueHexView.subarray(1);\n                }\n                break;\n        }\n        return this.toBER(sizeOnly);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const resultOffset = super.fromBER(inputBuffer, inputOffset, inputLength);\n        if (resultOffset === -1) {\n            return resultOffset;\n        }\n        this.setValueHex();\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        return sizeOnly\n            ? new ArrayBuffer(this.valueHexView.length)\n            : this.valueHexView.slice().buffer;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n        };\n    }\n    toString() {\n        const firstBit = (this.valueHexView.length * 8) - 1;\n        let digits = new Uint8Array((this.valueHexView.length * 8) / 3);\n        let bitNumber = 0;\n        let currentByte;\n        const asn1View = this.valueHexView;\n        let result = \"\";\n        let flag = false;\n        for (let byteNumber = (asn1View.byteLength - 1); byteNumber >= 0; byteNumber--) {\n            currentByte = asn1View[byteNumber];\n            for (let i = 0; i < 8; i++) {\n                if ((currentByte & 1) === 1) {\n                    switch (bitNumber) {\n                        case firstBit:\n                            digits = viewSub(power2(bitNumber), digits);\n                            result = \"-\";\n                            break;\n                        default:\n                            digits = viewAdd(digits, power2(bitNumber));\n                    }\n                }\n                bitNumber++;\n                currentByte >>= 1;\n            }\n        }\n        for (let i = 0; i < digits.length; i++) {\n            if (digits[i])\n                flag = true;\n            if (flag)\n                result += digitsString.charAt(digits[i]);\n        }\n        if (flag === false)\n            result += digitsString.charAt(0);\n        return result;\n    }\n}\n_a$p = LocalIntegerValueBlock;\nLocalIntegerValueBlock.NAME = \"IntegerValueBlock\";\n(() => {\n    Object.defineProperty(_a$p.prototype, \"valueHex\", {\n        set: function (v) {\n            this.valueHexView = new Uint8Array(v);\n            this.setValueHex();\n        },\n        get: function () {\n            return this.valueHexView.slice().buffer;\n        },\n    });\n})();\n\nvar _a$o;\nclass Integer extends BaseBlock {\n    constructor(parameters = {}) {\n        super(parameters, LocalIntegerValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 2;\n    }\n    toBigInt() {\n        assertBigInt();\n        return BigInt(this.valueBlock.toString());\n    }\n    static fromBigInt(value) {\n        assertBigInt();\n        const bigIntValue = BigInt(value);\n        const writer = new ViewWriter();\n        const hex = bigIntValue.toString(16).replace(/^-/, \"\");\n        const view = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(hex));\n        if (bigIntValue < 0) {\n            const first = new Uint8Array(view.length + (view[0] & 0x80 ? 1 : 0));\n            first[0] |= 0x80;\n            const firstInt = BigInt(`0x${pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(first)}`);\n            const secondInt = firstInt + bigIntValue;\n            const second = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromHex(secondInt.toString(16)));\n            second[0] |= 0x80;\n            writer.write(second);\n        }\n        else {\n            if (view[0] & 0x80) {\n                writer.write(new Uint8Array([0]));\n            }\n            writer.write(view);\n        }\n        const res = new _a$o({ valueHex: writer.final() });\n        return res;\n    }\n    convertToDER() {\n        const integer = new _a$o({ valueHex: this.valueBlock.valueHexView });\n        integer.valueBlock.toDER();\n        return integer;\n    }\n    convertFromDER() {\n        return new _a$o({\n            valueHex: this.valueBlock.valueHexView[0] === 0\n                ? this.valueBlock.valueHexView.subarray(1)\n                : this.valueBlock.valueHexView,\n        });\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString()}`;\n    }\n}\n_a$o = Integer;\n(() => {\n    typeStore.Integer = _a$o;\n})();\nInteger.NAME = \"INTEGER\";\n\nvar _a$n;\nclass Enumerated extends Integer {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 10;\n    }\n}\n_a$n = Enumerated;\n(() => {\n    typeStore.Enumerated = _a$n;\n})();\nEnumerated.NAME = \"ENUMERATED\";\n\nclass LocalSidValueBlock extends HexBlock(ValueBlock) {\n    constructor({ valueDec = -1, isFirstSid = false, ...parameters } = {}) {\n        super(parameters);\n        this.valueDec = valueDec;\n        this.isFirstSid = isFirstSid;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (!inputLength) {\n            return inputOffset;\n        }\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength)) {\n            return -1;\n        }\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for (let i = 0; i < inputLength; i++) {\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00)\n                break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for (let i = 0; i < this.blockLength; i++) {\n            tempView[i] = this.valueHexView[i];\n        }\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00)\n            this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8)\n            this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return (inputOffset + this.blockLength);\n    }\n    set valueBigInt(value) {\n        assertBigInt();\n        let bits = BigInt(value).toString(2);\n        while (bits.length % 7) {\n            bits = \"0\" + bits;\n        }\n        const bytes = new Uint8Array(bits.length / 7);\n        for (let i = 0; i < bytes.length; i++) {\n            bytes[i] = parseInt(bits.slice(i * 7, i * 7 + 7), 2) + (i + 1 < bytes.length ? 0x80 : 0);\n        }\n        this.fromBER(bytes.buffer, 0, bytes.length);\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly)\n                return (new ArrayBuffer(this.valueHexView.byteLength));\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for (let i = 0; i < (this.blockLength - 1); i++)\n                retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for (let i = 0; i < len; i++)\n                retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly)\n            result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            if (this.isFirstSid) {\n                let sidValue = this.valueDec;\n                if (this.valueDec <= 39)\n                    result = \"0.\";\n                else {\n                    if (this.valueDec <= 79) {\n                        result = \"1.\";\n                        sidValue -= 40;\n                    }\n                    else {\n                        result = \"2.\";\n                        sidValue -= 80;\n                    }\n                }\n                result += sidValue.toString();\n            }\n            else\n                result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n            isFirstSid: this.isFirstSid,\n        };\n    }\n}\nLocalSidValueBlock.NAME = \"sidBlock\";\n\nclass LocalObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}) {\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while (inputLength > 0) {\n            const sidBlock = new LocalSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            if (this.value.length === 0)\n                sidBlock.isFirstSid = true;\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly) {\n        const retBuffers = [];\n        for (let i = 0; i < this.value.length; i++) {\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        let flag = false;\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1)\n                sid = string.substring(pos1);\n            else\n                sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            if (flag) {\n                const sidBlock = this.value[0];\n                let plus = 0;\n                switch (sidBlock.valueDec) {\n                    case 0:\n                        break;\n                    case 1:\n                        plus = 40;\n                        break;\n                    case 2:\n                        plus = 80;\n                        break;\n                    default:\n                        this.value = [];\n                        return;\n                }\n                const parsedSID = parseInt(sid, 10);\n                if (isNaN(parsedSID))\n                    return;\n                sidBlock.valueDec = parsedSID + plus;\n                flag = false;\n            }\n            else {\n                const sidBlock = new LocalSidValueBlock();\n                if (sid > Number.MAX_SAFE_INTEGER) {\n                    assertBigInt();\n                    const sidValue = BigInt(sid);\n                    sidBlock.valueBigInt = sidValue;\n                }\n                else {\n                    sidBlock.valueDec = parseInt(sid, 10);\n                    if (isNaN(sidBlock.valueDec))\n                        return;\n                }\n                if (!this.value.length) {\n                    sidBlock.isFirstSid = true;\n                    flag = true;\n                }\n                this.value.push(sidBlock);\n            }\n        } while (pos2 !== -1);\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for (let i = 0; i < this.value.length; i++) {\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0)\n                result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                if (this.value[i].isFirstSid)\n                    result = `2.{${sidStr} - 80}`;\n                else\n                    result += sidStr;\n            }\n            else\n                result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: [],\n        };\n        for (let i = 0; i < this.value.length; i++) {\n            object.sidArray.push(this.value[i].toJSON());\n        }\n        return object;\n    }\n}\nLocalObjectIdentifierValueBlock.NAME = \"ObjectIdentifierValueBlock\";\n\nvar _a$m;\nclass ObjectIdentifier extends BaseBlock {\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 6;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue(),\n        };\n    }\n}\n_a$m = ObjectIdentifier;\n(() => {\n    typeStore.ObjectIdentifier = _a$m;\n})();\nObjectIdentifier.NAME = \"OBJECT IDENTIFIER\";\n\nclass LocalRelativeSidValueBlock extends HexBlock(LocalBaseBlock) {\n    constructor({ valueDec = 0, ...parameters } = {}) {\n        super(parameters);\n        this.valueDec = valueDec;\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        if (inputLength === 0)\n            return inputOffset;\n        const inputView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        if (!checkBufferParams(this, inputView, inputOffset, inputLength))\n            return -1;\n        const intBuffer = inputView.subarray(inputOffset, inputOffset + inputLength);\n        this.valueHexView = new Uint8Array(inputLength);\n        for (let i = 0; i < inputLength; i++) {\n            this.valueHexView[i] = intBuffer[i] & 0x7F;\n            this.blockLength++;\n            if ((intBuffer[i] & 0x80) === 0x00)\n                break;\n        }\n        const tempView = new Uint8Array(this.blockLength);\n        for (let i = 0; i < this.blockLength; i++)\n            tempView[i] = this.valueHexView[i];\n        this.valueHexView = tempView;\n        if ((intBuffer[this.blockLength - 1] & 0x80) !== 0x00) {\n            this.error = \"End of input reached before message was fully decoded\";\n            return -1;\n        }\n        if (this.valueHexView[0] === 0x00)\n            this.warnings.push(\"Needlessly long format of SID encoding\");\n        if (this.blockLength <= 8)\n            this.valueDec = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilFromBase(this.valueHexView, 7);\n        else {\n            this.isHexOnly = true;\n            this.warnings.push(\"Too big SID for decoding, hex only\");\n        }\n        return (inputOffset + this.blockLength);\n    }\n    toBER(sizeOnly) {\n        if (this.isHexOnly) {\n            if (sizeOnly)\n                return (new ArrayBuffer(this.valueHexView.byteLength));\n            const curView = this.valueHexView;\n            const retView = new Uint8Array(this.blockLength);\n            for (let i = 0; i < (this.blockLength - 1); i++)\n                retView[i] = curView[i] | 0x80;\n            retView[this.blockLength - 1] = curView[this.blockLength - 1];\n            return retView.buffer;\n        }\n        const encodedBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(this.valueDec, 7);\n        if (encodedBuf.byteLength === 0) {\n            this.error = \"Error during encoding SID value\";\n            return EMPTY_BUFFER;\n        }\n        const retView = new Uint8Array(encodedBuf.byteLength);\n        if (!sizeOnly) {\n            const encodedView = new Uint8Array(encodedBuf);\n            const len = encodedBuf.byteLength - 1;\n            for (let i = 0; i < len; i++)\n                retView[i] = encodedView[i] | 0x80;\n            retView[len] = encodedView[len];\n        }\n        return retView.buffer;\n    }\n    toString() {\n        let result = \"\";\n        if (this.isHexOnly)\n            result = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToHex(this.valueHexView);\n        else {\n            result = this.valueDec.toString();\n        }\n        return result;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            valueDec: this.valueDec,\n        };\n    }\n}\nLocalRelativeSidValueBlock.NAME = \"relativeSidBlock\";\n\nclass LocalRelativeObjectIdentifierValueBlock extends ValueBlock {\n    constructor({ value = EMPTY_STRING, ...parameters } = {}) {\n        super(parameters);\n        this.value = [];\n        if (value) {\n            this.fromString(value);\n        }\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        let resultOffset = inputOffset;\n        while (inputLength > 0) {\n            const sidBlock = new LocalRelativeSidValueBlock();\n            resultOffset = sidBlock.fromBER(inputBuffer, resultOffset, inputLength);\n            if (resultOffset === -1) {\n                this.blockLength = 0;\n                this.error = sidBlock.error;\n                return resultOffset;\n            }\n            this.blockLength += sidBlock.blockLength;\n            inputLength -= sidBlock.blockLength;\n            this.value.push(sidBlock);\n        }\n        return resultOffset;\n    }\n    toBER(sizeOnly, _writer) {\n        const retBuffers = [];\n        for (let i = 0; i < this.value.length; i++) {\n            const valueBuf = this.value[i].toBER(sizeOnly);\n            if (valueBuf.byteLength === 0) {\n                this.error = this.value[i].error;\n                return EMPTY_BUFFER;\n            }\n            retBuffers.push(valueBuf);\n        }\n        return concat(retBuffers);\n    }\n    fromString(string) {\n        this.value = [];\n        let pos1 = 0;\n        let pos2 = 0;\n        let sid = \"\";\n        do {\n            pos2 = string.indexOf(\".\", pos1);\n            if (pos2 === -1)\n                sid = string.substring(pos1);\n            else\n                sid = string.substring(pos1, pos2);\n            pos1 = pos2 + 1;\n            const sidBlock = new LocalRelativeSidValueBlock();\n            sidBlock.valueDec = parseInt(sid, 10);\n            if (isNaN(sidBlock.valueDec))\n                return true;\n            this.value.push(sidBlock);\n        } while (pos2 !== -1);\n        return true;\n    }\n    toString() {\n        let result = \"\";\n        let isHexOnly = false;\n        for (let i = 0; i < this.value.length; i++) {\n            isHexOnly = this.value[i].isHexOnly;\n            let sidStr = this.value[i].toString();\n            if (i !== 0)\n                result = `${result}.`;\n            if (isHexOnly) {\n                sidStr = `{${sidStr}}`;\n                result += sidStr;\n            }\n            else\n                result += sidStr;\n        }\n        return result;\n    }\n    toJSON() {\n        const object = {\n            ...super.toJSON(),\n            value: this.toString(),\n            sidArray: [],\n        };\n        for (let i = 0; i < this.value.length; i++)\n            object.sidArray.push(this.value[i].toJSON());\n        return object;\n    }\n}\nLocalRelativeObjectIdentifierValueBlock.NAME = \"RelativeObjectIdentifierValueBlock\";\n\nvar _a$l;\nclass RelativeObjectIdentifier extends BaseBlock {\n    getValue() {\n        return this.valueBlock.toString();\n    }\n    setValue(value) {\n        this.valueBlock.fromString(value);\n    }\n    constructor(parameters = {}) {\n        super(parameters, LocalRelativeObjectIdentifierValueBlock);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 13;\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.valueBlock.toString() || \"empty\"}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.getValue(),\n        };\n    }\n}\n_a$l = RelativeObjectIdentifier;\n(() => {\n    typeStore.RelativeObjectIdentifier = _a$l;\n})();\nRelativeObjectIdentifier.NAME = \"RelativeObjectIdentifier\";\n\nvar _a$k;\nclass Sequence extends Constructed {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 16;\n    }\n}\n_a$k = Sequence;\n(() => {\n    typeStore.Sequence = _a$k;\n})();\nSequence.NAME = \"SEQUENCE\";\n\nvar _a$j;\nclass Set extends Constructed {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 17;\n    }\n}\n_a$j = Set;\n(() => {\n    typeStore.Set = _a$j;\n})();\nSet.NAME = \"SET\";\n\nclass LocalStringValueBlock extends HexBlock(ValueBlock) {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.isHexOnly = true;\n        this.value = EMPTY_STRING;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            value: this.value,\n        };\n    }\n}\nLocalStringValueBlock.NAME = \"StringValueBlock\";\n\nclass LocalSimpleStringValueBlock extends LocalStringValueBlock {\n}\nLocalSimpleStringValueBlock.NAME = \"SimpleStringValueBlock\";\n\nclass LocalSimpleStringBlock extends BaseStringBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters, LocalSimpleStringValueBlock);\n    }\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    }\n    fromString(inputString) {\n        const strLen = inputString.length;\n        const view = this.valueBlock.valueHexView = new Uint8Array(strLen);\n        for (let i = 0; i < strLen; i++)\n            view[i] = inputString.charCodeAt(i);\n        this.valueBlock.value = inputString;\n    }\n}\nLocalSimpleStringBlock.NAME = \"SIMPLE STRING\";\n\nclass LocalUtf8StringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n        try {\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf8String(inputBuffer);\n        }\n        catch (ex) {\n            this.warnings.push(`Error during \"decodeURIComponent\": ${ex}, using raw string`);\n            this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToBinary(inputBuffer);\n        }\n    }\n    fromString(inputString) {\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf8String(inputString));\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUtf8StringValueBlock.NAME = \"Utf8StringValueBlock\";\n\nvar _a$i;\nclass Utf8String extends LocalUtf8StringValueBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 12;\n    }\n}\n_a$i = Utf8String;\n(() => {\n    typeStore.Utf8String = _a$i;\n})();\nUtf8String.NAME = \"UTF8String\";\n\nclass LocalBmpStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        this.valueBlock.value = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.ToUtf16String(inputBuffer);\n        this.valueBlock.valueHexView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer);\n    }\n    fromString(inputString) {\n        this.valueBlock.value = inputString;\n        this.valueBlock.valueHexView = new Uint8Array(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.Convert.FromUtf16String(inputString));\n    }\n}\nLocalBmpStringValueBlock.NAME = \"BmpStringValueBlock\";\n\nvar _a$h;\nclass BmpString extends LocalBmpStringValueBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 30;\n    }\n}\n_a$h = BmpString;\n(() => {\n    typeStore.BmpString = _a$h;\n})();\nBmpString.NAME = \"BMPString\";\n\nclass LocalUniversalStringValueBlock extends LocalSimpleStringBlock {\n    fromBuffer(inputBuffer) {\n        const copyBuffer = ArrayBuffer.isView(inputBuffer) ? inputBuffer.slice().buffer : inputBuffer.slice(0);\n        const valueView = new Uint8Array(copyBuffer);\n        for (let i = 0; i < valueView.length; i += 4) {\n            valueView[i] = valueView[i + 3];\n            valueView[i + 1] = valueView[i + 2];\n            valueView[i + 2] = 0x00;\n            valueView[i + 3] = 0x00;\n        }\n        this.valueBlock.value = String.fromCharCode.apply(null, new Uint32Array(copyBuffer));\n    }\n    fromString(inputString) {\n        const strLength = inputString.length;\n        const valueHexView = this.valueBlock.valueHexView = new Uint8Array(strLength * 4);\n        for (let i = 0; i < strLength; i++) {\n            const codeBuf = pvutils__WEBPACK_IMPORTED_MODULE_1__.utilToBase(inputString.charCodeAt(i), 8);\n            const codeView = new Uint8Array(codeBuf);\n            if (codeView.length > 4)\n                continue;\n            const dif = 4 - codeView.length;\n            for (let j = (codeView.length - 1); j >= 0; j--)\n                valueHexView[i * 4 + j + dif] = codeView[j];\n        }\n        this.valueBlock.value = inputString;\n    }\n}\nLocalUniversalStringValueBlock.NAME = \"UniversalStringValueBlock\";\n\nvar _a$g;\nclass UniversalString extends LocalUniversalStringValueBlock {\n    constructor({ ...parameters } = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 28;\n    }\n}\n_a$g = UniversalString;\n(() => {\n    typeStore.UniversalString = _a$g;\n})();\nUniversalString.NAME = \"UniversalString\";\n\nvar _a$f;\nclass NumericString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 18;\n    }\n}\n_a$f = NumericString;\n(() => {\n    typeStore.NumericString = _a$f;\n})();\nNumericString.NAME = \"NumericString\";\n\nvar _a$e;\nclass PrintableString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 19;\n    }\n}\n_a$e = PrintableString;\n(() => {\n    typeStore.PrintableString = _a$e;\n})();\nPrintableString.NAME = \"PrintableString\";\n\nvar _a$d;\nclass TeletexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 20;\n    }\n}\n_a$d = TeletexString;\n(() => {\n    typeStore.TeletexString = _a$d;\n})();\nTeletexString.NAME = \"TeletexString\";\n\nvar _a$c;\nclass VideotexString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 21;\n    }\n}\n_a$c = VideotexString;\n(() => {\n    typeStore.VideotexString = _a$c;\n})();\nVideotexString.NAME = \"VideotexString\";\n\nvar _a$b;\nclass IA5String extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 22;\n    }\n}\n_a$b = IA5String;\n(() => {\n    typeStore.IA5String = _a$b;\n})();\nIA5String.NAME = \"IA5String\";\n\nvar _a$a;\nclass GraphicString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 25;\n    }\n}\n_a$a = GraphicString;\n(() => {\n    typeStore.GraphicString = _a$a;\n})();\nGraphicString.NAME = \"GraphicString\";\n\nvar _a$9;\nclass VisibleString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 26;\n    }\n}\n_a$9 = VisibleString;\n(() => {\n    typeStore.VisibleString = _a$9;\n})();\nVisibleString.NAME = \"VisibleString\";\n\nvar _a$8;\nclass GeneralString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 27;\n    }\n}\n_a$8 = GeneralString;\n(() => {\n    typeStore.GeneralString = _a$8;\n})();\nGeneralString.NAME = \"GeneralString\";\n\nvar _a$7;\nclass CharacterString extends LocalSimpleStringBlock {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 29;\n    }\n}\n_a$7 = CharacterString;\n(() => {\n    typeStore.CharacterString = _a$7;\n})();\nCharacterString.NAME = \"CharacterString\";\n\nvar _a$6;\nclass UTCTime extends VisibleString {\n    constructor({ value, valueDate, ...parameters } = {}) {\n        super(parameters);\n        this.year = 0;\n        this.month = 0;\n        this.day = 0;\n        this.hour = 0;\n        this.minute = 0;\n        this.second = 0;\n        if (value) {\n            this.fromString(value);\n            this.valueBlock.valueHexView = new Uint8Array(value.length);\n            for (let i = 0; i < value.length; i++)\n                this.valueBlock.valueHexView[i] = value.charCodeAt(i);\n        }\n        if (valueDate) {\n            this.fromDate(valueDate);\n            this.valueBlock.valueHexView = new Uint8Array(this.toBuffer());\n        }\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 23;\n    }\n    fromBuffer(inputBuffer) {\n        this.fromString(String.fromCharCode.apply(null, pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer)));\n    }\n    toBuffer() {\n        const str = this.toString();\n        const buffer = new ArrayBuffer(str.length);\n        const view = new Uint8Array(buffer);\n        for (let i = 0; i < str.length; i++)\n            view[i] = str.charCodeAt(i);\n        return buffer;\n    }\n    fromDate(inputDate) {\n        this.year = inputDate.getUTCFullYear();\n        this.month = inputDate.getUTCMonth() + 1;\n        this.day = inputDate.getUTCDate();\n        this.hour = inputDate.getUTCHours();\n        this.minute = inputDate.getUTCMinutes();\n        this.second = inputDate.getUTCSeconds();\n    }\n    toDate() {\n        return (new Date(Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second)));\n    }\n    fromString(inputString) {\n        const parser = /(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})Z/ig;\n        const parserArray = parser.exec(inputString);\n        if (parserArray === null) {\n            this.error = \"Wrong input string for conversion\";\n            return;\n        }\n        const year = parseInt(parserArray[1], 10);\n        if (year >= 50)\n            this.year = 1900 + year;\n        else\n            this.year = 2000 + year;\n        this.month = parseInt(parserArray[2], 10);\n        this.day = parseInt(parserArray[3], 10);\n        this.hour = parseInt(parserArray[4], 10);\n        this.minute = parseInt(parserArray[5], 10);\n        this.second = parseInt(parserArray[6], 10);\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = new Array(7);\n            outputArray[0] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(((this.year < 2000) ? (this.year - 1900) : (this.year - 2000)), 2);\n            outputArray[1] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2);\n            outputArray[2] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2);\n            outputArray[3] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2);\n            outputArray[4] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2);\n            outputArray[5] = pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2);\n            outputArray[6] = \"Z\";\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    onAsciiEncoding() {\n        return `${this.constructor.NAME} : ${this.toDate().toISOString()}`;\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            year: this.year,\n            month: this.month,\n            day: this.day,\n            hour: this.hour,\n            minute: this.minute,\n            second: this.second,\n        };\n    }\n}\n_a$6 = UTCTime;\n(() => {\n    typeStore.UTCTime = _a$6;\n})();\nUTCTime.NAME = \"UTCTime\";\n\nvar _a$5;\nclass GeneralizedTime extends UTCTime {\n    constructor(parameters = {}) {\n        var _b;\n        super(parameters);\n        (_b = this.millisecond) !== null && _b !== void 0 ? _b : (this.millisecond = 0);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 24;\n    }\n    fromDate(inputDate) {\n        super.fromDate(inputDate);\n        this.millisecond = inputDate.getUTCMilliseconds();\n    }\n    toDate() {\n        const utcDate = Date.UTC(this.year, this.month - 1, this.day, this.hour, this.minute, this.second, this.millisecond);\n        return (new Date(utcDate));\n    }\n    fromString(inputString) {\n        let isUTC = false;\n        let timeString = \"\";\n        let dateTimeString = \"\";\n        let fractionPart = 0;\n        let parser;\n        let hourDifference = 0;\n        let minuteDifference = 0;\n        if (inputString[inputString.length - 1] === \"Z\") {\n            timeString = inputString.substring(0, inputString.length - 1);\n            isUTC = true;\n        }\n        else {\n            const number = new Number(inputString[inputString.length - 1]);\n            if (isNaN(number.valueOf()))\n                throw new Error(\"Wrong input string for conversion\");\n            timeString = inputString;\n        }\n        if (isUTC) {\n            if (timeString.indexOf(\"+\") !== -1)\n                throw new Error(\"Wrong input string for conversion\");\n            if (timeString.indexOf(\"-\") !== -1)\n                throw new Error(\"Wrong input string for conversion\");\n        }\n        else {\n            let multiplier = 1;\n            let differencePosition = timeString.indexOf(\"+\");\n            let differenceString = \"\";\n            if (differencePosition === -1) {\n                differencePosition = timeString.indexOf(\"-\");\n                multiplier = -1;\n            }\n            if (differencePosition !== -1) {\n                differenceString = timeString.substring(differencePosition + 1);\n                timeString = timeString.substring(0, differencePosition);\n                if ((differenceString.length !== 2) && (differenceString.length !== 4))\n                    throw new Error(\"Wrong input string for conversion\");\n                let number = parseInt(differenceString.substring(0, 2), 10);\n                if (isNaN(number.valueOf()))\n                    throw new Error(\"Wrong input string for conversion\");\n                hourDifference = multiplier * number;\n                if (differenceString.length === 4) {\n                    number = parseInt(differenceString.substring(2, 4), 10);\n                    if (isNaN(number.valueOf()))\n                        throw new Error(\"Wrong input string for conversion\");\n                    minuteDifference = multiplier * number;\n                }\n            }\n        }\n        let fractionPointPosition = timeString.indexOf(\".\");\n        if (fractionPointPosition === -1)\n            fractionPointPosition = timeString.indexOf(\",\");\n        if (fractionPointPosition !== -1) {\n            const fractionPartCheck = new Number(`0${timeString.substring(fractionPointPosition)}`);\n            if (isNaN(fractionPartCheck.valueOf()))\n                throw new Error(\"Wrong input string for conversion\");\n            fractionPart = fractionPartCheck.valueOf();\n            dateTimeString = timeString.substring(0, fractionPointPosition);\n        }\n        else\n            dateTimeString = timeString;\n        switch (true) {\n            case (dateTimeString.length === 8):\n                parser = /(\\d{4})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1)\n                    throw new Error(\"Wrong input string for conversion\");\n                break;\n            case (dateTimeString.length === 10):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.minute = Math.floor(fractionResult);\n                    fractionResult = 60 * (fractionResult - this.minute);\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case (dateTimeString.length === 12):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    let fractionResult = 60 * fractionPart;\n                    this.second = Math.floor(fractionResult);\n                    fractionResult = 1000 * (fractionResult - this.second);\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            case (dateTimeString.length === 14):\n                parser = /(\\d{4})(\\d{2})(\\d{2})(\\d{2})(\\d{2})(\\d{2})/ig;\n                if (fractionPointPosition !== -1) {\n                    const fractionResult = 1000 * fractionPart;\n                    this.millisecond = Math.floor(fractionResult);\n                }\n                break;\n            default:\n                throw new Error(\"Wrong input string for conversion\");\n        }\n        const parserArray = parser.exec(dateTimeString);\n        if (parserArray === null)\n            throw new Error(\"Wrong input string for conversion\");\n        for (let j = 1; j < parserArray.length; j++) {\n            switch (j) {\n                case 1:\n                    this.year = parseInt(parserArray[j], 10);\n                    break;\n                case 2:\n                    this.month = parseInt(parserArray[j], 10);\n                    break;\n                case 3:\n                    this.day = parseInt(parserArray[j], 10);\n                    break;\n                case 4:\n                    this.hour = parseInt(parserArray[j], 10) + hourDifference;\n                    break;\n                case 5:\n                    this.minute = parseInt(parserArray[j], 10) + minuteDifference;\n                    break;\n                case 6:\n                    this.second = parseInt(parserArray[j], 10);\n                    break;\n                default:\n                    throw new Error(\"Wrong input string for conversion\");\n            }\n        }\n        if (isUTC === false) {\n            const tempDate = new Date(this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n            this.year = tempDate.getUTCFullYear();\n            this.month = tempDate.getUTCMonth();\n            this.day = tempDate.getUTCDay();\n            this.hour = tempDate.getUTCHours();\n            this.minute = tempDate.getUTCMinutes();\n            this.second = tempDate.getUTCSeconds();\n            this.millisecond = tempDate.getUTCMilliseconds();\n        }\n    }\n    toString(encoding = \"iso\") {\n        if (encoding === \"iso\") {\n            const outputArray = [];\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.year, 4));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.month, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.day, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.hour, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.minute, 2));\n            outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.second, 2));\n            if (this.millisecond !== 0) {\n                outputArray.push(\".\");\n                outputArray.push(pvutils__WEBPACK_IMPORTED_MODULE_1__.padNumber(this.millisecond, 3));\n            }\n            outputArray.push(\"Z\");\n            return outputArray.join(\"\");\n        }\n        return super.toString(encoding);\n    }\n    toJSON() {\n        return {\n            ...super.toJSON(),\n            millisecond: this.millisecond,\n        };\n    }\n}\n_a$5 = GeneralizedTime;\n(() => {\n    typeStore.GeneralizedTime = _a$5;\n})();\nGeneralizedTime.NAME = \"GeneralizedTime\";\n\nvar _a$4;\nclass DATE extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 31;\n    }\n}\n_a$4 = DATE;\n(() => {\n    typeStore.DATE = _a$4;\n})();\nDATE.NAME = \"DATE\";\n\nvar _a$3;\nclass TimeOfDay extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 32;\n    }\n}\n_a$3 = TimeOfDay;\n(() => {\n    typeStore.TimeOfDay = _a$3;\n})();\nTimeOfDay.NAME = \"TimeOfDay\";\n\nvar _a$2;\nclass DateTime extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 33;\n    }\n}\n_a$2 = DateTime;\n(() => {\n    typeStore.DateTime = _a$2;\n})();\nDateTime.NAME = \"DateTime\";\n\nvar _a$1;\nclass Duration extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 34;\n    }\n}\n_a$1 = Duration;\n(() => {\n    typeStore.Duration = _a$1;\n})();\nDuration.NAME = \"Duration\";\n\nvar _a;\nclass TIME extends Utf8String {\n    constructor(parameters = {}) {\n        super(parameters);\n        this.idBlock.tagClass = 1;\n        this.idBlock.tagNumber = 14;\n    }\n}\n_a = TIME;\n(() => {\n    typeStore.TIME = _a;\n})();\nTIME.NAME = \"TIME\";\n\nclass Any {\n    constructor({ name = EMPTY_STRING, optional = false } = {}) {\n        this.name = name;\n        this.optional = optional;\n    }\n}\n\nclass Choice extends Any {\n    constructor({ value = [], ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n    }\n}\n\nclass Repeated extends Any {\n    constructor({ value = new Any(), local = false, ...parameters } = {}) {\n        super(parameters);\n        this.value = value;\n        this.local = local;\n    }\n}\n\nclass RawData {\n    get data() {\n        return this.dataView.slice().buffer;\n    }\n    set data(value) {\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(value);\n    }\n    constructor({ data = EMPTY_VIEW } = {}) {\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(data);\n    }\n    fromBER(inputBuffer, inputOffset, inputLength) {\n        const endLength = inputOffset + inputLength;\n        this.dataView = pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer).subarray(inputOffset, endLength);\n        return endLength;\n    }\n    toBER(_sizeOnly) {\n        return this.dataView.slice().buffer;\n    }\n}\n\nfunction compareSchema(root, inputData, inputSchema) {\n    if (inputSchema instanceof Choice) {\n        for (const element of inputSchema.value) {\n            const result = compareSchema(root, inputData, element);\n            if (result.verified) {\n                return {\n                    verified: true,\n                    result: root,\n                };\n            }\n        }\n        {\n            const _result = {\n                verified: false,\n                result: { error: \"Wrong values for Choice type\" },\n            };\n            if (inputSchema.hasOwnProperty(NAME))\n                _result.name = inputSchema.name;\n            return _result;\n        }\n    }\n    if (inputSchema instanceof Any) {\n        if (inputSchema.hasOwnProperty(NAME))\n            root[inputSchema.name] = inputData;\n        return {\n            verified: true,\n            result: root,\n        };\n    }\n    if ((root instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong root object\" },\n        };\n    }\n    if ((inputData instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 data\" },\n        };\n    }\n    if ((inputSchema instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((ID_BLOCK in inputSchema) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((FROM_BER in inputSchema.idBlock) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if ((TO_BER in inputSchema.idBlock) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    const encodedId = inputSchema.idBlock.toBER(false);\n    if (encodedId.byteLength === 0) {\n        return {\n            verified: false,\n            result: { error: \"Error encoding idBlock for ASN.1 schema\" },\n        };\n    }\n    const decodedOffset = inputSchema.idBlock.fromBER(encodedId, 0, encodedId.byteLength);\n    if (decodedOffset === -1) {\n        return {\n            verified: false,\n            result: { error: \"Error decoding idBlock for ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_CLASS) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.tagClass !== inputData.idBlock.tagClass) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(TAG_NUMBER) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.tagNumber !== inputData.idBlock.tagNumber) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.hasOwnProperty(IS_CONSTRUCTED) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.isConstructed !== inputData.idBlock.isConstructed) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (!(IS_HEX_ONLY in inputSchema.idBlock)) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema\" },\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly !== inputData.idBlock.isHexOnly) {\n        return {\n            verified: false,\n            result: root,\n        };\n    }\n    if (inputSchema.idBlock.isHexOnly) {\n        if ((VALUE_HEX_VIEW in inputSchema.idBlock) === false) {\n            return {\n                verified: false,\n                result: { error: \"Wrong ASN.1 schema\" },\n            };\n        }\n        const schemaView = inputSchema.idBlock.valueHexView;\n        const asn1View = inputData.idBlock.valueHexView;\n        if (schemaView.length !== asn1View.length) {\n            return {\n                verified: false,\n                result: root,\n            };\n        }\n        for (let i = 0; i < schemaView.length; i++) {\n            if (schemaView[i] !== asn1View[1]) {\n                return {\n                    verified: false,\n                    result: root,\n                };\n            }\n        }\n    }\n    if (inputSchema.name) {\n        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n        if (inputSchema.name)\n            root[inputSchema.name] = inputData;\n    }\n    if (inputSchema instanceof typeStore.Constructed) {\n        let admission = 0;\n        let result = {\n            verified: false,\n            result: { error: \"Unknown error\" },\n        };\n        let maxLength = inputSchema.valueBlock.value.length;\n        if (maxLength > 0) {\n            if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                maxLength = inputData.valueBlock.value.length;\n            }\n        }\n        if (maxLength === 0) {\n            return {\n                verified: true,\n                result: root,\n            };\n        }\n        if ((inputData.valueBlock.value.length === 0)\n            && (inputSchema.valueBlock.value.length !== 0)) {\n            let _optional = true;\n            for (let i = 0; i < inputSchema.valueBlock.value.length; i++)\n                _optional = _optional && (inputSchema.valueBlock.value[i].optional || false);\n            if (_optional) {\n                return {\n                    verified: true,\n                    result: root,\n                };\n            }\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name)\n                    delete root[inputSchema.name];\n            }\n            root.error = \"Inconsistent object length\";\n            return {\n                verified: false,\n                result: root,\n            };\n        }\n        for (let i = 0; i < maxLength; i++) {\n            if ((i - admission) >= inputData.valueBlock.value.length) {\n                if (inputSchema.valueBlock.value[i].optional === false) {\n                    const _result = {\n                        verified: false,\n                        result: root,\n                    };\n                    root.error = \"Inconsistent length between ASN.1 data and schema\";\n                    if (inputSchema.name) {\n                        inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                        if (inputSchema.name) {\n                            delete root[inputSchema.name];\n                            _result.name = inputSchema.name;\n                        }\n                    }\n                    return _result;\n                }\n            }\n            else {\n                if (inputSchema.valueBlock.value[0] instanceof Repeated) {\n                    result = compareSchema(root, inputData.valueBlock.value[i], inputSchema.valueBlock.value[0].value);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[0].optional)\n                            admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name)\n                                    delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                    if ((NAME in inputSchema.valueBlock.value[0]) && (inputSchema.valueBlock.value[0].name.length > 0)) {\n                        let arrayRoot = {};\n                        if ((LOCAL in inputSchema.valueBlock.value[0]) && (inputSchema.valueBlock.value[0].local))\n                            arrayRoot = inputData;\n                        else\n                            arrayRoot = root;\n                        if (typeof arrayRoot[inputSchema.valueBlock.value[0].name] === \"undefined\")\n                            arrayRoot[inputSchema.valueBlock.value[0].name] = [];\n                        arrayRoot[inputSchema.valueBlock.value[0].name].push(inputData.valueBlock.value[i]);\n                    }\n                }\n                else {\n                    result = compareSchema(root, inputData.valueBlock.value[i - admission], inputSchema.valueBlock.value[i]);\n                    if (result.verified === false) {\n                        if (inputSchema.valueBlock.value[i].optional)\n                            admission++;\n                        else {\n                            if (inputSchema.name) {\n                                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                                if (inputSchema.name)\n                                    delete root[inputSchema.name];\n                            }\n                            return result;\n                        }\n                    }\n                }\n            }\n        }\n        if (result.verified === false) {\n            const _result = {\n                verified: false,\n                result: root,\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return {\n            verified: true,\n            result: root,\n        };\n    }\n    if (inputSchema.primitiveSchema\n        && (VALUE_HEX_VIEW in inputData.valueBlock)) {\n        const asn1 = localFromBER(inputData.valueBlock.valueHexView);\n        if (asn1.offset === -1) {\n            const _result = {\n                verified: false,\n                result: asn1.result,\n            };\n            if (inputSchema.name) {\n                inputSchema.name = inputSchema.name.replace(/^\\s+|\\s+$/g, EMPTY_STRING);\n                if (inputSchema.name) {\n                    delete root[inputSchema.name];\n                    _result.name = inputSchema.name;\n                }\n            }\n            return _result;\n        }\n        return compareSchema(root, asn1.result, inputSchema.primitiveSchema);\n    }\n    return {\n        verified: true,\n        result: root,\n    };\n}\nfunction verifySchema(inputBuffer, inputSchema) {\n    if ((inputSchema instanceof Object) === false) {\n        return {\n            verified: false,\n            result: { error: \"Wrong ASN.1 schema type\" },\n        };\n    }\n    const asn1 = localFromBER(pvtsutils__WEBPACK_IMPORTED_MODULE_0__.BufferSourceConverter.toUint8Array(inputBuffer));\n    if (asn1.offset === -1) {\n        return {\n            verified: false,\n            result: asn1.result,\n        };\n    }\n    return compareSchema(asn1.result, asn1.result, inputSchema);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/asn1js/build/index.es.js\n");

/***/ })

};
;