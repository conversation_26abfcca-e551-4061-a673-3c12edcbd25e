"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_RealTimeMap_js"],{

/***/ "(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Person.js":
/*!************************************************************!*\
  !*** ../../node_modules/@mui/icons-material/esm/Person.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _utils_createSvgIcon__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/createSvgIcon */ \"(app-pages-browser)/../../node_modules/@mui/material/utils/createSvgIcon.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n/* harmony default export */ __webpack_exports__[\"default\"] = ((0,_utils_createSvgIcon__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n    d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4m0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4\"\n}), \"Person\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9pY29ucy1tYXRlcmlhbC9lc20vUGVyc29uLmpzIiwibWFwcGluZ3MiOiI7Ozs2REFFa0Q7QUFDRjtBQUNoRCwrREFBZUEsZ0VBQWFBLENBQUUsV0FBVyxHQUFFRSxzREFBSUEsQ0FBQyxRQUFRO0lBQ3REQyxHQUFHO0FBQ0wsSUFBSSxXQUFVIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9pY29ucy1tYXRlcmlhbC9lc20vUGVyc29uLmpzPzFlYjAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCBjcmVhdGVTdmdJY29uIGZyb20gJy4vdXRpbHMvY3JlYXRlU3ZnSWNvbic7XG5pbXBvcnQgeyBqc3ggYXMgX2pzeCB9IGZyb20gXCJyZWFjdC9qc3gtcnVudGltZVwiO1xuZXhwb3J0IGRlZmF1bHQgY3JlYXRlU3ZnSWNvbiggLyojX19QVVJFX18qL19qc3goXCJwYXRoXCIsIHtcbiAgZDogXCJNMTIgMTJjMi4yMSAwIDQtMS43OSA0LTRzLTEuNzktNC00LTQtNCAxLjc5LTQgNCAxLjc5IDQgNCA0bTAgMmMtMi42NyAwLTggMS4zNC04IDR2MmgxNnYtMmMwLTIuNjYtNS4zMy00LTgtNFwiXG59KSwgJ1BlcnNvbicpOyJdLCJuYW1lcyI6WyJjcmVhdGVTdmdJY29uIiwianN4IiwiX2pzeCIsImQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Person.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/RealTimeMap.js":
/*!***************************************!*\
  !*** ./src/components/RealTimeMap.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LocationOn,Person!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LocationOn,Person!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Person.js\");\n// BahinLink Real-time Map Component\n// ⚠️ CRITICAL: Real GPS coordinates and live agent tracking ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst RealTimeMap = (param)=>{\n    let { agentLocations = [], sites = [], height = 400 } = param;\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate map loading\n        const timer = setTimeout(()=>{\n            setIsLoaded(true);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, []);\n    const getAgentStatusColor = (status)=>{\n        switch(status === null || status === void 0 ? void 0 : status.toLowerCase()){\n            case \"on_duty\":\n                return \"#4caf50\";\n            case \"on_break\":\n                return \"#ff9800\";\n            case \"off_duty\":\n                return \"#757575\";\n            default:\n                return \"#2196f3\";\n        }\n    };\n    const formatLastUpdate = (timestamp)=>{\n        if (!timestamp) return \"Unknown\";\n        const now = new Date();\n        const updateTime = new Date(timestamp);\n        const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));\n        if (diffMinutes < 1) return \"Just now\";\n        if (diffMinutes < 60) return \"\".concat(diffMinutes, \"m ago\");\n        const diffHours = Math.floor(diffMinutes / 60);\n        if (diffHours < 24) return \"\".concat(diffHours, \"h ago\");\n        return updateTime.toLocaleDateString();\n    };\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            sx: {\n                height,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: \"Loading real-time map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        height: height,\n        width: \"100%\",\n        sx: {\n            backgroundColor: \"#f5f5f5\",\n            border: \"1px solid #ddd\",\n            borderRadius: 1,\n            p: 2,\n            overflow: \"auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                display: \"flex\",\n                alignItems: \"center\",\n                mb: 2,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        sx: {\n                            mr: 1,\n                            color: \"#1976d2\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        variant: \"h6\",\n                        children: \"Real-time Agent Locations (Dakar, Senegal)\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            agentLocations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                color: \"text.secondary\",\n                textAlign: \"center\",\n                py: 4,\n                children: \"No agents currently active\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: agentLocations.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        sx: {\n                            backgroundColor: \"white\",\n                            border: \"1px solid #e0e0e0\",\n                            borderRadius: 1,\n                            p: 2,\n                            mb: 2,\n                            \"&:last-child\": {\n                                mb: 0\n                            }\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                justifyContent: \"space-between\",\n                                mb: 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                sx: {\n                                                    mr: 1,\n                                                    color: \"#1976d2\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                variant: \"subtitle1\",\n                                                fontWeight: \"bold\",\n                                                children: agent.agentName\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        label: agent.status,\n                                        size: \"small\",\n                                        sx: {\n                                            backgroundColor: getAgentStatusColor(agent.status),\n                                            color: \"white\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                gutterBottom: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Employee ID:\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \",\n                                    agent.employeeId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, undefined),\n                            agent.siteName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                gutterBottom: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Current Site:\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \" \",\n                                    agent.siteName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                gutterBottom: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Location:\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \",\n                                    agent.latitude.toFixed(4),\n                                    \"\\xb0, \",\n                                    agent.longitude.toFixed(4),\n                                    \"\\xb0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"text.secondary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Last Update:\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \" \",\n                                    formatLastUpdate(agent.lastUpdate)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 133,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, agent.id, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                sx: {\n                    backgroundColor: \"white\",\n                    border: \"1px solid #e0e0e0\",\n                    borderRadius: 1,\n                    p: 2,\n                    mt: 2\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        variant: \"subtitle2\",\n                        gutterBottom: true,\n                        children: \"Status Legend\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        display: \"flex\",\n                        gap: 3,\n                        flexWrap: \"wrap\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        width: 12,\n                                        height: 12,\n                                        bgcolor: \"#4caf50\",\n                                        borderRadius: \"50%\",\n                                        mr: 1\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"caption\",\n                                        children: \"On Duty\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        width: 12,\n                                        height: 12,\n                                        bgcolor: \"#ff9800\",\n                                        borderRadius: \"50%\",\n                                        mr: 1\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"caption\",\n                                        children: \"On Break\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        width: 12,\n                                        height: 12,\n                                        bgcolor: \"#757575\",\n                                        borderRadius: \"50%\",\n                                        mr: 1\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        variant: \"caption\",\n                                        children: \"Off Duty\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RealTimeMap, \"e/1lVN3R6kIvuSIAmUIHNmZXQsc=\");\n_c = RealTimeMap;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RealTimeMap);\nvar _c;\n$RefreshReg$(_c, \"RealTimeMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RealTimeMap.js\n"));

/***/ })

}]);