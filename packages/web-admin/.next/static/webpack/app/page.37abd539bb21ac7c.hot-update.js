"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    var _user_firstName, _user_primaryEmailAddress, _user_firstName1;\n    _s();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileOpen, setMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: \"1px solid \".concat(colors.border),\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 227,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 227,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                title: isCollapsed ? \"\".concat(item.label, \" - \").concat(item.description) : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.3)) : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 292,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 264,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 259,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: \"1px solid \".concat(colors.border),\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.border, 0.5))\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                    alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: \"2px solid \".concat(colors.accent),\n                                        boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2))\n                                    },\n                                    children: user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.firstName,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user === null || user === void 0 ? void 0 : (_user_primaryEmailAddress = user.primaryEmailAddress) === null || _user_primaryEmailAddress === void 0 ? void 0 : _user_primaryEmailAddress.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"\".concat(user === null || user === void 0 ? void 0 : user.fullName, \" - Administrator\"),\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: \"2px solid \".concat(colors.accent),\n                                    boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2)),\n                                    cursor: \"pointer\"\n                                },\n                                children: user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 487,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 503,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 502,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 485,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModernSidebar, \"oa3mGlDAkjwbwakIjhs4S00aZjU=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = ModernSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModernSidebar);\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModernSidebar.js\n"));

/***/ })

});