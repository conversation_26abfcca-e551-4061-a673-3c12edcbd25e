"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/../../node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ useMediaQuery; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _mui_utils_useEnhancedEffect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @mui/utils/useEnhancedEffect */ \"(app-pages-browser)/../../node_modules/@mui/utils/esm/useEnhancedEffect/useEnhancedEffect.js\");\n/* harmony import */ var _useThemeProps__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../useThemeProps */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/useThemeProps/getThemeProps.js\");\n/* harmony import */ var _useThemeWithoutDefault__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../useThemeWithoutDefault */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/useThemeWithoutDefault.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n/**\n * @deprecated Not used internally. Use `MediaQueryListEvent` from lib.dom.d.ts instead.\n */ /**\n * @deprecated Not used internally. Use `MediaQueryList` from lib.dom.d.ts instead.\n */ /**\n * @deprecated Not used internally. Use `(event: MediaQueryListEvent) => void` instead.\n */ function useMediaQueryOld(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n    _s();\n    const [match, setMatch] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>{\n        if (noSsr && matchMedia) {\n            return matchMedia(query).matches;\n        }\n        if (ssrMatchMedia) {\n            return ssrMatchMedia(query).matches;\n        }\n        // Once the component is mounted, we rely on the\n        // event listeners to return the correct matches value.\n        return defaultMatches;\n    });\n    (0,_mui_utils_useEnhancedEffect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>{\n        let active = true;\n        if (!matchMedia) {\n            return undefined;\n        }\n        const queryList = matchMedia(query);\n        const updateMatch = ()=>{\n            // Workaround Safari wrong implementation of matchMedia\n            // TODO can we remove it?\n            // https://github.com/mui/material-ui/pull/17315#issuecomment-528286677\n            if (active) {\n                setMatch(queryList.matches);\n            }\n        };\n        updateMatch();\n        // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n        queryList.addListener(updateMatch);\n        return ()=>{\n            active = false;\n            queryList.removeListener(updateMatch);\n        };\n    }, [\n        query,\n        matchMedia\n    ]);\n    return match;\n}\n_s(useMediaQueryOld, \"Q0b1ML5O7AprPRq3CMRIv6uGhZI=\", false, function() {\n    return [\n        _mui_utils_useEnhancedEffect__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n    ];\n});\n// eslint-disable-next-line no-useless-concat -- Workaround for https://github.com/webpack/webpack/issues/14814\nconst maybeReactUseSyncExternalStore = react__WEBPACK_IMPORTED_MODULE_0__[\"useSyncExternalStore\" + \"\"];\nfunction useMediaQueryNew(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr) {\n    _s1();\n    const getDefaultSnapshot = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>defaultMatches, [\n        defaultMatches\n    ]);\n    const getServerSnapshot = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        if (noSsr && matchMedia) {\n            return ()=>matchMedia(query).matches;\n        }\n        if (ssrMatchMedia !== null) {\n            const { matches } = ssrMatchMedia(query);\n            return ()=>matches;\n        }\n        return getDefaultSnapshot;\n    }, [\n        getDefaultSnapshot,\n        query,\n        ssrMatchMedia,\n        noSsr,\n        matchMedia\n    ]);\n    const [getSnapshot, subscribe] = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        if (matchMedia === null) {\n            return [\n                getDefaultSnapshot,\n                ()=>()=>{}\n            ];\n        }\n        const mediaQueryList = matchMedia(query);\n        return [\n            ()=>mediaQueryList.matches,\n            (notify)=>{\n                // TODO: Use `addEventListener` once support for Safari < 14 is dropped\n                mediaQueryList.addListener(notify);\n                return ()=>{\n                    mediaQueryList.removeListener(notify);\n                };\n            }\n        ];\n    }, [\n        getDefaultSnapshot,\n        matchMedia,\n        query\n    ]);\n    const match = maybeReactUseSyncExternalStore(subscribe, getSnapshot, getServerSnapshot);\n    return match;\n}\n_s1(useMediaQueryNew, \"JQIMRqFsEA+vZSzXkqT6PuqW0P0=\");\nfunction useMediaQuery(queryInput) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    _s2();\n    const theme = (0,_useThemeWithoutDefault__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    // Wait for jsdom to support the match media feature.\n    // All the browsers MUI support have this built-in.\n    // This defensive check is here for simplicity.\n    // Most of the time, the match media logic isn't central to people tests.\n    const supportMatchMedia = typeof window !== \"undefined\" && typeof window.matchMedia !== \"undefined\";\n    const { defaultMatches = false, matchMedia = supportMatchMedia ? window.matchMedia : null, ssrMatchMedia = null, noSsr = false } = (0,_useThemeProps__WEBPACK_IMPORTED_MODULE_3__[\"default\"])({\n        name: \"MuiUseMediaQuery\",\n        props: options,\n        theme\n    });\n    if (true) {\n        if (typeof queryInput === \"function\" && theme === null) {\n            console.error([\n                \"MUI: The `query` argument provided is invalid.\",\n                \"You are providing a function without a theme in the context.\",\n                \"One of the parent elements needs to use a ThemeProvider.\"\n            ].join(\"\\n\"));\n        }\n    }\n    let query = typeof queryInput === \"function\" ? queryInput(theme) : queryInput;\n    query = query.replace(/^@media( ?)/m, \"\");\n    // TODO: Drop `useMediaQueryOld` and use  `use-sync-external-store` shim in `useMediaQueryNew` once the package is stable\n    const useMediaQueryImplementation = maybeReactUseSyncExternalStore !== undefined ? useMediaQueryNew : useMediaQueryOld;\n    const match = useMediaQueryImplementation(query, defaultMatches, matchMedia, ssrMatchMedia, noSsr);\n    if (true) {\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue({\n            query,\n            match\n        });\n    }\n    return match;\n}\n_s2(useMediaQuery, \"emcJxWUFMMUQjTbBbsllpc1DflE=\", true, function() {\n    return [\n        _useThemeWithoutDefault__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n    ];\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/@mui/system/esm/useThemeProps/getThemeProps.js":
/*!*************************************************************************!*\
  !*** ../../node_modules/@mui/system/esm/useThemeProps/getThemeProps.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ getThemeProps; }\n/* harmony export */ });\n/* harmony import */ var _mui_utils_resolveProps__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mui/utils/resolveProps */ \"(app-pages-browser)/../../node_modules/@mui/utils/esm/resolveProps/resolveProps.js\");\n\nfunction getThemeProps(params) {\n  const {\n    theme,\n    name,\n    props\n  } = params;\n  if (!theme || !theme.components || !theme.components[name] || !theme.components[name].defaultProps) {\n    return props;\n  }\n  return (0,_mui_utils_resolveProps__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(theme.components[name].defaultProps, props);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvQG11aS9zeXN0ZW0vZXNtL3VzZVRoZW1lUHJvcHMvZ2V0VGhlbWVQcm9wcy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRDtBQUNwQztBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBLFNBQVMsbUVBQVk7QUFDckIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uL25vZGVfbW9kdWxlcy9AbXVpL3N5c3RlbS9lc20vdXNlVGhlbWVQcm9wcy9nZXRUaGVtZVByb3BzLmpzPzVkMmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHJlc29sdmVQcm9wcyBmcm9tICdAbXVpL3V0aWxzL3Jlc29sdmVQcm9wcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBnZXRUaGVtZVByb3BzKHBhcmFtcykge1xuICBjb25zdCB7XG4gICAgdGhlbWUsXG4gICAgbmFtZSxcbiAgICBwcm9wc1xuICB9ID0gcGFyYW1zO1xuICBpZiAoIXRoZW1lIHx8ICF0aGVtZS5jb21wb25lbnRzIHx8ICF0aGVtZS5jb21wb25lbnRzW25hbWVdIHx8ICF0aGVtZS5jb21wb25lbnRzW25hbWVdLmRlZmF1bHRQcm9wcykge1xuICAgIHJldHVybiBwcm9wcztcbiAgfVxuICByZXR1cm4gcmVzb2x2ZVByb3BzKHRoZW1lLmNvbXBvbmVudHNbbmFtZV0uZGVmYXVsdFByb3BzLCBwcm9wcyk7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/@mui/system/esm/useThemeProps/getThemeProps.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/useMediaQuery/useMediaQuery.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    var _user_firstName, _user_primaryEmailAddress, _user_firstName1;\n    _s();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const isMobile = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(theme.breakpoints.down(\"md\"));\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileOpen, setMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: \"1px solid \".concat(colors.border),\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 229,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 229,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 235,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                title: isCollapsed ? \"\".concat(item.label, \" - \").concat(item.description) : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.3)) : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 294,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 266,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 261,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 260,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 360,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 331,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: \"1px solid \".concat(colors.border),\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.border, 0.5))\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                    alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: \"2px solid \".concat(colors.accent),\n                                        boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2))\n                                    },\n                                    children: user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.firstName,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user === null || user === void 0 ? void 0 : (_user_primaryEmailAddress = user.primaryEmailAddress) === null || _user_primaryEmailAddress === void 0 ? void 0 : _user_primaryEmailAddress.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 411,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 480,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 464,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"\".concat(user === null || user === void 0 ? void 0 : user.fullName, \" - Administrator\"),\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: \"2px solid \".concat(colors.accent),\n                                    boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.accent, 0.2)),\n                                    cursor: \"pointer\"\n                                },\n                                children: user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 489,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_18__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 505,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 487,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModernSidebar, \"oa3mGlDAkjwbwakIjhs4S00aZjU=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c = ModernSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModernSidebar);\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01vZGVyblNpZGViYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxnREFBZ0Q7QUFDaEQseUVBQXlFOzs7QUFJeEM7QUFDd0I7QUFDUDtBQWtCM0I7QUFpQk07QUFFN0IsTUFBTW9DLHlCQUF5QjtBQUMvQixNQUFNQywwQkFBMEI7QUFFaEMsTUFBTUMsZ0JBQWdCO1FBd1dMQyxpQkFtREVBLDJCQXlDRkE7O0lBbmNmLE1BQU0sRUFBRUEsSUFBSSxFQUFFLEdBQUdwQyxzREFBT0E7SUFDeEIsTUFBTSxFQUFFcUMsT0FBTyxFQUFFLEdBQUdwQyx1REFBUUE7SUFDNUIsTUFBTXFDLFNBQVN4QywwREFBU0E7SUFDeEIsTUFBTXlDLFdBQVd4Qyw0REFBV0E7SUFDNUIsTUFBTXlDLFFBQVF6QixnT0FBUUE7SUFDdEIsTUFBTTBCLFdBQVd6QixnT0FBYUEsQ0FBQ3dCLE1BQU1FLFdBQVcsQ0FBQ0MsSUFBSSxDQUFDO0lBQ3RELE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHaEQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDaUQsWUFBWUMsY0FBYyxHQUFHbEQsK0NBQVFBLENBQUM7SUFFN0MsaUVBQWlFO0lBQ2pFLE1BQU1tRCxTQUFTO1FBQ2JDLFNBQVM7UUFDVEMsY0FBYztRQUNkQyxRQUFRO1FBQ1JDLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxlQUFlO1FBQ2ZDLFdBQVc7UUFDWEMsUUFBUTtRQUNSQyxTQUFTO1FBQ1RDLFNBQVM7UUFDVEMsT0FBTyxVQUFVLGFBQWE7SUFDaEM7SUFFQSxNQUFNQyxrQkFBa0I7UUFDdEI7WUFDRUMsT0FBTztZQUNQQyxNQUFNNUMsd1BBQVNBO1lBQ2Y2QyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsTUFBTTNDLHdQQUFNQTtZQUNaNEMsTUFBTTtZQUNOQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLE1BQU0xQyx3UEFBVUE7WUFDaEIyQyxNQUFNO1lBQ05DLGFBQWE7UUFDZjtRQUNBO1lBQ0VILE9BQU87WUFDUEMsTUFBTXpDLHdQQUFVQTtZQUNoQjBDLE1BQU07WUFDTkMsYUFBYTtRQUNmO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxNQUFNbEMseVBBQVVBO1lBQ2hCbUMsTUFBTTtZQUNOQyxhQUFhO1FBQ2Y7UUFDQTtZQUNFSCxPQUFPO1lBQ1BDLE1BQU14Qyx5UEFBU0E7WUFDZnlDLE1BQU07WUFDTkMsYUFBYTtRQUNmO1FBQ0E7WUFDRUgsT0FBTztZQUNQQyxNQUFNckMseVBBQWFBO1lBQ25Cc0MsTUFBTTtZQUNOQyxhQUFhO1FBQ2Y7S0FDRDtJQUVELE1BQU1DLG1CQUFtQixDQUFDRjtRQUN4QnpCLE9BQU80QixJQUFJLENBQUNIO0lBQ2Q7SUFFQSxNQUFNSSxnQkFBZ0I7UUFDcEIsTUFBTTlCO1FBQ05DLE9BQU80QixJQUFJLENBQUM7SUFDZDtJQUVBLE1BQU1FLGdCQUFnQjtRQUNwQnZCLGVBQWUsQ0FBQ0Q7SUFDbEI7SUFFQSxNQUFNeUIsV0FBVyxDQUFDTjtRQUNoQixPQUFPeEIsYUFBYXdCO0lBQ3RCO0lBRUEsTUFBTU8sZUFBZTFCLGNBQWNWLDBCQUEwQkQ7SUFFN0QscUJBQ0UsOERBQUM5Qiw2TkFBTUE7UUFDTG9FLFNBQVE7UUFDUkMsSUFBSTtZQUNGQyxPQUFPSDtZQUNQSSxZQUFZO1lBQ1osc0JBQXNCO2dCQUNwQkQsT0FBT0g7Z0JBQ1BLLFdBQVc7Z0JBQ1hDLGlCQUFpQjVCLE9BQU9DLE9BQU87Z0JBQy9CNEIsYUFBYSxhQUEyQixPQUFkN0IsT0FBT1EsTUFBTTtnQkFDdkNzQixZQUFZdEMsTUFBTXVDLFdBQVcsQ0FBQ0MsTUFBTSxDQUFDLFNBQVM7b0JBQzVDQyxRQUFRekMsTUFBTXVDLFdBQVcsQ0FBQ0UsTUFBTSxDQUFDQyxLQUFLO29CQUN0Q0MsVUFBVTNDLE1BQU11QyxXQUFXLENBQUNJLFFBQVEsQ0FBQ0MsY0FBYztnQkFDckQ7Z0JBQ0FDLFdBQVc7Z0JBQ1gsZ0JBQWdCO2dCQUNoQkMsV0FBVztZQUNiO1FBQ0Y7OzBCQUdBLDhEQUFDcEYsNk5BQUdBO2dCQUNGc0UsSUFBSTtvQkFDRmUsR0FBRztvQkFDSEMsU0FBUztvQkFDVEMsWUFBWTtvQkFDWkMsZ0JBQWdCOUMsY0FBYyxXQUFXO29CQUN6QytDLGNBQWMsYUFBMkIsT0FBZDNDLE9BQU9RLE1BQU07b0JBQ3hDb0MsV0FBVztnQkFDYjs7b0JBRUMsQ0FBQ2hELDZCQUNBLDhEQUFDMUMsNk5BQUdBO3dCQUFDc0YsU0FBUTt3QkFBT0MsWUFBVzs7MENBQzdCLDhEQUFDL0QseVBBQU1BO2dDQUNMOEMsSUFBSTtvQ0FDRnFCLFVBQVU7b0NBQ1ZDLE9BQU85QyxPQUFPRyxNQUFNO29DQUNwQjRDLElBQUk7b0NBQ0pDLFFBQVE7Z0NBQ1Y7Ozs7OzswQ0FFRiw4REFBQzlGLDZOQUFHQTs7a0RBQ0YsOERBQUNPLDZOQUFVQTt3Q0FDVDhELFNBQVE7d0NBQ1JDLElBQUk7NENBQ0ZzQixPQUFPOUMsT0FBT0ssSUFBSTs0Q0FDbEI0QyxZQUFZOzRDQUNaSixVQUFVOzRDQUNWSyxlQUFlO3dDQUNqQjtrREFDRDs7Ozs7O2tEQUdELDhEQUFDekYsNk5BQVVBO3dDQUNUOEQsU0FBUTt3Q0FDUkMsSUFBSTs0Q0FDRnNCLE9BQU85QyxPQUFPTSxhQUFhOzRDQUMzQnVDLFVBQVU7NENBQ1ZJLFlBQVk7d0NBQ2Q7a0RBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQkFPTnJELDZCQUNDLDhEQUFDbEIseVBBQU1BO3dCQUNMOEMsSUFBSTs0QkFDRnFCLFVBQVU7NEJBQ1ZDLE9BQU85QyxPQUFPRyxNQUFNOzRCQUNwQjZDLFFBQVE7d0JBQ1Y7Ozs7OztrQ0FJSiw4REFBQ3JGLDZOQUFVQTt3QkFDVHdGLFNBQVMvQjt3QkFDVEksSUFBSTs0QkFDRnNCLE9BQU85QyxPQUFPTSxhQUFhOzRCQUMzQnNCLGlCQUFpQjNELDROQUFLQSxDQUFDK0IsT0FBT0UsWUFBWSxFQUFFOzRCQUM1Q3VCLE9BQU87NEJBQ1AyQixRQUFROzRCQUNSLFdBQVc7Z0NBQ1R4QixpQkFBaUIzRCw0TkFBS0EsQ0FBQytCLE9BQU9FLFlBQVksRUFBRTtnQ0FDNUM0QyxPQUFPOUMsT0FBT0ssSUFBSTs0QkFDcEI7NEJBQ0F5QixZQUFZO3dCQUNkO2tDQUVDbEMsNEJBQWMsOERBQUNkLHlQQUFZQTs7OztzREFBTSw4REFBQ0QseVBBQVdBOzs7Ozs7Ozs7Ozs7Ozs7OzBCQUtsRCw4REFBQzNCLDZOQUFHQTtnQkFBQ3NFLElBQUk7b0JBQUU2QixJQUFJO29CQUFHQyxJQUFJO2dCQUFFOzBCQUN0Qiw0RUFBQ3hGLDZOQUFJQTtvQkFDSCtDLE9BQU07b0JBQ04wQyxNQUFLO29CQUNML0IsSUFBSTt3QkFDRkksaUJBQWlCNUIsT0FBT1MsT0FBTzt3QkFDL0JxQyxPQUFPO3dCQUNQRyxZQUFZO3dCQUNaSixVQUFVO3dCQUNWTyxRQUFRO3dCQUNSLG9CQUFvQjs0QkFDbEJDLElBQUk7d0JBQ047d0JBQ0FiLFNBQVM1QyxjQUFjLFNBQVM7b0JBQ2xDOzs7Ozs7Ozs7OzswQkFLSiw4REFBQzFDLDZOQUFHQTtnQkFBQ3NFLElBQUk7b0JBQUVnQyxNQUFNO29CQUFHSCxJQUFJO2dCQUFFOzBCQUN4Qiw0RUFBQ2pHLDZOQUFJQTtvQkFBQ29FLElBQUk7d0JBQUU4QixJQUFJO29CQUFFOzhCQUNmMUMsZ0JBQWdCNkMsR0FBRyxDQUFDLENBQUNDO3dCQUNwQixNQUFNQyxPQUFPRCxLQUFLNUMsSUFBSTt3QkFDdEIsTUFBTThDLFNBQVN2QyxTQUFTcUMsS0FBSzNDLElBQUk7d0JBRWpDLHFCQUNFLDhEQUFDMUQsNk5BQVFBOzRCQUFpQndHLGNBQWM7NEJBQUNyQyxJQUFJO2dDQUFFc0MsSUFBSTs0QkFBSTtzQ0FDckQsNEVBQUNqRyw2TkFBT0E7Z0NBQ05rRyxPQUFPbkUsY0FBYyxHQUFtQjhELE9BQWhCQSxLQUFLN0MsS0FBSyxFQUFDLE9BQXNCLE9BQWpCNkMsS0FBSzFDLFdBQVcsSUFBSztnQ0FDN0RnRCxXQUFVO2dDQUNWQyxLQUFLOzBDQUVMLDRFQUFDM0csNk5BQWNBO29DQUNiNkYsU0FBUyxJQUFNbEMsaUJBQWlCeUMsS0FBSzNDLElBQUk7b0NBQ3pDUyxJQUFJO3dDQUNGMEMsY0FBYzt3Q0FDZEMsSUFBSTt3Q0FDSmQsSUFBSTt3Q0FDSkMsSUFBSTt3Q0FDSlYsV0FBVzt3Q0FDWGhCLGlCQUFpQmdDLFNBQVMzRiw0TkFBS0EsQ0FBQytCLE9BQU9HLE1BQU0sRUFBRSxRQUFRO3dDQUN2REssUUFBUW9ELFNBQVMsYUFBdUMsT0FBMUIzRiw0TkFBS0EsQ0FBQytCLE9BQU9HLE1BQU0sRUFBRSxRQUFTO3dDQUM1RCxXQUFXOzRDQUNUeUIsaUJBQWlCZ0MsU0FDYjNGLDROQUFLQSxDQUFDK0IsT0FBT0csTUFBTSxFQUFFLE9BQ3JCbEMsNE5BQUtBLENBQUMrQixPQUFPRSxZQUFZLEVBQUU7NENBQy9Ca0UsV0FBVzt3Q0FDYjt3Q0FDQXRDLFlBQVk7d0NBQ1pZLGdCQUFnQjlDLGNBQWMsV0FBVztvQ0FDM0M7O3NEQUVBLDhEQUFDckMsNk5BQVlBOzRDQUNYaUUsSUFBSTtnREFDRnNCLE9BQU9jLFNBQVM1RCxPQUFPRyxNQUFNLEdBQUdILE9BQU9NLGFBQWE7Z0RBQ3BEK0QsVUFBVXpFLGNBQWMsU0FBUztnREFDakNtRCxJQUFJbkQsY0FBYyxJQUFJO2dEQUN0QmtDLFlBQVk7NENBQ2Q7c0RBRUEsNEVBQUM2QjtnREFBS25DLElBQUk7b0RBQUVxQixVQUFVO2dEQUFHOzs7Ozs7Ozs7Ozt3Q0FHMUIsQ0FBQ2pELDZCQUNBLDhEQUFDcEMsNk5BQVlBOzRDQUNYOEcsU0FBU1osS0FBSzdDLEtBQUs7NENBQ25CMEQsV0FBV2IsS0FBSzFDLFdBQVc7NENBQzNCd0Qsd0JBQXdCO2dEQUN0QmhELElBQUk7b0RBQ0ZzQixPQUFPYyxTQUFTNUQsT0FBT0ssSUFBSSxHQUFHTCxPQUFPTSxhQUFhO29EQUNsRDJDLFlBQVlXLFNBQVMsTUFBTTtvREFDM0JmLFVBQVU7b0RBQ1ZmLFlBQVk7Z0RBQ2Q7NENBQ0Y7NENBQ0EyQywwQkFBMEI7Z0RBQ3hCakQsSUFBSTtvREFDRnNCLE9BQU85QyxPQUFPTyxTQUFTO29EQUN2QnNDLFVBQVU7b0RBQ1Y2QixJQUFJO2dEQUNOOzRDQUNGOzs7Ozs7Ozs7Ozs7Ozs7OzsyQkF2REtoQixLQUFLM0MsSUFBSTs7Ozs7b0JBOEQ1Qjs7Ozs7Ozs7Ozs7MEJBS0osOERBQUM3RCw2TkFBR0E7Z0JBQUNzRSxJQUFJO29CQUFFNkIsSUFBSTtvQkFBR3NCLElBQUk7Z0JBQUU7O2tDQUN0Qiw4REFBQy9HLDZOQUFPQTt3QkFBQzRELElBQUk7NEJBQUVvRCxhQUFhNUUsT0FBT1EsTUFBTTs0QkFBRXNELElBQUk7d0JBQUU7Ozs7OztrQ0FFakQsOERBQUN6Ryw2TkFBUUE7d0JBQUN3RyxjQUFjO2tDQUN0Qiw0RUFBQ2hHLDZOQUFPQTs0QkFBQ2tHLE9BQU9uRSxjQUFjLGFBQWE7NEJBQUlvRSxXQUFVOzRCQUFRQyxLQUFLO3NDQUNwRSw0RUFBQzNHLDZOQUFjQTtnQ0FDYjZGLFNBQVMsSUFBTWxDLGlCQUFpQjtnQ0FDaENPLElBQUk7b0NBQ0YwQyxjQUFjO29DQUNkQyxJQUFJO29DQUNKZCxJQUFJO29DQUNKQyxJQUFJO29DQUNKVixXQUFXO29DQUNYaEIsaUJBQWlCckMsYUFBYSxjQUFjdEIsNE5BQUtBLENBQUMrQixPQUFPRyxNQUFNLEVBQUUsUUFBUTtvQ0FDekUsV0FBVzt3Q0FDVHlCLGlCQUFpQjNELDROQUFLQSxDQUFDK0IsT0FBT0UsWUFBWSxFQUFFO3dDQUM1Q2tFLFdBQVc7b0NBQ2I7b0NBQ0F0QyxZQUFZO29DQUNaWSxnQkFBZ0I5QyxjQUFjLFdBQVc7Z0NBQzNDOztrREFFQSw4REFBQ3JDLDZOQUFZQTt3Q0FDWGlFLElBQUk7NENBQ0ZzQixPQUFPdkQsYUFBYSxjQUFjUyxPQUFPRyxNQUFNLEdBQUdILE9BQU9NLGFBQWE7NENBQ3RFK0QsVUFBVXpFLGNBQWMsU0FBUzs0Q0FDakNtRCxJQUFJbkQsY0FBYyxJQUFJO3dDQUN4QjtrREFFQSw0RUFBQ3JCLHlQQUFRQTs0Q0FBQ2lELElBQUk7Z0RBQUVxQixVQUFVOzRDQUFHOzs7Ozs7Ozs7OztvQ0FHOUIsQ0FBQ2pELDZCQUNBLDhEQUFDcEMsNk5BQVlBO3dDQUNYOEcsU0FBUTt3Q0FDUkUsd0JBQXdCOzRDQUN0QmhELElBQUk7Z0RBQ0ZzQixPQUFPdkQsYUFBYSxjQUFjUyxPQUFPSyxJQUFJLEdBQUdMLE9BQU9NLGFBQWE7Z0RBQ3BFMkMsWUFBWTFELGFBQWEsY0FBYyxNQUFNO2dEQUM3Q3NELFVBQVU7NENBQ1o7d0NBQ0Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBU1osOERBQUMzRiw2TkFBR0E7Z0JBQ0ZzRSxJQUFJO29CQUNGNkIsSUFBSTtvQkFDSnNCLElBQUk7b0JBQ0pFLFdBQVcsYUFBMkIsT0FBZDdFLE9BQU9RLE1BQU07b0JBQ3JDc0UsSUFBSTtnQkFDTjswQkFFQyxDQUFDbEYsY0FDQSx3QkFBd0I7OEJBQ3hCLDhEQUFDMUMsNk5BQUdBO29CQUNGc0UsSUFBSTt3QkFDRkksaUJBQWlCM0QsNE5BQUtBLENBQUMrQixPQUFPRSxZQUFZLEVBQUU7d0JBQzVDZ0UsY0FBYzt3QkFDZDNCLEdBQUc7d0JBQ0g0QixJQUFJO3dCQUNKM0QsUUFBUSxhQUF1QyxPQUExQnZDLDROQUFLQSxDQUFDK0IsT0FBT1EsTUFBTSxFQUFFO29CQUM1Qzs7c0NBRUEsOERBQUN0RCw2TkFBR0E7NEJBQUNzRixTQUFROzRCQUFPQyxZQUFXOzRCQUFTcUIsSUFBSTs7OENBQzFDLDhEQUFDcEcsNk5BQU1BO29DQUNMcUgsR0FBRyxFQUFFM0YsaUJBQUFBLDJCQUFBQSxLQUFNNEYsUUFBUTtvQ0FDbkJDLEdBQUcsRUFBRTdGLGlCQUFBQSwyQkFBQUEsS0FBTThGLFFBQVE7b0NBQ25CMUQsSUFBSTt3Q0FDRkMsT0FBTzt3Q0FDUDJCLFFBQVE7d0NBQ1JMLElBQUk7d0NBQ0p2QyxRQUFRLGFBQTJCLE9BQWRSLE9BQU9HLE1BQU07d0NBQ2xDbUMsV0FBVyxhQUF1QyxPQUExQnJFLDROQUFLQSxDQUFDK0IsT0FBT0csTUFBTSxFQUFFO29DQUMvQzs4Q0FFQ2YsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTStGLFNBQVMsY0FBZi9GLHNDQUFBQSxnQkFBaUJnRyxNQUFNLENBQUM7Ozs7Ozs4Q0FHM0IsOERBQUNsSSw2TkFBR0E7b0NBQUNzRSxJQUFJO3dDQUFFZ0MsTUFBTTt3Q0FBR2EsVUFBVTtvQ0FBRTs7c0RBQzlCLDhEQUFDNUcsNk5BQVVBOzRDQUNUOEQsU0FBUTs0Q0FDUkMsSUFBSTtnREFDRnNCLE9BQU85QyxPQUFPSyxJQUFJO2dEQUNsQjRDLFlBQVk7Z0RBQ1pKLFVBQVU7Z0RBQ1Z3QyxZQUFZO2dEQUNaQyxVQUFVO2dEQUNWQyxjQUFjO2dEQUNkQyxZQUFZOzRDQUNkOztnREFFQ3BHLGlCQUFBQSwyQkFBQUEsS0FBTStGLFNBQVM7Z0RBQUM7Z0RBQUUvRixpQkFBQUEsMkJBQUFBLEtBQU1xRyxRQUFROzs7Ozs7O3NEQUduQyw4REFBQ3ZJLDZOQUFHQTs0Q0FBQ3NGLFNBQVE7NENBQU9DLFlBQVc7NENBQVNpQyxJQUFJOzs4REFDMUMsOERBQUMxRix5UEFBa0JBO29EQUNqQndDLElBQUk7d0RBQ0ZxQixVQUFVO3dEQUNWQyxPQUFPOUMsT0FBT0csTUFBTTt3REFDcEI0QyxJQUFJO29EQUNOOzs7Ozs7OERBRUYsOERBQUN0Riw2TkFBVUE7b0RBQ1Q4RCxTQUFRO29EQUNSQyxJQUFJO3dEQUNGc0IsT0FBTzlDLE9BQU9NLGFBQWE7d0RBQzNCdUMsVUFBVTt3REFDVkksWUFBWTtvREFDZDs4REFDRDs7Ozs7Ozs7Ozs7O3NEQUtILDhEQUFDeEYsNk5BQVVBOzRDQUNUOEQsU0FBUTs0Q0FDUkMsSUFBSTtnREFDRnNCLE9BQU85QyxPQUFPTyxTQUFTO2dEQUN2QnNDLFVBQVU7Z0RBQ1ZMLFNBQVM7Z0RBQ1RrQyxJQUFJO2dEQUNKWSxVQUFVO2dEQUNWQyxjQUFjO2dEQUNkQyxZQUFZOzRDQUNkO3NEQUVDcEcsaUJBQUFBLDRCQUFBQSw0QkFBQUEsS0FBTXNHLG1CQUFtQixjQUF6QnRHLGdEQUFBQSwwQkFBMkJ1RyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzlDLDhEQUFDaEksNk5BQVVBOzRCQUNUd0YsU0FBU2hDOzRCQUNUSyxJQUFJO2dDQUNGQyxPQUFPO2dDQUNQRyxpQkFBaUIzRCw0TkFBS0EsQ0FBQytCLE9BQU9XLEtBQUssRUFBRTtnQ0FDckNtQyxPQUFPOUMsT0FBT1csS0FBSztnQ0FDbkJ1RCxjQUFjO2dDQUNkWixJQUFJO2dDQUNKLFdBQVc7b0NBQ1QxQixpQkFBaUIzRCw0TkFBS0EsQ0FBQytCLE9BQU9XLEtBQUssRUFBRTtvQ0FDckN5RCxXQUFXO2dDQUNiO2dDQUNBdEMsWUFBWTs0QkFDZDs7OENBRUEsOERBQUN0RCx5UEFBU0E7b0NBQUNnRCxJQUFJO3dDQUFFcUIsVUFBVTt3Q0FBSUUsSUFBSTtvQ0FBRTs7Ozs7OzhDQUNyQyw4REFBQ3RGLDZOQUFVQTtvQ0FBQzhELFNBQVE7b0NBQVVDLElBQUk7d0NBQUV5QixZQUFZO29DQUFJOzhDQUFHOzs7Ozs7Ozs7Ozs7Ozs7OztnQ0FNM0QseUJBQXlCOzhCQUN6Qiw4REFBQy9GLDZOQUFHQTtvQkFBQ3NGLFNBQVE7b0JBQU9vRCxlQUFjO29CQUFTbkQsWUFBVztvQkFBU29ELEtBQUs7O3NDQUNsRSw4REFBQ2hJLDZOQUFPQTs0QkFBQ2tHLE9BQU8sR0FBa0IsT0FBZjNFLGlCQUFBQSwyQkFBQUEsS0FBTThGLFFBQVEsRUFBQzs0QkFBbUJsQixXQUFVOzRCQUFRQyxLQUFLO3NDQUMxRSw0RUFBQ3ZHLDZOQUFNQTtnQ0FDTHFILEdBQUcsRUFBRTNGLGlCQUFBQSwyQkFBQUEsS0FBTTRGLFFBQVE7Z0NBQ25CQyxHQUFHLEVBQUU3RixpQkFBQUEsMkJBQUFBLEtBQU04RixRQUFRO2dDQUNuQjFELElBQUk7b0NBQ0ZDLE9BQU87b0NBQ1AyQixRQUFRO29DQUNSNUMsUUFBUSxhQUEyQixPQUFkUixPQUFPRyxNQUFNO29DQUNsQ21DLFdBQVcsYUFBdUMsT0FBMUJyRSw0TkFBS0EsQ0FBQytCLE9BQU9HLE1BQU0sRUFBRTtvQ0FDN0MyRixRQUFRO2dDQUNWOzBDQUVDMUcsaUJBQUFBLDRCQUFBQSxtQkFBQUEsS0FBTStGLFNBQVMsY0FBZi9GLHVDQUFBQSxpQkFBaUJnRyxNQUFNLENBQUM7Ozs7Ozs7Ozs7O3NDQUk3Qiw4REFBQ3ZILDZOQUFPQTs0QkFBQ2tHLE9BQU07NEJBQVdDLFdBQVU7NEJBQVFDLEtBQUs7c0NBQy9DLDRFQUFDdEcsNk5BQVVBO2dDQUNUd0YsU0FBU2hDO2dDQUNUSyxJQUFJO29DQUNGQyxPQUFPO29DQUNQMkIsUUFBUTtvQ0FDUnhCLGlCQUFpQjNELDROQUFLQSxDQUFDK0IsT0FBT1csS0FBSyxFQUFFO29DQUNyQ21DLE9BQU85QyxPQUFPVyxLQUFLO29DQUNuQixXQUFXO3dDQUNUaUIsaUJBQWlCM0QsNE5BQUtBLENBQUMrQixPQUFPVyxLQUFLLEVBQUU7d0NBQ3JDeUQsV0FBVztvQ0FDYjtvQ0FDQXRDLFlBQVk7Z0NBQ2Q7MENBRUEsNEVBQUN0RCx5UEFBU0E7b0NBQUNnRCxJQUFJO3dDQUFFcUIsVUFBVTtvQ0FBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUTlDO0dBL2RNMUQ7O1FBQ2FuQyxrREFBT0E7UUFDSkMsbURBQVFBO1FBQ2JILHNEQUFTQTtRQUNQQyx3REFBV0E7UUFDZGdCLDROQUFRQTtRQUNMQyw0TkFBYUE7OztLQU4xQm1CO0FBaWVOLCtEQUFlQSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL01vZGVyblNpZGViYXIuanM/YTRhNSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYWhpbkxpbmsgTW9kZXJuIFNpZGViYXIgTmF2aWdhdGlvbiBDb21wb25lbnRcbi8vIOKaoO+4jyBDUklUSUNBTDogU29waGlzdGljYXRlZCAyMDI0IGRlc2lnbiB3aXRoIHJlYWwgZGF0YSBpbnRlZ3JhdGlvbiBPTkxZXG5cbid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIsIHVzZVBhdGhuYW1lIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcbmltcG9ydCB7IHVzZVVzZXIsIHVzZUNsZXJrIH0gZnJvbSAnQGNsZXJrL25leHRqcyc7XG5pbXBvcnQge1xuICBCb3gsXG4gIERyYXdlcixcbiAgTGlzdCxcbiAgTGlzdEl0ZW0sXG4gIExpc3RJdGVtQnV0dG9uLFxuICBMaXN0SXRlbUljb24sXG4gIExpc3RJdGVtVGV4dCxcbiAgVHlwb2dyYXBoeSxcbiAgQXZhdGFyLFxuICBJY29uQnV0dG9uLFxuICBEaXZpZGVyLFxuICBUb29sdGlwLFxuICBDaGlwLFxuICB1c2VUaGVtZSxcbiAgdXNlTWVkaWFRdWVyeSxcbiAgYWxwaGFcbn0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5pbXBvcnQge1xuICBEYXNoYm9hcmQsXG4gIFBlb3BsZSxcbiAgTG9jYXRpb25PbixcbiAgQXNzaWdubWVudCxcbiAgQW5hbHl0aWNzLFxuICBTZXR0aW5ncyxcbiAgRXhpdFRvQXBwLFxuICBBY2NvdW50Q2lyY2xlLFxuICBTaGllbGQsXG4gIEJ1c2luZXNzLFxuICBBc3Nlc3NtZW50LFxuICBDaGV2cm9uTGVmdCxcbiAgQ2hldnJvblJpZ2h0LFxuICBOb3RpZmljYXRpb25zLFxuICBBZG1pblBhbmVsU2V0dGluZ3Ncbn0gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbCc7XG5cbmNvbnN0IFNJREVCQVJfV0lEVEhfRVhQQU5ERUQgPSAyODA7XG5jb25zdCBTSURFQkFSX1dJRFRIX0NPTExBUFNFRCA9IDcyO1xuXG5jb25zdCBNb2Rlcm5TaWRlYmFyID0gKCkgPT4ge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZVVzZXIoKTtcbiAgY29uc3QgeyBzaWduT3V0IH0gPSB1c2VDbGVyaygpO1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuICBjb25zdCB0aGVtZSA9IHVzZVRoZW1lKCk7XG4gIGNvbnN0IGlzTW9iaWxlID0gdXNlTWVkaWFRdWVyeSh0aGVtZS5icmVha3BvaW50cy5kb3duKCdtZCcpKTtcbiAgY29uc3QgW2lzQ29sbGFwc2VkLCBzZXRJc0NvbGxhcHNlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFttb2JpbGVPcGVuLCBzZXRNb2JpbGVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyBNb2Rlcm4gMjAyNCBjb2xvciBwYWxldHRlIC0gc29waGlzdGljYXRlZCBuZXV0cmFscyB3aXRoIGFjY2VudFxuICBjb25zdCBjb2xvcnMgPSB7XG4gICAgc2lkZWJhcjogJyMxYTFkMjknLCAvLyBEZWVwIGNoYXJjb2FsXG4gICAgc2lkZWJhckhvdmVyOiAnIzI1MjkzNicsIC8vIExpZ2h0ZXIgY2hhcmNvYWxcbiAgICBhY2NlbnQ6ICcjNjM2NmYxJywgLy8gTW9kZXJuIGluZGlnb1xuICAgIGFjY2VudEhvdmVyOiAnIzU4NTVlYicsIC8vIERhcmtlciBpbmRpZ29cbiAgICB0ZXh0OiAnI2UyZThmMCcsIC8vIExpZ2h0IGdyYXlcbiAgICB0ZXh0U2Vjb25kYXJ5OiAnIzk0YTNiOCcsIC8vIE1lZGl1bSBncmF5XG4gICAgdGV4dE11dGVkOiAnIzY0NzQ4YicsIC8vIERhcmtlciBncmF5XG4gICAgYm9yZGVyOiAnIzMzNDE1NScsIC8vIEJvcmRlciBncmF5XG4gICAgc3VjY2VzczogJyMxMGI5ODEnLCAvLyBNb2Rlcm4gZ3JlZW5cbiAgICB3YXJuaW5nOiAnI2Y1OWUwYicsIC8vIE1vZGVybiBhbWJlclxuICAgIGVycm9yOiAnI2VmNDQ0NCcgLy8gTW9kZXJuIHJlZFxuICB9O1xuXG4gIGNvbnN0IG5hdmlnYXRpb25JdGVtcyA9IFtcbiAgICB7IFxuICAgICAgbGFiZWw6ICdEYXNoYm9hcmQnLCBcbiAgICAgIGljb246IERhc2hib2FyZCwgXG4gICAgICBwYXRoOiAnLycsIFxuICAgICAgZGVzY3JpcHRpb246ICdPdmVydmlldyAmIEFuYWx5dGljcydcbiAgICB9LFxuICAgIHsgXG4gICAgICBsYWJlbDogJ0FnZW50cycsIFxuICAgICAgaWNvbjogUGVvcGxlLCBcbiAgICAgIHBhdGg6ICcvYWdlbnRzJywgXG4gICAgICBkZXNjcmlwdGlvbjogJ1NlY3VyaXR5IFBlcnNvbm5lbCdcbiAgICB9LFxuICAgIHsgXG4gICAgICBsYWJlbDogJ1NpdGVzJywgXG4gICAgICBpY29uOiBMb2NhdGlvbk9uLCBcbiAgICAgIHBhdGg6ICcvc2l0ZXMnLCBcbiAgICAgIGRlc2NyaXB0aW9uOiAnQ2xpZW50IExvY2F0aW9ucydcbiAgICB9LFxuICAgIHsgXG4gICAgICBsYWJlbDogJ1NoaWZ0cycsIFxuICAgICAgaWNvbjogQXNzaWdubWVudCwgXG4gICAgICBwYXRoOiAnL3NoaWZ0cycsIFxuICAgICAgZGVzY3JpcHRpb246ICdTY2hlZHVsZSBNYW5hZ2VtZW50J1xuICAgIH0sXG4gICAgeyBcbiAgICAgIGxhYmVsOiAnUmVwb3J0cycsIFxuICAgICAgaWNvbjogQXNzZXNzbWVudCwgXG4gICAgICBwYXRoOiAnL3JlcG9ydHMnLCBcbiAgICAgIGRlc2NyaXB0aW9uOiAnU2VjdXJpdHkgUmVwb3J0cydcbiAgICB9LFxuICAgIHsgXG4gICAgICBsYWJlbDogJ0FuYWx5dGljcycsIFxuICAgICAgaWNvbjogQW5hbHl0aWNzLCBcbiAgICAgIHBhdGg6ICcvYW5hbHl0aWNzJywgXG4gICAgICBkZXNjcmlwdGlvbjogJ1BlcmZvcm1hbmNlIE1ldHJpY3MnXG4gICAgfSxcbiAgICB7IFxuICAgICAgbGFiZWw6ICdVc2VycycsIFxuICAgICAgaWNvbjogQWNjb3VudENpcmNsZSwgXG4gICAgICBwYXRoOiAnL3VzZXJzJywgXG4gICAgICBkZXNjcmlwdGlvbjogJ1VzZXIgTWFuYWdlbWVudCdcbiAgICB9XG4gIF07XG5cbiAgY29uc3QgaGFuZGxlTmF2aWdhdGlvbiA9IChwYXRoKSA9PiB7XG4gICAgcm91dGVyLnB1c2gocGF0aCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICBhd2FpdCBzaWduT3V0KCk7XG4gICAgcm91dGVyLnB1c2goJy9zaWduLWluJyk7XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlU2lkZWJhciA9ICgpID0+IHtcbiAgICBzZXRJc0NvbGxhcHNlZCghaXNDb2xsYXBzZWQpO1xuICB9O1xuXG4gIGNvbnN0IGlzQWN0aXZlID0gKHBhdGgpID0+IHtcbiAgICByZXR1cm4gcGF0aG5hbWUgPT09IHBhdGg7XG4gIH07XG5cbiAgY29uc3Qgc2lkZWJhcldpZHRoID0gaXNDb2xsYXBzZWQgPyBTSURFQkFSX1dJRFRIX0NPTExBUFNFRCA6IFNJREVCQVJfV0lEVEhfRVhQQU5ERUQ7XG5cbiAgcmV0dXJuIChcbiAgICA8RHJhd2VyXG4gICAgICB2YXJpYW50PVwicGVybWFuZW50XCJcbiAgICAgIHN4PXt7XG4gICAgICAgIHdpZHRoOiBzaWRlYmFyV2lkdGgsXG4gICAgICAgIGZsZXhTaHJpbms6IDAsXG4gICAgICAgICcmIC5NdWlEcmF3ZXItcGFwZXInOiB7XG4gICAgICAgICAgd2lkdGg6IHNpZGViYXJXaWR0aCxcbiAgICAgICAgICBib3hTaXppbmc6ICdib3JkZXItYm94JyxcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5zaWRlYmFyLFxuICAgICAgICAgIGJvcmRlclJpZ2h0OiBgMXB4IHNvbGlkICR7Y29sb3JzLmJvcmRlcn1gLFxuICAgICAgICAgIHRyYW5zaXRpb246IHRoZW1lLnRyYW5zaXRpb25zLmNyZWF0ZSgnd2lkdGgnLCB7XG4gICAgICAgICAgICBlYXNpbmc6IHRoZW1lLnRyYW5zaXRpb25zLmVhc2luZy5zaGFycCxcbiAgICAgICAgICAgIGR1cmF0aW9uOiB0aGVtZS50cmFuc2l0aW9ucy5kdXJhdGlvbi5lbnRlcmluZ1NjcmVlbixcbiAgICAgICAgICB9KSxcbiAgICAgICAgICBvdmVyZmxvd1g6ICdoaWRkZW4nLFxuICAgICAgICAgIC8vIE1vZGVybiBzaGFkb3dcbiAgICAgICAgICBib3hTaGFkb3c6ICc0cHggMCAyNHB4IHJnYmEoMCwgMCwgMCwgMC4xMiknLFxuICAgICAgICB9LFxuICAgICAgfX1cbiAgICA+XG4gICAgICB7LyogSGVhZGVyIFNlY3Rpb24gKi99XG4gICAgICA8Qm94XG4gICAgICAgIHN4PXt7XG4gICAgICAgICAgcDogMyxcbiAgICAgICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgICAgICAganVzdGlmeUNvbnRlbnQ6IGlzQ29sbGFwc2VkID8gJ2NlbnRlcicgOiAnc3BhY2UtYmV0d2VlbicsXG4gICAgICAgICAgYm9yZGVyQm90dG9tOiBgMXB4IHNvbGlkICR7Y29sb3JzLmJvcmRlcn1gLFxuICAgICAgICAgIG1pbkhlaWdodDogODBcbiAgICAgICAgfX1cbiAgICAgID5cbiAgICAgICAgeyFpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgPEJveCBkaXNwbGF5PVwiZmxleFwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIj5cbiAgICAgICAgICAgIDxTaGllbGQgXG4gICAgICAgICAgICAgIHN4PXt7IFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAzMiwgXG4gICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy5hY2NlbnQsIFxuICAgICAgICAgICAgICAgIG1yOiAyLFxuICAgICAgICAgICAgICAgIGZpbHRlcjogJ2Ryb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDk5LCAxMDIsIDI0MSwgMC4zKSknXG4gICAgICAgICAgICAgIH19IFxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IFxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJoNlwiIFxuICAgICAgICAgICAgICAgIHN4PXt7IFxuICAgICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0LFxuICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogNzAwLFxuICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcxLjI1cmVtJyxcbiAgICAgICAgICAgICAgICAgIGxldHRlclNwYWNpbmc6ICctMC4wMjVlbSdcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQmFoaW5MaW5rXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImNhcHRpb25cIiBcbiAgICAgICAgICAgICAgICBzeD17eyBcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC43NXJlbScsXG4gICAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDBcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgU2VjdXJpdHkgTWFuYWdlbWVudFxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8L0JveD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHtpc0NvbGxhcHNlZCAmJiAoXG4gICAgICAgICAgPFNoaWVsZCBcbiAgICAgICAgICAgIHN4PXt7IFxuICAgICAgICAgICAgICBmb250U2l6ZTogMjgsIFxuICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLmFjY2VudCxcbiAgICAgICAgICAgICAgZmlsdGVyOiAnZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoOTksIDEwMiwgMjQxLCAwLjMpKSdcbiAgICAgICAgICAgIH19IFxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG5cbiAgICAgICAgPEljb25CdXR0b25cbiAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVTaWRlYmFyfVxuICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICBjb2xvcjogY29sb3JzLnRleHRTZWNvbmRhcnksXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGFscGhhKGNvbG9ycy5zaWRlYmFySG92ZXIsIDAuNSksXG4gICAgICAgICAgICB3aWR0aDogMzIsXG4gICAgICAgICAgICBoZWlnaHQ6IDMyLFxuICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYWxwaGEoY29sb3JzLnNpZGViYXJIb3ZlciwgMC44KSxcbiAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UtaW4tb3V0J1xuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICB7aXNDb2xsYXBzZWQgPyA8Q2hldnJvblJpZ2h0IC8+IDogPENoZXZyb25MZWZ0IC8+fVxuICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICA8L0JveD5cblxuICAgICAgey8qIExpdmUgU3RhdHVzIEluZGljYXRvciAqL31cbiAgICAgIDxCb3ggc3g9e3sgcHg6IDMsIHB5OiAyIH19PlxuICAgICAgICA8Q2hpcFxuICAgICAgICAgIGxhYmVsPVwiTElWRVwiXG4gICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMuc3VjY2VzcyxcbiAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgZm9udFdlaWdodDogNjAwLFxuICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgIGhlaWdodDogMjQsXG4gICAgICAgICAgICAnJiAuTXVpQ2hpcC1sYWJlbCc6IHtcbiAgICAgICAgICAgICAgcHg6IDEuNVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGRpc3BsYXk6IGlzQ29sbGFwc2VkID8gJ25vbmUnIDogJ2ZsZXgnXG4gICAgICAgICAgfX1cbiAgICAgICAgLz5cbiAgICAgIDwvQm94PlxuXG4gICAgICB7LyogTmF2aWdhdGlvbiBJdGVtcyAqL31cbiAgICAgIDxCb3ggc3g9e3sgZmxleDogMSwgcHg6IDIgfX0+XG4gICAgICAgIDxMaXN0IHN4PXt7IHB5OiAwIH19PlxuICAgICAgICAgIHtuYXZpZ2F0aW9uSXRlbXMubWFwKChpdGVtKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBJY29uID0gaXRlbS5pY29uO1xuICAgICAgICAgICAgY29uc3QgYWN0aXZlID0gaXNBY3RpdmUoaXRlbS5wYXRoKTtcbiAgICAgICAgICAgIFxuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgPExpc3RJdGVtIGtleT17aXRlbS5wYXRofSBkaXNhYmxlUGFkZGluZyBzeD17eyBtYjogMC41IH19PlxuICAgICAgICAgICAgICAgIDxUb29sdGlwIFxuICAgICAgICAgICAgICAgICAgdGl0bGU9e2lzQ29sbGFwc2VkID8gYCR7aXRlbS5sYWJlbH0gLSAke2l0ZW0uZGVzY3JpcHRpb259YCA6ICcnfSBcbiAgICAgICAgICAgICAgICAgIHBsYWNlbWVudD1cInJpZ2h0XCJcbiAgICAgICAgICAgICAgICAgIGFycm93XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU5hdmlnYXRpb24oaXRlbS5wYXRoKX1cbiAgICAgICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDIsXG4gICAgICAgICAgICAgICAgICAgICAgbXg6IDEsXG4gICAgICAgICAgICAgICAgICAgICAgcHg6IDIsXG4gICAgICAgICAgICAgICAgICAgICAgcHk6IDEuNSxcbiAgICAgICAgICAgICAgICAgICAgICBtaW5IZWlnaHQ6IDQ4LFxuICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYWN0aXZlID8gYWxwaGEoY29sb3JzLmFjY2VudCwgMC4xNSkgOiAndHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgIGJvcmRlcjogYWN0aXZlID8gYDFweCBzb2xpZCAke2FscGhhKGNvbG9ycy5hY2NlbnQsIDAuMyl9YCA6ICcxcHggc29saWQgdHJhbnNwYXJlbnQnLFxuICAgICAgICAgICAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhY3RpdmUgXG4gICAgICAgICAgICAgICAgICAgICAgICAgID8gYWxwaGEoY29sb3JzLmFjY2VudCwgMC4yKSBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBhbHBoYShjb2xvcnMuc2lkZWJhckhvdmVyLCAwLjgpLFxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWCgycHgpJyxcbiAgICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlLWluLW91dCcsXG4gICAgICAgICAgICAgICAgICAgICAganVzdGlmeUNvbnRlbnQ6IGlzQ29sbGFwc2VkID8gJ2NlbnRlcicgOiAnZmxleC1zdGFydCdcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPExpc3RJdGVtSWNvblxuICAgICAgICAgICAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogYWN0aXZlID8gY29sb3JzLmFjY2VudCA6IGNvbG9ycy50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgICAgbWluV2lkdGg6IGlzQ29sbGFwc2VkID8gJ2F1dG8nIDogNDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBtcjogaXNDb2xsYXBzZWQgPyAwIDogMS41LFxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2NvbG9yIDAuMnMgZWFzZS1pbi1vdXQnXG4gICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgIDxJY29uIHN4PXt7IGZvbnRTaXplOiAyMiB9fSAvPlxuICAgICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHshaXNDb2xsYXBzZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxMaXN0SXRlbVRleHRcbiAgICAgICAgICAgICAgICAgICAgICAgIHByaW1hcnk9e2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgICAgICBzZWNvbmRhcnk9e2l0ZW0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgICAgICAgICBwcmltYXJ5VHlwb2dyYXBoeVByb3BzPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGFjdGl2ZSA/IGNvbG9ycy50ZXh0IDogY29sb3JzLnRleHRTZWNvbmRhcnksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFdlaWdodDogYWN0aXZlID8gNjAwIDogNTAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC44NzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdjb2xvciAwLjJzIGVhc2UtaW4tb3V0J1xuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgc2Vjb25kYXJ5VHlwb2dyYXBoeVByb3BzPXt7XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4OiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0TXV0ZWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjc1cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBtdDogMC4yNVxuICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICAgICAgPC9MaXN0SXRlbT5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvTGlzdD5cbiAgICAgIDwvQm94PlxuXG4gICAgICB7LyogU2V0dGluZ3MgU2VjdGlvbiAqL31cbiAgICAgIDxCb3ggc3g9e3sgcHg6IDIsIHBiOiAxIH19PlxuICAgICAgICA8RGl2aWRlciBzeD17eyBib3JkZXJDb2xvcjogY29sb3JzLmJvcmRlciwgbWI6IDIgfX0gLz5cblxuICAgICAgICA8TGlzdEl0ZW0gZGlzYWJsZVBhZGRpbmc+XG4gICAgICAgICAgPFRvb2x0aXAgdGl0bGU9e2lzQ29sbGFwc2VkID8gJ1NldHRpbmdzJyA6ICcnfSBwbGFjZW1lbnQ9XCJyaWdodFwiIGFycm93PlxuICAgICAgICAgICAgPExpc3RJdGVtQnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU5hdmlnYXRpb24oJy9zZXR0aW5ncycpfVxuICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgIGJvcmRlclJhZGl1czogMixcbiAgICAgICAgICAgICAgICBteDogMSxcbiAgICAgICAgICAgICAgICBweDogMixcbiAgICAgICAgICAgICAgICBweTogMS41LFxuICAgICAgICAgICAgICAgIG1pbkhlaWdodDogNDgsXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBwYXRobmFtZSA9PT0gJy9zZXR0aW5ncycgPyBhbHBoYShjb2xvcnMuYWNjZW50LCAwLjE1KSA6ICd0cmFuc3BhcmVudCcsXG4gICAgICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGFscGhhKGNvbG9ycy5zaWRlYmFySG92ZXIsIDAuOCksXG4gICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVYKDJweCknLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgdHJhbnNpdGlvbjogJ2FsbCAwLjJzIGVhc2UtaW4tb3V0JyxcbiAgICAgICAgICAgICAgICBqdXN0aWZ5Q29udGVudDogaXNDb2xsYXBzZWQgPyAnY2VudGVyJyA6ICdmbGV4LXN0YXJ0J1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TGlzdEl0ZW1JY29uXG4gICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBwYXRobmFtZSA9PT0gJy9zZXR0aW5ncycgPyBjb2xvcnMuYWNjZW50IDogY29sb3JzLnRleHRTZWNvbmRhcnksXG4gICAgICAgICAgICAgICAgICBtaW5XaWR0aDogaXNDb2xsYXBzZWQgPyAnYXV0bycgOiA0MCxcbiAgICAgICAgICAgICAgICAgIG1yOiBpc0NvbGxhcHNlZCA/IDAgOiAxLjVcbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIHN4PXt7IGZvbnRTaXplOiAyMiB9fSAvPlxuICAgICAgICAgICAgICA8L0xpc3RJdGVtSWNvbj5cblxuICAgICAgICAgICAgICB7IWlzQ29sbGFwc2VkICYmIChcbiAgICAgICAgICAgICAgICA8TGlzdEl0ZW1UZXh0XG4gICAgICAgICAgICAgICAgICBwcmltYXJ5PVwiU2V0dGluZ3NcIlxuICAgICAgICAgICAgICAgICAgcHJpbWFyeVR5cG9ncmFwaHlQcm9wcz17e1xuICAgICAgICAgICAgICAgICAgICBzeDoge1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBwYXRobmFtZSA9PT0gJy9zZXR0aW5ncycgPyBjb2xvcnMudGV4dCA6IGNvbG9ycy50ZXh0U2Vjb25kYXJ5LFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IHBhdGhuYW1lID09PSAnL3NldHRpbmdzJyA/IDYwMCA6IDUwMCxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuODc1cmVtJ1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0xpc3RJdGVtQnV0dG9uPlxuICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgPC9MaXN0SXRlbT5cbiAgICAgIDwvQm94PlxuXG4gICAgICB7LyogVXNlciBQcm9maWxlIFNlY3Rpb24gKi99XG4gICAgICA8Qm94XG4gICAgICAgIHN4PXt7XG4gICAgICAgICAgcHg6IDIsXG4gICAgICAgICAgcGI6IDMsXG4gICAgICAgICAgYm9yZGVyVG9wOiBgMXB4IHNvbGlkICR7Y29sb3JzLmJvcmRlcn1gLFxuICAgICAgICAgIHB0OiAyXG4gICAgICAgIH19XG4gICAgICA+XG4gICAgICAgIHshaXNDb2xsYXBzZWQgPyAoXG4gICAgICAgICAgLy8gRXhwYW5kZWQgUHJvZmlsZSBWaWV3XG4gICAgICAgICAgPEJveFxuICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhbHBoYShjb2xvcnMuc2lkZWJhckhvdmVyLCAwLjUpLFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIHA6IDIsXG4gICAgICAgICAgICAgIG14OiAxLFxuICAgICAgICAgICAgICBib3JkZXI6IGAxcHggc29saWQgJHthbHBoYShjb2xvcnMuYm9yZGVyLCAwLjUpfWBcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPEJveCBkaXNwbGF5PVwiZmxleFwiIGFsaWduSXRlbXM9XCJjZW50ZXJcIiBtYj17Mn0+XG4gICAgICAgICAgICAgIDxBdmF0YXJcbiAgICAgICAgICAgICAgICBzcmM9e3VzZXI/LmltYWdlVXJsfVxuICAgICAgICAgICAgICAgIGFsdD17dXNlcj8uZnVsbE5hbWV9XG4gICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiA0MCxcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogNDAsXG4gICAgICAgICAgICAgICAgICBtcjogMixcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDJweCBzb2xpZCAke2NvbG9ycy5hY2NlbnR9YCxcbiAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzogYDAgMCAwIDJweCAke2FscGhhKGNvbG9ycy5hY2NlbnQsIDAuMil9YFxuICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7dXNlcj8uZmlyc3ROYW1lPy5jaGFyQXQoMCl9XG4gICAgICAgICAgICAgIDwvQXZhdGFyPlxuXG4gICAgICAgICAgICAgIDxCb3ggc3g9e3sgZmxleDogMSwgbWluV2lkdGg6IDAgfX0+XG4gICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJib2R5MlwiXG4gICAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLnRleHQsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDYwMCxcbiAgICAgICAgICAgICAgICAgICAgZm9udFNpemU6ICcwLjg3NXJlbScsXG4gICAgICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDEuMixcbiAgICAgICAgICAgICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLFxuICAgICAgICAgICAgICAgICAgICB0ZXh0T3ZlcmZsb3c6ICdlbGxpcHNpcycsXG4gICAgICAgICAgICAgICAgICAgIHdoaXRlU3BhY2U6ICdub3dyYXAnXG4gICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHt1c2VyPy5maXJzdE5hbWV9IHt1c2VyPy5sYXN0TmFtZX1cbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG5cbiAgICAgICAgICAgICAgICA8Qm94IGRpc3BsYXk9XCJmbGV4XCIgYWxpZ25JdGVtcz1cImNlbnRlclwiIG10PXswLjV9PlxuICAgICAgICAgICAgICAgICAgPEFkbWluUGFuZWxTZXR0aW5nc1xuICAgICAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAxNCxcbiAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogY29sb3JzLmFjY2VudCxcbiAgICAgICAgICAgICAgICAgICAgICBtcjogMC41XG4gICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNhcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMudGV4dFNlY29uZGFyeSxcbiAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzAuNzVyZW0nLFxuICAgICAgICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMFxuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICBBZG1pbmlzdHJhdG9yXG4gICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImNhcHRpb25cIlxuICAgICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgICAgY29sb3I6IGNvbG9ycy50ZXh0TXV0ZWQsXG4gICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMC43cmVtJyxcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogJ2Jsb2NrJyxcbiAgICAgICAgICAgICAgICAgICAgbXQ6IDAuMjUsXG4gICAgICAgICAgICAgICAgICAgIG92ZXJmbG93OiAnaGlkZGVuJyxcbiAgICAgICAgICAgICAgICAgICAgdGV4dE92ZXJmbG93OiAnZWxsaXBzaXMnLFxuICAgICAgICAgICAgICAgICAgICB3aGl0ZVNwYWNlOiAnbm93cmFwJ1xuICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7dXNlcj8ucHJpbWFyeUVtYWlsQWRkcmVzcz8uZW1haWxBZGRyZXNzfVxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICA8L0JveD5cblxuICAgICAgICAgICAgPEljb25CdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2lnbk91dH1cbiAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICB3aWR0aDogJzEwMCUnLFxuICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogYWxwaGEoY29sb3JzLmVycm9yLCAwLjEpLFxuICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuZXJyb3IsXG4gICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAyLFxuICAgICAgICAgICAgICAgIHB5OiAxLFxuICAgICAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhbHBoYShjb2xvcnMuZXJyb3IsIDAuMiksXG4gICAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC0xcHgpJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4ycyBlYXNlLWluLW91dCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPEV4aXRUb0FwcCBzeD17eyBmb250U2l6ZTogMTgsIG1yOiAxIH19IC8+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJjYXB0aW9uXCIgc3g9e3sgZm9udFdlaWdodDogNjAwIH19PlxuICAgICAgICAgICAgICAgIFNpZ24gT3V0XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICAvLyBDb2xsYXBzZWQgUHJvZmlsZSBWaWV3XG4gICAgICAgICAgPEJveCBkaXNwbGF5PVwiZmxleFwiIGZsZXhEaXJlY3Rpb249XCJjb2x1bW5cIiBhbGlnbkl0ZW1zPVwiY2VudGVyXCIgZ2FwPXsxfT5cbiAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPXtgJHt1c2VyPy5mdWxsTmFtZX0gLSBBZG1pbmlzdHJhdG9yYH0gcGxhY2VtZW50PVwicmlnaHRcIiBhcnJvdz5cbiAgICAgICAgICAgICAgPEF2YXRhclxuICAgICAgICAgICAgICAgIHNyYz17dXNlcj8uaW1hZ2VVcmx9XG4gICAgICAgICAgICAgICAgYWx0PXt1c2VyPy5mdWxsTmFtZX1cbiAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgd2lkdGg6IDM2LFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiAzNixcbiAgICAgICAgICAgICAgICAgIGJvcmRlcjogYDJweCBzb2xpZCAke2NvbG9ycy5hY2NlbnR9YCxcbiAgICAgICAgICAgICAgICAgIGJveFNoYWRvdzogYDAgMCAwIDJweCAke2FscGhhKGNvbG9ycy5hY2NlbnQsIDAuMil9YCxcbiAgICAgICAgICAgICAgICAgIGN1cnNvcjogJ3BvaW50ZXInXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHt1c2VyPy5maXJzdE5hbWU/LmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICA8L1Rvb2x0aXA+XG5cbiAgICAgICAgICAgIDxUb29sdGlwIHRpdGxlPVwiU2lnbiBPdXRcIiBwbGFjZW1lbnQ9XCJyaWdodFwiIGFycm93PlxuICAgICAgICAgICAgICA8SWNvbkJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNpZ25PdXR9XG4gICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiAzNixcbiAgICAgICAgICAgICAgICAgIGhlaWdodDogMzYsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGFscGhhKGNvbG9ycy5lcnJvciwgMC4xKSxcbiAgICAgICAgICAgICAgICAgIGNvbG9yOiBjb2xvcnMuZXJyb3IsXG4gICAgICAgICAgICAgICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiBhbHBoYShjb2xvcnMuZXJyb3IsIDAuMiksXG4gICAgICAgICAgICAgICAgICAgIHRyYW5zZm9ybTogJ3NjYWxlKDEuMDUpJyxcbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuMnMgZWFzZS1pbi1vdXQnXG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxFeGl0VG9BcHAgc3g9e3sgZm9udFNpemU6IDE4IH19IC8+XG4gICAgICAgICAgICAgIDwvSWNvbkJ1dHRvbj5cbiAgICAgICAgICAgIDwvVG9vbHRpcD5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgKX1cbiAgICAgIDwvQm94PlxuICAgIDwvRHJhd2VyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTW9kZXJuU2lkZWJhcjtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZVJvdXRlciIsInVzZVBhdGhuYW1lIiwidXNlVXNlciIsInVzZUNsZXJrIiwiQm94IiwiRHJhd2VyIiwiTGlzdCIsIkxpc3RJdGVtIiwiTGlzdEl0ZW1CdXR0b24iLCJMaXN0SXRlbUljb24iLCJMaXN0SXRlbVRleHQiLCJUeXBvZ3JhcGh5IiwiQXZhdGFyIiwiSWNvbkJ1dHRvbiIsIkRpdmlkZXIiLCJUb29sdGlwIiwiQ2hpcCIsInVzZVRoZW1lIiwidXNlTWVkaWFRdWVyeSIsImFscGhhIiwiRGFzaGJvYXJkIiwiUGVvcGxlIiwiTG9jYXRpb25PbiIsIkFzc2lnbm1lbnQiLCJBbmFseXRpY3MiLCJTZXR0aW5ncyIsIkV4aXRUb0FwcCIsIkFjY291bnRDaXJjbGUiLCJTaGllbGQiLCJCdXNpbmVzcyIsIkFzc2Vzc21lbnQiLCJDaGV2cm9uTGVmdCIsIkNoZXZyb25SaWdodCIsIk5vdGlmaWNhdGlvbnMiLCJBZG1pblBhbmVsU2V0dGluZ3MiLCJTSURFQkFSX1dJRFRIX0VYUEFOREVEIiwiU0lERUJBUl9XSURUSF9DT0xMQVBTRUQiLCJNb2Rlcm5TaWRlYmFyIiwidXNlciIsInNpZ25PdXQiLCJyb3V0ZXIiLCJwYXRobmFtZSIsInRoZW1lIiwiaXNNb2JpbGUiLCJicmVha3BvaW50cyIsImRvd24iLCJpc0NvbGxhcHNlZCIsInNldElzQ29sbGFwc2VkIiwibW9iaWxlT3BlbiIsInNldE1vYmlsZU9wZW4iLCJjb2xvcnMiLCJzaWRlYmFyIiwic2lkZWJhckhvdmVyIiwiYWNjZW50IiwiYWNjZW50SG92ZXIiLCJ0ZXh0IiwidGV4dFNlY29uZGFyeSIsInRleHRNdXRlZCIsImJvcmRlciIsInN1Y2Nlc3MiLCJ3YXJuaW5nIiwiZXJyb3IiLCJuYXZpZ2F0aW9uSXRlbXMiLCJsYWJlbCIsImljb24iLCJwYXRoIiwiZGVzY3JpcHRpb24iLCJoYW5kbGVOYXZpZ2F0aW9uIiwicHVzaCIsImhhbmRsZVNpZ25PdXQiLCJ0b2dnbGVTaWRlYmFyIiwiaXNBY3RpdmUiLCJzaWRlYmFyV2lkdGgiLCJ2YXJpYW50Iiwic3giLCJ3aWR0aCIsImZsZXhTaHJpbmsiLCJib3hTaXppbmciLCJiYWNrZ3JvdW5kQ29sb3IiLCJib3JkZXJSaWdodCIsInRyYW5zaXRpb24iLCJ0cmFuc2l0aW9ucyIsImNyZWF0ZSIsImVhc2luZyIsInNoYXJwIiwiZHVyYXRpb24iLCJlbnRlcmluZ1NjcmVlbiIsIm92ZXJmbG93WCIsImJveFNoYWRvdyIsInAiLCJkaXNwbGF5IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwiYm9yZGVyQm90dG9tIiwibWluSGVpZ2h0IiwiZm9udFNpemUiLCJjb2xvciIsIm1yIiwiZmlsdGVyIiwiZm9udFdlaWdodCIsImxldHRlclNwYWNpbmciLCJvbkNsaWNrIiwiaGVpZ2h0IiwicHgiLCJweSIsInNpemUiLCJmbGV4IiwibWFwIiwiaXRlbSIsIkljb24iLCJhY3RpdmUiLCJkaXNhYmxlUGFkZGluZyIsIm1iIiwidGl0bGUiLCJwbGFjZW1lbnQiLCJhcnJvdyIsImJvcmRlclJhZGl1cyIsIm14IiwidHJhbnNmb3JtIiwibWluV2lkdGgiLCJwcmltYXJ5Iiwic2Vjb25kYXJ5IiwicHJpbWFyeVR5cG9ncmFwaHlQcm9wcyIsInNlY29uZGFyeVR5cG9ncmFwaHlQcm9wcyIsIm10IiwicGIiLCJib3JkZXJDb2xvciIsImJvcmRlclRvcCIsInB0Iiwic3JjIiwiaW1hZ2VVcmwiLCJhbHQiLCJmdWxsTmFtZSIsImZpcnN0TmFtZSIsImNoYXJBdCIsImxpbmVIZWlnaHQiLCJvdmVyZmxvdyIsInRleHRPdmVyZmxvdyIsIndoaXRlU3BhY2UiLCJsYXN0TmFtZSIsInByaW1hcnlFbWFpbEFkZHJlc3MiLCJlbWFpbEFkZHJlc3MiLCJmbGV4RGlyZWN0aW9uIiwiZ2FwIiwiY3Vyc29yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModernSidebar.js\n"));

/***/ })

});