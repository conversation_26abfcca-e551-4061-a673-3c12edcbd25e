"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/utils/index.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n// BahinLink Admin Dashboard\n// ⚠️ CRITICAL: Real-time monitoring with production data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Dynamically import RealTimeMap to avoid SSR issues\nconst RealTimeMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_RealTimeMap_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RealTimeMap */ \"(app-pages-browser)/./src/components/RealTimeMap.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.js -> \" + \"../components/RealTimeMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sx: {\n                height: 400,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n});\n_c = RealTimeMap;\nfunction Dashboard() {\n    var _dashboardData_currentShifts;\n    _s();\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadDashboardData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadDashboardData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            setError(null);\n            // Get real dashboard analytics\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/dashboard\");\n            if (response.success) {\n                setDashboardData(response.data);\n            } else {\n                var _response_error;\n                throw new _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"](((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || \"Failed to load dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Dashboard data error:\", error);\n            setError(error.message || \"Failed to load dashboard data\");\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadDashboardData();\n    };\n    if (!isLoaded || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading BahinLink Dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    color: \"inherit\",\n                    size: \"small\",\n                    onClick: handleRefresh,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, void 0),\n                children: error\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const { activeAgents = 0, totalAgents = 0, activeShifts = 0, pendingReports = 0, geofenceViolations = 0, clientSatisfaction = 0, recentActivity = [], agentLocations = [], shiftStats = [], alerts = [] } = dashboardData || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8fafc\",\n                    minHeight: \"100vh\",\n                    transition: \"margin-left 0.3s ease-in-out\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        p: {\n                            xs: 2,\n                            sm: 3,\n                            md: 4\n                        },\n                        maxWidth: \"1400px\",\n                        mx: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 4,\n                            sx: {\n                                backgroundColor: \"white\",\n                                borderRadius: 3,\n                                p: 3,\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                border: \"1px solid rgba(0, 0, 0, 0.05)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h4\",\n                                            component: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#1e293b\",\n                                                fontSize: {\n                                                    xs: \"1.75rem\",\n                                                    md: \"2.125rem\"\n                                                },\n                                                letterSpacing: \"-0.025em\",\n                                                mb: 0.5\n                                            },\n                                            children: \"Dashboard Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                fontSize: \"1rem\"\n                                            },\n                                            children: \"Real-time monitoring for Bahin SARL security operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                display: {\n                                                    xs: \"none\",\n                                                    sm: \"block\"\n                                                }\n                                            },\n                                            children: [\n                                                \"Welcome back, \",\n                                                user === null || user === void 0 ? void 0 : user.firstName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: handleRefresh,\n                                            disabled: refreshing,\n                                            sx: {\n                                                borderColor: \"#e2e8f0\",\n                                                color: \"#475569\",\n                                                \"&:hover\": {\n                                                    borderColor: \"#cbd5e1\",\n                                                    backgroundColor: \"#f8fafc\"\n                                                },\n                                                borderRadius: 2,\n                                                textTransform: \"none\",\n                                                fontWeight: 500\n                                            },\n                                            children: refreshing ? \"Refreshing...\" : \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    activeAgents,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: [\n                                                                            \"/\",\n                                                                            totalAgents\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dbeafe\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#3b82f6\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Shifts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: activeShifts\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Pending Reports\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: pendingReports\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#fef3c7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#d97706\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Client Satisfaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    clientSatisfaction.toFixed(1),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: \"/5.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 241,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                gap: 2,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/agents\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            backgroundColor: \"#6366f1\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            boxShadow: \"0 1px 3px rgba(99, 102, 241, 0.3)\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"#5855eb\",\n                                                                transform: \"translateY(-1px)\",\n                                                                boxShadow: \"0 4px 12px rgba(99, 102, 241, 0.4)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Manage Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/sites\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Sites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/shifts\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Schedule Shifts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/reports\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Reports\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 486,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 485,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 484,\n                            columnNumber: 7\n                        }, this),\n                        alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this),\n                                            alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    severity: alert.severity,\n                                                    sx: {\n                                                        mb: 2,\n                                                        borderRadius: 2,\n                                                        border: \"1px solid\",\n                                                        borderColor: alert.severity === \"warning\" ? \"#fbbf24\" : \"#3b82f6\",\n                                                        backgroundColor: alert.severity === \"warning\" ? \"#fef3c7\" : \"#dbeafe\",\n                                                        \"& .MuiAlert-icon\": {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\"\n                                                        },\n                                                        \"& .MuiAlert-message\": {\n                                                            color: \"#1e293b\",\n                                                            fontWeight: 500\n                                                        }\n                                                    },\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: \"small\",\n                                                        sx: {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\",\n                                                            fontWeight: 500,\n                                                            textTransform: \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(0, 0, 0, 0.04)\"\n                                                            }\n                                                        },\n                                                        children: \"VIEW\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    children: alert.message\n                                                }, index, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 603,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Real-time Agent Locations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 400,\n                                                    sx: {\n                                                        borderRadius: 2,\n                                                        overflow: \"hidden\",\n                                                        border: \"1px solid #e2e8f0\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeMap, {\n                                                        agentLocations: agentLocations\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 679,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 671,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 670,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Recent Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    dense: true,\n                                                    sx: {\n                                                        \"& .MuiListItem-root\": {\n                                                            px: 0\n                                                        }\n                                                    },\n                                                    children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            sx: {\n                                                                borderRadius: 2,\n                                                                mb: 1,\n                                                                backgroundColor: \"#f8fafc\",\n                                                                border: \"1px solid #e2e8f0\",\n                                                                \"&:hover\": {\n                                                                    backgroundColor: \"#f1f5f9\"\n                                                                },\n                                                                transition: \"background-color 0.2s ease-in-out\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        minWidth: 40\n                                                                    },\n                                                                    children: [\n                                                                        activity.type === \"clock_in\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#16a34a\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"clock_out\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"report_submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#6366f1\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"geofence_violation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"location_update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    primary: activity.message,\n                                                                    secondary: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(activity.timestamp),\n                                                                    primaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.875rem\",\n                                                                            fontWeight: 500,\n                                                                            color: \"#1e293b\",\n                                                                            lineHeight: 1.4\n                                                                        }\n                                                                    },\n                                                                    secondaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.75rem\",\n                                                                            color: \"#64748b\",\n                                                                            mt: 0.5\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 714,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 706,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 669,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Shift Performance (Last 7 Days)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 300,\n                                                    sx: {\n                                                        \"& .recharts-cartesian-grid-horizontal line\": {\n                                                            stroke: \"#e2e8f0\"\n                                                        },\n                                                        \"& .recharts-cartesian-grid-vertical line\": {\n                                                            stroke: \"#e2e8f0\"\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.LineChart, {\n                                                            data: shiftStats,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.XAxis, {\n                                                                    dataKey: \"date\",\n                                                                    tick: {\n                                                                        fontSize: 12,\n                                                                        fill: \"#64748b\"\n                                                                    },\n                                                                    axisLine: {\n                                                                        stroke: \"#e2e8f0\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.YAxis, {\n                                                                    tick: {\n                                                                        fontSize: 12,\n                                                                        fill: \"#64748b\"\n                                                                    },\n                                                                    axisLine: {\n                                                                        stroke: \"#e2e8f0\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Tooltip, {\n                                                                    contentStyle: {\n                                                                        backgroundColor: \"white\",\n                                                                        border: \"1px solid #e2e8f0\",\n                                                                        borderRadius: \"8px\",\n                                                                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"completedShifts\",\n                                                                    stroke: \"#6366f1\",\n                                                                    strokeWidth: 3,\n                                                                    name: \"Completed Shifts\",\n                                                                    dot: {\n                                                                        fill: \"#6366f1\",\n                                                                        strokeWidth: 2,\n                                                                        r: 4\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"onTimePercentage\",\n                                                                    stroke: \"#16a34a\",\n                                                                    strokeWidth: 3,\n                                                                    name: \"On-time %\",\n                                                                    dot: {\n                                                                        fill: \"#16a34a\",\n                                                                        strokeWidth: 2,\n                                                                        r: 4\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 796,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 787,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Current Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    sx: {\n                                                        borderRadius: 2,\n                                                        border: \"1px solid #e2e8f0\",\n                                                        \"& .MuiTable-root\": {\n                                                            minWidth: \"auto\"\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                        size: \"small\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                    sx: {\n                                                                        backgroundColor: \"#f8fafc\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Agent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Site\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 925,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_currentShifts = dashboardData.currentShifts) === null || _dashboardData_currentShifts === void 0 ? void 0 : _dashboardData_currentShifts.map((shift)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        sx: {\n                                                                            \"&:hover\": {\n                                                                                backgroundColor: \"#f8fafc\"\n                                                                            },\n                                                                            \"&:last-child td\": {\n                                                                                borderBottom: \"none\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#1e293b\",\n                                                                                    fontWeight: 500,\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: shift.agentName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 950,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#64748b\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: shift.siteName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                                    label: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.enumToDisplayString)(shift.status),\n                                                                                    size: \"small\",\n                                                                                    sx: {\n                                                                                        backgroundColor: shift.status === \"IN_PROGRESS\" ? \"#dcfce7\" : \"#f1f5f9\",\n                                                                                        color: shift.status === \"IN_PROGRESS\" ? \"#16a34a\" : \"#64748b\",\n                                                                                        fontWeight: 500,\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        height: 24,\n                                                                                        \"& .MuiChip-label\": {\n                                                                                            px: 1.5\n                                                                                        }\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                    lineNumber: 970,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#64748b\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: [\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.startTime),\n                                                                                    \" - \",\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.endTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, shift.id, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 939,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 871,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 863,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 862,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 786,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"3hh1eXmDMCnbUTDNuIvrvg4KgVg=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser\n    ];\n});\n_c1 = Dashboard;\nvar _c, _c1;\n$RefreshReg$(_c, \"RealTimeMap\");\n$RefreshReg$(_c1, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});