"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/utils/index.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n// BahinLink Admin Dashboard\n// ⚠️ CRITICAL: Real-time monitoring with production data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Dynamically import RealTimeMap to avoid SSR issues\nconst RealTimeMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_RealTimeMap_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RealTimeMap */ \"(app-pages-browser)/./src/components/RealTimeMap.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.js -> \" + \"../components/RealTimeMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sx: {\n                height: 400,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n});\n_c = RealTimeMap;\nfunction Dashboard() {\n    var _dashboardData_currentShifts;\n    _s();\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadDashboardData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadDashboardData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            setError(null);\n            // Get real dashboard analytics\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/dashboard\");\n            if (response.success) {\n                setDashboardData(response.data);\n            } else {\n                var _response_error;\n                throw new _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"](((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || \"Failed to load dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Dashboard data error:\", error);\n            setError(error.message || \"Failed to load dashboard data\");\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadDashboardData();\n    };\n    if (!isLoaded || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading BahinLink Dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    color: \"inherit\",\n                    size: \"small\",\n                    onClick: handleRefresh,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, void 0),\n                children: error\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const { activeAgents = 0, totalAgents = 0, activeShifts = 0, pendingReports = 0, geofenceViolations = 0, clientSatisfaction = 0, recentActivity = [], agentLocations = [], shiftStats = [], alerts = [] } = dashboardData || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8fafc\",\n                    minHeight: \"100vh\",\n                    transition: \"margin-left 0.3s ease-in-out\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        p: {\n                            xs: 2,\n                            sm: 3,\n                            md: 4\n                        },\n                        maxWidth: \"1400px\",\n                        mx: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 4,\n                            sx: {\n                                backgroundColor: \"white\",\n                                borderRadius: 3,\n                                p: 3,\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                border: \"1px solid rgba(0, 0, 0, 0.05)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h4\",\n                                            component: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#1e293b\",\n                                                fontSize: {\n                                                    xs: \"1.75rem\",\n                                                    md: \"2.125rem\"\n                                                },\n                                                letterSpacing: \"-0.025em\",\n                                                mb: 0.5\n                                            },\n                                            children: \"Dashboard Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                fontSize: \"1rem\"\n                                            },\n                                            children: \"Real-time monitoring for Bahin SARL security operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                display: {\n                                                    xs: \"none\",\n                                                    sm: \"block\"\n                                                }\n                                            },\n                                            children: [\n                                                \"Welcome back, \",\n                                                user === null || user === void 0 ? void 0 : user.firstName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: handleRefresh,\n                                            disabled: refreshing,\n                                            sx: {\n                                                borderColor: \"#e2e8f0\",\n                                                color: \"#475569\",\n                                                \"&:hover\": {\n                                                    borderColor: \"#cbd5e1\",\n                                                    backgroundColor: \"#f8fafc\"\n                                                },\n                                                borderRadius: 2,\n                                                textTransform: \"none\",\n                                                fontWeight: 500\n                                            },\n                                            children: refreshing ? \"Refreshing...\" : \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    activeAgents,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: [\n                                                                            \"/\",\n                                                                            totalAgents\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dbeafe\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#3b82f6\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Shifts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: activeShifts\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Pending Reports\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: pendingReports\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#fef3c7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#d97706\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Client Satisfaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    clientSatisfaction.toFixed(1),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: \"/5.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 241,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 488,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                gap: 2,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/agents\",\n                                                        sx: {\n                                                            textTransform: \"none\"\n                                                        },\n                                                        children: \"Manage Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 492,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/sites\",\n                                                        sx: {\n                                                            textTransform: \"none\"\n                                                        },\n                                                        children: \"View Sites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 500,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/shifts\",\n                                                        sx: {\n                                                            textTransform: \"none\"\n                                                        },\n                                                        children: \"Schedule Shifts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/reports\",\n                                                        sx: {\n                                                            textTransform: \"none\"\n                                                        },\n                                                        children: \"View Reports\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 487,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 486,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 485,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 484,\n                            columnNumber: 7\n                        }, this),\n                        alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 3,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                gutterBottom: true,\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 536,\n                                                columnNumber: 17\n                                            }, this),\n                                            alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    severity: alert.severity,\n                                                    sx: {\n                                                        mb: 1\n                                                    },\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        color: \"inherit\",\n                                                        size: \"small\",\n                                                        children: \"View\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 545,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    children: alert.message\n                                                }, index, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 535,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 533,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 532,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Real-time Agent Locations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 400,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeMap, {\n                                                        agentLocations: agentLocations\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 563,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 562,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 561,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Recent Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    dense: true,\n                                                    children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    children: [\n                                                                        activity.type === \"clock_in\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            color: \"success\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 584,\n                                                                            columnNumber: 56\n                                                                        }, this),\n                                                                        activity.type === \"clock_out\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            color: \"primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 585,\n                                                                            columnNumber: 57\n                                                                        }, this),\n                                                                        activity.type === \"report_submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            color: \"info\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 586,\n                                                                            columnNumber: 64\n                                                                        }, this),\n                                                                        activity.type === \"geofence_violation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            color: \"error\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 587,\n                                                                            columnNumber: 66\n                                                                        }, this),\n                                                                        activity.type === \"location_update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            color: \"primary\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 588,\n                                                                            columnNumber: 63\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    primary: activity.message,\n                                                                    secondary: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(activity.timestamp)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 576,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 575,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 574,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 560,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Shift Performance (Last 7 Days)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 300,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.LineChart, {\n                                                            data: shiftStats,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 613,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.XAxis, {\n                                                                    dataKey: \"date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 614,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.YAxis, {}, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 615,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Tooltip, {}, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 616,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"completedShifts\",\n                                                                    stroke: \"#2196f3\",\n                                                                    strokeWidth: 2,\n                                                                    name: \"Completed Shifts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 617,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"onTimePercentage\",\n                                                                    stroke: \"#4caf50\",\n                                                                    strokeWidth: 2,\n                                                                    name: \"On-time %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 612,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 610,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 606,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 605,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 604,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Current Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n                                                    variant: \"outlined\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                        size: \"small\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Agent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 648,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Site\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 649,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 650,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 651,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 647,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_currentShifts = dashboardData.currentShifts) === null || _dashboardData_currentShifts === void 0 ? void 0 : _dashboardData_currentShifts.map((shift)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: shift.agentName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 657,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: shift.siteName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 658,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                                                                    label: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.enumToDisplayString)(shift.status),\n                                                                                    color: shift.status === \"IN_PROGRESS\" ? \"success\" : \"default\",\n                                                                                    size: \"small\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                    lineNumber: 660,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 659,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: [\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.startTime),\n                                                                                    \" - \",\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.endTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, shift.id, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 656,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 640,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 639,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 638,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 603,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"3hh1eXmDMCnbUTDNuIvrvg4KgVg=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser\n    ];\n});\n_c1 = Dashboard;\nvar _c, _c1;\n$RefreshReg$(_c, \"RealTimeMap\");\n$RefreshReg$(_c1, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});