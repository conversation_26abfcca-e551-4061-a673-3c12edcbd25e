"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/utils/index.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n// BahinLink Admin Dashboard\n// ⚠️ CRITICAL: Real-time monitoring with production data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Dynamically import RealTimeMap to avoid SSR issues\nconst RealTimeMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_RealTimeMap_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RealTimeMap */ \"(app-pages-browser)/./src/components/RealTimeMap.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.js -> \" + \"../components/RealTimeMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sx: {\n                height: 400,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n});\n_c = RealTimeMap;\nfunction Dashboard() {\n    var _dashboardData_currentShifts;\n    _s();\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadDashboardData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadDashboardData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            setError(null);\n            // Get real dashboard analytics\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/dashboard\");\n            if (response.success) {\n                setDashboardData(response.data);\n            } else {\n                var _response_error;\n                throw new _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"](((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || \"Failed to load dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Dashboard data error:\", error);\n            setError(error.message || \"Failed to load dashboard data\");\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadDashboardData();\n    };\n    if (!isLoaded || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading BahinLink Dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    color: \"inherit\",\n                    size: \"small\",\n                    onClick: handleRefresh,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, void 0),\n                children: error\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const { activeAgents = 0, totalAgents = 0, activeShifts = 0, pendingReports = 0, geofenceViolations = 0, clientSatisfaction = 0, recentActivity = [], agentLocations = [], shiftStats = [], alerts = [] } = dashboardData || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8fafc\",\n                    minHeight: \"100vh\",\n                    transition: \"margin-left 0.3s ease-in-out\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        p: {\n                            xs: 2,\n                            sm: 3,\n                            md: 4\n                        },\n                        maxWidth: \"1400px\",\n                        mx: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 4,\n                            sx: {\n                                backgroundColor: \"white\",\n                                borderRadius: 3,\n                                p: 3,\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                border: \"1px solid rgba(0, 0, 0, 0.05)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h4\",\n                                            component: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#1e293b\",\n                                                fontSize: {\n                                                    xs: \"1.75rem\",\n                                                    md: \"2.125rem\"\n                                                },\n                                                letterSpacing: \"-0.025em\",\n                                                mb: 0.5\n                                            },\n                                            children: \"Dashboard Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                fontSize: \"1rem\"\n                                            },\n                                            children: \"Real-time monitoring for Bahin SARL security operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                display: {\n                                                    xs: \"none\",\n                                                    sm: \"block\"\n                                                }\n                                            },\n                                            children: [\n                                                \"Welcome back, \",\n                                                user === null || user === void 0 ? void 0 : user.firstName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: handleRefresh,\n                                            disabled: refreshing,\n                                            sx: {\n                                                borderColor: \"#e2e8f0\",\n                                                color: \"#475569\",\n                                                \"&:hover\": {\n                                                    borderColor: \"#cbd5e1\",\n                                                    backgroundColor: \"#f8fafc\"\n                                                },\n                                                borderRadius: 2,\n                                                textTransform: \"none\",\n                                                fontWeight: 500\n                                            },\n                                            children: refreshing ? \"Refreshing...\" : \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    activeAgents,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: [\n                                                                            \"/\",\n                                                                            totalAgents\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dbeafe\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#3b82f6\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Shifts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: activeShifts\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Pending Reports\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: pendingReports\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#fef3c7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#d97706\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Client Satisfaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    clientSatisfaction.toFixed(1),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: \"/5.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 241,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                gap: 2,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/agents\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            backgroundColor: \"#6366f1\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            boxShadow: \"0 1px 3px rgba(99, 102, 241, 0.3)\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"#5855eb\",\n                                                                transform: \"translateY(-1px)\",\n                                                                boxShadow: \"0 4px 12px rgba(99, 102, 241, 0.4)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Manage Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/sites\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Sites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/shifts\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Schedule Shifts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/reports\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Reports\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 486,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 485,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 484,\n                            columnNumber: 7\n                        }, this),\n                        alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this),\n                                            alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    severity: alert.severity,\n                                                    sx: {\n                                                        mb: 2,\n                                                        borderRadius: 2,\n                                                        border: \"1px solid\",\n                                                        borderColor: alert.severity === \"warning\" ? \"#fbbf24\" : \"#3b82f6\",\n                                                        backgroundColor: alert.severity === \"warning\" ? \"#fef3c7\" : \"#dbeafe\",\n                                                        \"& .MuiAlert-icon\": {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\"\n                                                        },\n                                                        \"& .MuiAlert-message\": {\n                                                            color: \"#1e293b\",\n                                                            fontWeight: 500\n                                                        }\n                                                    },\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: \"small\",\n                                                        sx: {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\",\n                                                            fontWeight: 500,\n                                                            textTransform: \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(0, 0, 0, 0.04)\"\n                                                            }\n                                                        },\n                                                        children: \"VIEW\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    children: alert.message\n                                                }, index, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 603,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Real-time Agent Locations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 400,\n                                                    sx: {\n                                                        borderRadius: 2,\n                                                        overflow: \"hidden\",\n                                                        border: \"1px solid #e2e8f0\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeMap, {\n                                                        agentLocations: agentLocations\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 679,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 671,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 670,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Recent Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    dense: true,\n                                                    sx: {\n                                                        \"& .MuiListItem-root\": {\n                                                            px: 0\n                                                        }\n                                                    },\n                                                    children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            sx: {\n                                                                borderRadius: 2,\n                                                                mb: 1,\n                                                                backgroundColor: \"#f8fafc\",\n                                                                border: \"1px solid #e2e8f0\",\n                                                                \"&:hover\": {\n                                                                    backgroundColor: \"#f1f5f9\"\n                                                                },\n                                                                transition: \"background-color 0.2s ease-in-out\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        minWidth: 40\n                                                                    },\n                                                                    children: [\n                                                                        activity.type === \"clock_in\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#16a34a\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"clock_out\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"report_submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#6366f1\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"geofence_violation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"location_update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    primary: activity.message,\n                                                                    secondary: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(activity.timestamp),\n                                                                    primaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.875rem\",\n                                                                            fontWeight: 500,\n                                                                            color: \"#1e293b\",\n                                                                            lineHeight: 1.4\n                                                                        }\n                                                                    },\n                                                                    secondaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.75rem\",\n                                                                            color: \"#64748b\",\n                                                                            mt: 0.5\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 714,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 706,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 669,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Shift Performance (Last 7 Days)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 790,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 300,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.LineChart, {\n                                                            data: shiftStats,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.XAxis, {\n                                                                    dataKey: \"date\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 797,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.YAxis, {}, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 798,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Tooltip, {}, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"completedShifts\",\n                                                                    stroke: \"#2196f3\",\n                                                                    strokeWidth: 2,\n                                                                    name: \"Completed Shifts\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"onTimePercentage\",\n                                                                    stroke: \"#4caf50\",\n                                                                    strokeWidth: 2,\n                                                                    name: \"On-time %\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 807,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 795,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 794,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 793,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 789,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 787,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: \"Current Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    component: _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"],\n                                                    variant: \"outlined\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                        size: \"small\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Agent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 831,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Site\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 832,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 833,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                            children: \"Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_currentShifts = dashboardData.currentShifts) === null || _dashboardData_currentShifts === void 0 ? void 0 : _dashboardData_currentShifts.map((shift)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: shift.agentName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 840,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: shift.siteName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 841,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                                                                    label: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.enumToDisplayString)(shift.status),\n                                                                                    color: shift.status === \"IN_PROGRESS\" ? \"success\" : \"default\",\n                                                                                    size: \"small\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                    lineNumber: 843,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 842,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                                children: [\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.startTime),\n                                                                                    \" - \",\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.endTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 849,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, shift.id, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 839,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 837,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 823,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 822,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 821,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 786,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"3hh1eXmDMCnbUTDNuIvrvg4KgVg=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser\n    ];\n});\n_c1 = Dashboard;\nvar _c, _c1;\n$RefreshReg$(_c, \"RealTimeMap\");\n$RefreshReg$(_c1, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});