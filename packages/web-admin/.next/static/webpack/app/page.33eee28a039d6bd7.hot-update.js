"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Dashboard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Grid,List,ListItem,ListItemIcon,ListItemText,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Error.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,CheckCircle,Error,LocationOn,People,Refresh,Schedule,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Warning.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=CartesianGrid,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils */ \"(app-pages-browser)/./src/utils/index.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n// BahinLink Admin Dashboard\n// ⚠️ CRITICAL: Real-time monitoring with production data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Dynamically import RealTimeMap to avoid SSR issues\nconst RealTimeMap = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_RealTimeMap_js\").then(__webpack_require__.bind(__webpack_require__, /*! ../components/RealTimeMap */ \"(app-pages-browser)/./src/components/RealTimeMap.js\")), {\n    loadableGenerated: {\n        modules: [\n            \"app/page.js -> \" + \"../components/RealTimeMap\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            sx: {\n                height: 400,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 52,\n            columnNumber: 5\n        }, undefined)\n});\n_c = RealTimeMap;\nfunction Dashboard() {\n    var _dashboardData_currentShifts;\n    _s();\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dashboardData, setDashboardData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadDashboardData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadDashboardData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn\n    ]);\n    const loadDashboardData = async ()=>{\n        try {\n            setError(null);\n            // Get real dashboard analytics\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/dashboard\");\n            if (response.success) {\n                setDashboardData(response.data);\n            } else {\n                var _response_error;\n                throw new _barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"](((_response_error = response.error) === null || _response_error === void 0 ? void 0 : _response_error.message) || \"Failed to load dashboard data\");\n            }\n        } catch (error) {\n            console.error(\"Dashboard data error:\", error);\n            setError(error.message || \"Failed to load dashboard data\");\n        } finally{\n            setLoading(false);\n            setRefreshing(false);\n        }\n    };\n    const handleRefresh = async ()=>{\n        setRefreshing(true);\n        await loadDashboardData();\n    };\n    if (!isLoaded || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            minHeight: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    variant: \"h6\",\n                    sx: {\n                        ml: 2\n                    },\n                    children: \"Loading BahinLink Dashboard...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            p: 3,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                severity: \"error\",\n                action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    color: \"inherit\",\n                    size: \"small\",\n                    onClick: handleRefresh,\n                    children: \"Retry\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, void 0),\n                children: error\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, this);\n    }\n    const { activeAgents = 0, totalAgents = 0, activeShifts = 0, pendingReports = 0, geofenceViolations = 0, clientSatisfaction = 0, recentActivity = [], agentLocations = [], shiftStats = [], alerts = [] } = dashboardData || {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    backgroundColor: \"#f8fafc\",\n                    minHeight: \"100vh\",\n                    transition: \"margin-left 0.3s ease-in-out\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        p: {\n                            xs: 2,\n                            sm: 3,\n                            md: 4\n                        },\n                        maxWidth: \"1400px\",\n                        mx: \"auto\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\",\n                            mb: 4,\n                            sx: {\n                                backgroundColor: \"white\",\n                                borderRadius: 3,\n                                p: 3,\n                                boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                border: \"1px solid rgba(0, 0, 0, 0.05)\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"h4\",\n                                            component: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#1e293b\",\n                                                fontSize: {\n                                                    xs: \"1.75rem\",\n                                                    md: \"2.125rem\"\n                                                },\n                                                letterSpacing: \"-0.025em\",\n                                                mb: 0.5\n                                            },\n                                            children: \"Dashboard Overview\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                fontSize: \"1rem\"\n                                            },\n                                            children: \"Real-time monitoring for Bahin SARL security operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: 2,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: \"#64748b\",\n                                                display: {\n                                                    xs: \"none\",\n                                                    sm: \"block\"\n                                                }\n                                            },\n                                            children: [\n                                                \"Welcome back, \",\n                                                user === null || user === void 0 ? void 0 : user.firstName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            variant: \"outlined\",\n                                            startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 220,\n                                                columnNumber: 28\n                                            }, void 0),\n                                            onClick: handleRefresh,\n                                            disabled: refreshing,\n                                            sx: {\n                                                borderColor: \"#e2e8f0\",\n                                                color: \"#475569\",\n                                                \"&:hover\": {\n                                                    borderColor: \"#cbd5e1\",\n                                                    backgroundColor: \"#f8fafc\"\n                                                },\n                                                borderRadius: 2,\n                                                textTransform: \"none\",\n                                                fontWeight: 500\n                                            },\n                                            children: refreshing ? \"Refreshing...\" : \"Refresh\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    activeAgents,\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: [\n                                                                            \"/\",\n                                                                            totalAgents\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 278,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dbeafe\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#3b82f6\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 255,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Active Shifts\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: activeShifts\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 321,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 320,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Pending Reports\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: pendingReports\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#fef3c7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#d97706\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 410,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 363,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            transition: \"all 0.2s ease-in-out\",\n                                            \"&:hover\": {\n                                                transform: \"translateY(-2px)\",\n                                                boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                            }\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                alignItems: \"center\",\n                                                justifyContent: \"space-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                sx: {\n                                                                    color: \"#64748b\",\n                                                                    fontSize: \"0.875rem\",\n                                                                    fontWeight: 500,\n                                                                    mb: 1\n                                                                },\n                                                                children: \"Client Satisfaction\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                variant: \"h3\",\n                                                                sx: {\n                                                                    fontWeight: 700,\n                                                                    color: \"#1e293b\",\n                                                                    fontSize: \"2rem\",\n                                                                    lineHeight: 1.2\n                                                                },\n                                                                children: [\n                                                                    clientSatisfaction.toFixed(1),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        component: \"span\",\n                                                                        sx: {\n                                                                            color: \"#94a3b8\",\n                                                                            fontSize: \"1.25rem\",\n                                                                            fontWeight: 500\n                                                                        },\n                                                                        children: \"/5.0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        sx: {\n                                                            backgroundColor: \"#dcfce7\",\n                                                            borderRadius: 2,\n                                                            p: 1.5,\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            justifyContent: \"center\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Star, {\n                                                            sx: {\n                                                                fontSize: 28,\n                                                                color: \"#16a34a\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 475,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 431,\n                                                columnNumber: 15\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 430,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 418,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 417,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 241,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Quick Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                display: \"flex\",\n                                                gap: 2,\n                                                flexWrap: \"wrap\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"contained\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 509,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/agents\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            backgroundColor: \"#6366f1\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            boxShadow: \"0 1px 3px rgba(99, 102, 241, 0.3)\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"#5855eb\",\n                                                                transform: \"translateY(-1px)\",\n                                                                boxShadow: \"0 4px 12px rgba(99, 102, 241, 0.4)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Manage Agents\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/sites\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Sites\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/shifts\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"Schedule Shifts\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 30\n                                                        }, void 0),\n                                                        onClick: ()=>window.location.href = \"/reports\",\n                                                        sx: {\n                                                            textTransform: \"none\",\n                                                            borderColor: \"#e2e8f0\",\n                                                            color: \"#475569\",\n                                                            borderRadius: 2,\n                                                            px: 3,\n                                                            py: 1.5,\n                                                            fontWeight: 500,\n                                                            \"&:hover\": {\n                                                                borderColor: \"#cbd5e1\",\n                                                                backgroundColor: \"#f8fafc\",\n                                                                transform: \"translateY(-1px)\"\n                                                            },\n                                                            transition: \"all 0.2s ease-in-out\"\n                                                        },\n                                                        children: \"View Reports\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 506,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 486,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 485,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 484,\n                            columnNumber: 7\n                        }, this),\n                        alerts.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    sx: {\n                                        borderRadius: 3,\n                                        border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                        boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                        backgroundColor: \"white\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        sx: {\n                                            p: 3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                variant: \"h6\",\n                                                sx: {\n                                                    fontWeight: 600,\n                                                    color: \"#1e293b\",\n                                                    fontSize: \"1.125rem\",\n                                                    mb: 3\n                                                },\n                                                children: \"Active Alerts\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                lineNumber: 614,\n                                                columnNumber: 17\n                                            }, this),\n                                            alerts.map((alert, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    severity: alert.severity,\n                                                    sx: {\n                                                        mb: 2,\n                                                        borderRadius: 2,\n                                                        border: \"1px solid\",\n                                                        borderColor: alert.severity === \"warning\" ? \"#fbbf24\" : \"#3b82f6\",\n                                                        backgroundColor: alert.severity === \"warning\" ? \"#fef3c7\" : \"#dbeafe\",\n                                                        \"& .MuiAlert-icon\": {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\"\n                                                        },\n                                                        \"& .MuiAlert-message\": {\n                                                            color: \"#1e293b\",\n                                                            fontWeight: 500\n                                                        }\n                                                    },\n                                                    action: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        size: \"small\",\n                                                        sx: {\n                                                            color: alert.severity === \"warning\" ? \"#d97706\" : \"#3b82f6\",\n                                                            fontWeight: 500,\n                                                            textTransform: \"none\",\n                                                            \"&:hover\": {\n                                                                backgroundColor: \"rgba(0, 0, 0, 0.04)\"\n                                                            }\n                                                        },\n                                                        children: \"VIEW\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 644,\n                                                        columnNumber: 23\n                                                    }, void 0),\n                                                    children: alert.message\n                                                }, index, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 613,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                lineNumber: 604,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 603,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            mb: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 8,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Real-time Agent Locations\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 400,\n                                                    sx: {\n                                                        borderRadius: 2,\n                                                        overflow: \"hidden\",\n                                                        border: \"1px solid #e2e8f0\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RealTimeMap, {\n                                                        agentLocations: agentLocations\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 691,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 679,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 671,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 670,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 4,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Recent Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 715,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    dense: true,\n                                                    sx: {\n                                                        \"& .MuiListItem-root\": {\n                                                            px: 0\n                                                        }\n                                                    },\n                                                    children: recentActivity.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            sx: {\n                                                                borderRadius: 2,\n                                                                mb: 1,\n                                                                backgroundColor: \"#f8fafc\",\n                                                                border: \"1px solid #e2e8f0\",\n                                                                \"&:hover\": {\n                                                                    backgroundColor: \"#f1f5f9\"\n                                                                },\n                                                                transition: \"background-color 0.2s ease-in-out\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    sx: {\n                                                                        minWidth: 40\n                                                                    },\n                                                                    children: [\n                                                                        activity.type === \"clock_in\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#16a34a\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"clock_out\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"report_submitted\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#6366f1\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 749,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"geofence_violation\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#ef4444\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        activity.type === \"location_update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_CheckCircle_Error_LocationOn_People_Refresh_Schedule_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            sx: {\n                                                                                color: \"#3b82f6\",\n                                                                                fontSize: 20\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 755,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    primary: activity.message,\n                                                                    secondary: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(activity.timestamp),\n                                                                    primaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.875rem\",\n                                                                            fontWeight: 500,\n                                                                            color: \"#1e293b\",\n                                                                            lineHeight: 1.4\n                                                                        }\n                                                                    },\n                                                                    secondaryTypographyProps: {\n                                                                        sx: {\n                                                                            fontSize: \"0.75rem\",\n                                                                            color: \"#64748b\",\n                                                                            mt: 0.5\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 758,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 726,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 714,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 706,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 705,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 669,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            container: true,\n                            spacing: 3,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Shift Performance (Last 7 Days)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 797,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    height: 300,\n                                                    sx: {\n                                                        \"& .recharts-cartesian-grid-horizontal line\": {\n                                                            stroke: \"#e2e8f0\"\n                                                        },\n                                                        \"& .recharts-cartesian-grid-vertical line\": {\n                                                            stroke: \"#e2e8f0\"\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.ResponsiveContainer, {\n                                                        width: \"100%\",\n                                                        height: \"100%\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.LineChart, {\n                                                            data: shiftStats,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.CartesianGrid, {\n                                                                    strokeDasharray: \"3 3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 821,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.XAxis, {\n                                                                    dataKey: \"date\",\n                                                                    tick: {\n                                                                        fontSize: 12,\n                                                                        fill: \"#64748b\"\n                                                                    },\n                                                                    axisLine: {\n                                                                        stroke: \"#e2e8f0\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 822,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_32__.YAxis, {\n                                                                    tick: {\n                                                                        fontSize: 12,\n                                                                        fill: \"#64748b\"\n                                                                    },\n                                                                    axisLine: {\n                                                                        stroke: \"#e2e8f0\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 827,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_33__.Tooltip, {\n                                                                    contentStyle: {\n                                                                        backgroundColor: \"white\",\n                                                                        border: \"1px solid #e2e8f0\",\n                                                                        borderRadius: \"8px\",\n                                                                        boxShadow: \"0 4px 12px rgba(0, 0, 0, 0.15)\"\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 831,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"completedShifts\",\n                                                                    stroke: \"#6366f1\",\n                                                                    strokeWidth: 3,\n                                                                    name: \"Completed Shifts\",\n                                                                    dot: {\n                                                                        fill: \"#6366f1\",\n                                                                        strokeWidth: 2,\n                                                                        r: 4\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 839,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CartesianGrid_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_34__.Line, {\n                                                                    type: \"monotone\",\n                                                                    dataKey: \"onTimePercentage\",\n                                                                    stroke: \"#16a34a\",\n                                                                    strokeWidth: 3,\n                                                                    name: \"On-time %\",\n                                                                    dot: {\n                                                                        fill: \"#16a34a\",\n                                                                        strokeWidth: 2,\n                                                                        r: 4\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 847,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 796,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 788,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 787,\n                                    columnNumber: 9\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    md: 6,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        sx: {\n                                            borderRadius: 3,\n                                            border: \"1px solid rgba(0, 0, 0, 0.05)\",\n                                            boxShadow: \"0 1px 3px rgba(0, 0, 0, 0.1)\",\n                                            backgroundColor: \"white\"\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                p: 3\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#1e293b\",\n                                                        fontSize: \"1.125rem\",\n                                                        mb: 3\n                                                    },\n                                                    children: \"Current Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 872,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                                                    sx: {\n                                                        borderRadius: 2,\n                                                        border: \"1px solid #e2e8f0\",\n                                                        \"& .MuiTable-root\": {\n                                                            minWidth: \"auto\"\n                                                        }\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                                        size: \"small\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                    sx: {\n                                                                        backgroundColor: \"#f8fafc\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Agent\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 895,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Site\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 905,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Status\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 915,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                            sx: {\n                                                                                fontWeight: 600,\n                                                                                color: \"#475569\",\n                                                                                fontSize: \"0.875rem\",\n                                                                                borderBottom: \"1px solid #e2e8f0\"\n                                                                            },\n                                                                            children: \"Time\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                            lineNumber: 925,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                    lineNumber: 894,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 893,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                                                                children: dashboardData === null || dashboardData === void 0 ? void 0 : (_dashboardData_currentShifts = dashboardData.currentShifts) === null || _dashboardData_currentShifts === void 0 ? void 0 : _dashboardData_currentShifts.map((shift)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                                                        sx: {\n                                                                            \"&:hover\": {\n                                                                                backgroundColor: \"#f8fafc\"\n                                                                            },\n                                                                            \"&:last-child td\": {\n                                                                                borderBottom: \"none\"\n                                                                            }\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#1e293b\",\n                                                                                    fontWeight: 500,\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: shift.agentName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 950,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#64748b\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: shift.siteName\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 960,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                                                                    label: (0,_utils__WEBPACK_IMPORTED_MODULE_5__.enumToDisplayString)(shift.status),\n                                                                                    size: \"small\",\n                                                                                    sx: {\n                                                                                        backgroundColor: shift.status === \"IN_PROGRESS\" ? \"#dcfce7\" : \"#f1f5f9\",\n                                                                                        color: shift.status === \"IN_PROGRESS\" ? \"#16a34a\" : \"#64748b\",\n                                                                                        fontWeight: 500,\n                                                                                        fontSize: \"0.75rem\",\n                                                                                        height: 24,\n                                                                                        \"& .MuiChip-label\": {\n                                                                                            px: 1.5\n                                                                                        }\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                    lineNumber: 970,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 969,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Grid_List_ListItem_ListItemIcon_ListItemText_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                                                                sx: {\n                                                                                    color: \"#64748b\",\n                                                                                    fontSize: \"0.875rem\",\n                                                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                                                },\n                                                                                children: [\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.startTime),\n                                                                                    \" - \",\n                                                                                    (0,_utils__WEBPACK_IMPORTED_MODULE_5__.formatTime)(shift.endTime)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                                lineNumber: 985,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, shift.id, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                        lineNumber: 939,\n                                                                        columnNumber: 23\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                                lineNumber: 937,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                        lineNumber: 892,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                                    lineNumber: 883,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                            lineNumber: 871,\n                                            columnNumber: 13\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                        lineNumber: 863,\n                                        columnNumber: 11\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                                    lineNumber: 862,\n                                    columnNumber: 9\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                            lineNumber: 786,\n                            columnNumber: 7\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(Dashboard, \"3hh1eXmDMCnbUTDNuIvrvg4KgVg=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useAuth,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_9__.useUser\n    ];\n});\n_c1 = Dashboard;\nvar _c, _c1;\n$RefreshReg$(_c, \"RealTimeMap\");\n$RefreshReg$(_c1, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});