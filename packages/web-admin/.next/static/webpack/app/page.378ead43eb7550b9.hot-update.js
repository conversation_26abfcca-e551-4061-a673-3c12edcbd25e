"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/RealTimeMap.js":
/*!***************************************!*\
  !*** ./src/components/RealTimeMap.js ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/../../node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Chip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Business_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Business,LocationOn,Person!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Business_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Business,LocationOn,Person!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Business.js\");\n// BahinLink Real-time Map Component\n// ⚠️ CRITICAL: Real GPS coordinates and live agent tracking ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Dynamically import map components to avoid SSR issues\nconst MapContainer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-leaflet_lib_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-leaflet */ \"(app-pages-browser)/../../node_modules/react-leaflet/lib/index.js\")).then((mod)=>mod.MapContainer), {\n    loadableGenerated: {\n        modules: [\n            \"components/RealTimeMap.js -> \" + \"react-leaflet\"\n        ]\n    },\n    ssr: false\n});\n_c = MapContainer;\nconst TileLayer = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-leaflet_lib_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-leaflet */ \"(app-pages-browser)/../../node_modules/react-leaflet/lib/index.js\")).then((mod)=>mod.TileLayer), {\n    loadableGenerated: {\n        modules: [\n            \"components/RealTimeMap.js -> \" + \"react-leaflet\"\n        ]\n    },\n    ssr: false\n});\n_c1 = TileLayer;\nconst Marker = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-leaflet_lib_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-leaflet */ \"(app-pages-browser)/../../node_modules/react-leaflet/lib/index.js\")).then((mod)=>mod.Marker), {\n    loadableGenerated: {\n        modules: [\n            \"components/RealTimeMap.js -> \" + \"react-leaflet\"\n        ]\n    },\n    ssr: false\n});\n_c2 = Marker;\nconst Popup = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-leaflet_lib_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-leaflet */ \"(app-pages-browser)/../../node_modules/react-leaflet/lib/index.js\")).then((mod)=>mod.Popup), {\n    loadableGenerated: {\n        modules: [\n            \"components/RealTimeMap.js -> \" + \"react-leaflet\"\n        ]\n    },\n    ssr: false\n});\n_c3 = Popup;\nconst Circle = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-leaflet_lib_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-leaflet */ \"(app-pages-browser)/../../node_modules/react-leaflet/lib/index.js\")).then((mod)=>mod.Circle), {\n    loadableGenerated: {\n        modules: [\n            \"components/RealTimeMap.js -> \" + \"react-leaflet\"\n        ]\n    },\n    ssr: false\n});\n_c4 = Circle;\n// Initialize Leaflet only on client side\nlet L;\nif (true) {\n    L = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/../../node_modules/leaflet/dist/leaflet-src.js\");\n    __webpack_require__(/*! leaflet/dist/leaflet.css */ \"(app-pages-browser)/../../node_modules/leaflet/dist/leaflet.css\");\n    // Fix for default markers in react-leaflet\n    delete L.Icon.Default.prototype._getIconUrl;\n    L.Icon.Default.mergeOptions({\n        iconRetinaUrl: \"/leaflet/marker-icon-2x.png\",\n        iconUrl: \"/leaflet/marker-icon.png\",\n        shadowUrl: \"/leaflet/marker-shadow.png\"\n    });\n}\n// Custom icons for different marker types\nconst agentIcon = new L.Icon({\n    iconUrl: \"/icons/agent-marker.png\",\n    iconRetinaUrl: \"/icons/agent-marker-2x.png\",\n    iconSize: [\n        32,\n        32\n    ],\n    iconAnchor: [\n        16,\n        32\n    ],\n    popupAnchor: [\n        0,\n        -32\n    ],\n    shadowUrl: \"/leaflet/marker-shadow.png\",\n    shadowSize: [\n        41,\n        41\n    ],\n    shadowAnchor: [\n        12,\n        41\n    ]\n});\nconst siteIcon = new L.Icon({\n    iconUrl: \"/icons/site-marker.png\",\n    iconRetinaUrl: \"/icons/site-marker-2x.png\",\n    iconSize: [\n        32,\n        32\n    ],\n    iconAnchor: [\n        16,\n        32\n    ],\n    popupAnchor: [\n        0,\n        -32\n    ],\n    shadowUrl: \"/leaflet/marker-shadow.png\",\n    shadowSize: [\n        41,\n        41\n    ],\n    shadowAnchor: [\n        12,\n        41\n    ]\n});\nconst RealTimeMap = (param)=>{\n    let { agentLocations = [], sites = [], height = 400 } = param;\n    _s();\n    const [mapReady, setMapReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Default center: Dakar, Senegal (real coordinates)\n    const defaultCenter = [\n        14.6937,\n        -17.4441\n    ];\n    const defaultZoom = 12;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Ensure we're in browser environment\n        if (true) {\n            setMapReady(true);\n        }\n    }, []);\n    // Don't render map on server side\n    if (!mapReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            sx: {\n                height,\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"#f5f5f5\",\n                border: \"1px solid #ddd\",\n                borderRadius: 1\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Auto-fit map bounds when agent locations change\n        if (mapRef.current && agentLocations.length > 0) {\n            const map = mapRef.current;\n            const bounds = L.latLngBounds();\n            agentLocations.forEach((agent)=>{\n                if (agent.latitude && agent.longitude) {\n                    bounds.extend([\n                        agent.latitude,\n                        agent.longitude\n                    ]);\n                }\n            });\n            sites.forEach((site)=>{\n                if (site.latitude && site.longitude) {\n                    bounds.extend([\n                        site.latitude,\n                        site.longitude\n                    ]);\n                }\n            });\n            if (bounds.isValid()) {\n                map.fitBounds(bounds, {\n                    padding: [\n                        20,\n                        20\n                    ]\n                });\n            }\n        }\n    }, [\n        agentLocations,\n        sites\n    ]);\n    const getAgentStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"#4caf50\";\n            case \"on_shift\":\n                return \"#2196f3\";\n            case \"break\":\n                return \"#ff9800\";\n            case \"offline\":\n                return \"#757575\";\n            default:\n                return \"#757575\";\n        }\n    };\n    const formatLastUpdate = (timestamp)=>{\n        if (!timestamp) return \"Unknown\";\n        const now = new Date();\n        const updateTime = new Date(timestamp);\n        const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));\n        if (diffMinutes < 1) return \"Just now\";\n        if (diffMinutes < 60) return \"\".concat(diffMinutes, \"m ago\");\n        const diffHours = Math.floor(diffMinutes / 60);\n        if (diffHours < 24) return \"\".concat(diffHours, \"h ago\");\n        return updateTime.toLocaleDateString();\n    };\n    if (!mapReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            display: \"flex\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            height: height,\n            bgcolor: \"#f5f5f5\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: \"Loading map...\"\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n            lineNumber: 143,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            severity: \"error\",\n            sx: {\n                height\n            },\n            children: [\n                \"Failed to load map: \",\n                error\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n            lineNumber: 157,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        height: height,\n        width: \"100%\",\n        position: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MapContainer, {\n                center: defaultCenter,\n                zoom: defaultZoom,\n                style: {\n                    height: \"100%\",\n                    width: \"100%\"\n                },\n                ref: mapRef,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TileLayer, {\n                        attribution: '\\xa9 <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors',\n                        url: \"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, undefined),\n                    agentLocations.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Marker, {\n                            position: [\n                                agent.latitude,\n                                agent.longitude\n                            ],\n                            icon: agentIcon,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Popup, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    p: 1,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"h6\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    sx: {\n                                                        mr: 1,\n                                                        verticalAlign: \"middle\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                agent.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Employee ID:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                agent.employeeId\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Status:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    label: agent.status,\n                                                    size: \"small\",\n                                                    sx: {\n                                                        backgroundColor: getAgentStatusColor(agent.status),\n                                                        color: \"white\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        agent.currentShift && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Current Site:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" \",\n                                                agent.currentShift.siteName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 208,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Location:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                agent.latitude.toFixed(6),\n                                                \", \",\n                                                agent.longitude.toFixed(6)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            gutterBottom: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Accuracy:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \\xb1\",\n                                                Math.round(agent.accuracy || 0),\n                                                \"m\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: \"text.secondary\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Last Update:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 222,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \" \",\n                                                formatLastUpdate(agent.lastUpdate)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        agent.distanceFromSite !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"body2\",\n                                            color: agent.withinGeofence ? \"success.main\" : \"error.main\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                    children: \"Distance from site:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \" \",\n                                                Math.round(agent.distanceFromSite),\n                                                \"m\",\n                                                agent.withinGeofence ? \" ✓\" : \" ⚠️\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined)\n                        }, agent.id, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, undefined)),\n                    sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Marker, {\n                                    position: [\n                                        site.latitude,\n                                        site.longitude\n                                    ],\n                                    icon: siteIcon,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Popup, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            p: 1,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"h6\",\n                                                    gutterBottom: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Business_LocationOn_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            sx: {\n                                                                mr: 1,\n                                                                verticalAlign: \"middle\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        site.name\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    gutterBottom: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Client:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 254,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        site.clientName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    gutterBottom: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Address:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        site.address\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    gutterBottom: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Type:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        site.siteType\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    gutterBottom: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Geofence:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 266,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \" \",\n                                                        site.geofenceRadius,\n                                                        \"m radius\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                site.activeAgents > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"success.main\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                            children: \"Active Agents:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        \" \",\n                                                        site.activeAgents\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Circle, {\n                                    center: [\n                                        site.latitude,\n                                        site.longitude\n                                    ],\n                                    radius: site.geofenceRadius,\n                                    pathOptions: {\n                                        color: \"#2196f3\",\n                                        fillColor: \"#2196f3\",\n                                        fillOpacity: 0.1,\n                                        weight: 2\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, site.id, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                            lineNumber: 241,\n                            columnNumber: 11\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                position: \"absolute\",\n                top: 10,\n                right: 10,\n                bgcolor: \"white\",\n                p: 1,\n                borderRadius: 1,\n                boxShadow: 2,\n                zIndex: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        variant: \"caption\",\n                        display: \"block\",\n                        gutterBottom: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Legend\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 304,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        mb: 0.5,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 12,\n                                height: 12,\n                                bgcolor: \"#4caf50\",\n                                borderRadius: \"50%\",\n                                mr: 1\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"caption\",\n                                children: \"Active Agent\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        mb: 0.5,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 12,\n                                height: 12,\n                                bgcolor: \"#2196f3\",\n                                borderRadius: \"50%\",\n                                mr: 1\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"caption\",\n                                children: \"On Shift\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 317,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        mb: 0.5,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 12,\n                                height: 12,\n                                bgcolor: \"#ff9800\",\n                                borderRadius: \"50%\",\n                                mr: 1\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"caption\",\n                                children: \"On Break\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                width: 12,\n                                height: 12,\n                                bgcolor: \"#757575\",\n                                borderRadius: \"50%\",\n                                mr: 1\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Chip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                variant: \"caption\",\n                                children: \"Offline\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                                lineNumber: 345,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                        lineNumber: 337,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/RealTimeMap.js\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, undefined);\n};\n_s(RealTimeMap, \"h+E8NlIR9QlM8Ugfd9VIFUrVSb0=\");\n_c5 = RealTimeMap;\n/* harmony default export */ __webpack_exports__[\"default\"] = (RealTimeMap);\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MapContainer\");\n$RefreshReg$(_c1, \"TileLayer\");\n$RefreshReg$(_c2, \"Marker\");\n$RefreshReg$(_c3, \"Popup\");\n$RefreshReg$(_c4, \"Circle\");\n$RefreshReg$(_c5, \"RealTimeMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RealTimeMap.js\n"));

/***/ })

});