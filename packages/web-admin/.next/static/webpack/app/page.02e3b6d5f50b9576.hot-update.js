"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useMediaQuery,useTheme!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,Business,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,Notifications,People,Settings,Shield!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    var _user_firstName, _user_primaryEmailAddress, _user_firstName1;\n    _s();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: \"1px solid \".concat(colors.border),\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: \"1px solid \".concat(colors.border),\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 189,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 227,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 227,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                title: isCollapsed ? \"\".concat(item.label, \" - \").concat(item.description) : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.3)) : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 292,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 264,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 259,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 258,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 358,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: \"1px solid \".concat(colors.border),\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: \"1px solid \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.border, 0.5))\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                    alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: \"2px solid \".concat(colors.accent),\n                                        boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2))\n                                    },\n                                    children: user === null || user === void 0 ? void 0 : (_user_firstName = user.firstName) === null || _user_firstName === void 0 ? void 0 : _user_firstName.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user === null || user === void 0 ? void 0 : user.firstName,\n                                                \" \",\n                                                user === null || user === void 0 ? void 0 : user.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 425,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user === null || user === void 0 ? void 0 : (_user_primaryEmailAddress = user.primaryEmailAddress) === null || _user_primaryEmailAddress === void 0 ? void 0 : _user_primaryEmailAddress.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 477,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 478,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 385,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"\".concat(user === null || user === void 0 ? void 0 : user.fullName, \" - Administrator\"),\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                src: user === null || user === void 0 ? void 0 : user.imageUrl,\n                                alt: user === null || user === void 0 ? void 0 : user.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: \"2px solid \".concat(colors.accent),\n                                    boxShadow: \"0 0 0 2px \".concat((0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)),\n                                    cursor: \"pointer\"\n                                },\n                                children: user === null || user === void 0 ? void 0 : (_user_firstName1 = user.firstName) === null || _user_firstName1 === void 0 ? void 0 : _user_firstName1.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 487,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 486,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_Business_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_Notifications_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 503,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 502,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 485,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModernSidebar, \"H/Th896/VJA9swIrszVnm+8Pq7M=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useMediaQuery_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n    ];\n});\n_c = ModernSidebar;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModernSidebar);\nvar _c;\n$RefreshReg$(_c, \"ModernSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ModernSidebar.js\n"));

/***/ })

});