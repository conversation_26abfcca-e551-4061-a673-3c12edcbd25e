// BahinLink Web Admin Sign-Up Page
// ⚠️ CRITICAL: Real Clerk authentication integration ONLY

'use client';

import { SignUp } from '@clerk/nextjs';
import { Box, Paper, Typography, Container, Grid, Alert } from '@mui/material';
import { Security, Shield, LocationOn, Analytics, AdminPanelSettings } from '@mui/icons-material';

export default function SignUpPage() {
  return (
    <Container maxWidth="lg" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', py: 4 }}>
      <Grid container spacing={4} alignItems="center">
        {/* Left side - Branding and Features */}
        <Grid item xs={12} md={6}>
          <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>
            {/* Logo and Title */}
            <Box display="flex" alignItems="center" justifyContent={{ xs: 'center', md: 'flex-start' }} mb={3}>
              <Shield sx={{ fontSize: 48, color: '#1976d2', mr: 2 }} />
              <Typography variant="h3" component="h1" fontWeight="bold" color="primary">
                BahinLink
              </Typography>
            </Box>
            
            <Typography variant="h5" component="h2" gutterBottom color="text.primary">
              Join Our Security Management Platform
            </Typography>
            
            <Typography variant="body1" color="text.secondary" mb={4}>
              Create your admin account to manage Bahin SARL security operations across Senegal.
            </Typography>

            {/* Feature highlights */}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Box display="flex" alignItems="center">
                <LocationOn sx={{ color: '#1976d2', mr: 2 }} />
                <Typography variant="body2">
                  Real-time GPS tracking and geofencing
                </Typography>
              </Box>
              
              <Box display="flex" alignItems="center">
                <Security sx={{ color: '#1976d2', mr: 2 }} />
                <Typography variant="body2">
                  Comprehensive shift and time management
                </Typography>
              </Box>
              
              <Box display="flex" alignItems="center">
                <Analytics sx={{ color: '#1976d2', mr: 2 }} />
                <Typography variant="body2">
                  Advanced reporting and analytics
                </Typography>
              </Box>

              <Box display="flex" alignItems="center">
                <AdminPanelSettings sx={{ color: '#1976d2', mr: 2 }} />
                <Typography variant="body2">
                  Full administrative control and oversight
                </Typography>
              </Box>
            </Box>
          </Box>
        </Grid>

        {/* Right side - Sign Up Form */}
        <Grid item xs={12} md={6}>
          <Paper 
            elevation={8} 
            sx={{ 
              p: 4, 
              borderRadius: 3,
              background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
              border: '1px solid rgba(25, 118, 210, 0.1)'
            }}
          >
            <Box textAlign="center" mb={3}>
              <Typography variant="h4" component="h2" fontWeight="bold" color="primary" gutterBottom>
                Create Admin Account
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Sign up to access the BahinLink admin dashboard
              </Typography>
            </Box>

            {/* Admin Notice */}
            <Alert severity="info" sx={{ mb: 3 }}>
              <Typography variant="body2">
                <strong>Admin Registration:</strong> New admin accounts require approval from existing administrators.
              </Typography>
            </Alert>

            {/* Clerk SignUp Component */}
            <Box display="flex" justifyContent="center">
              <SignUp 
                appearance={{
                  elements: {
                    formButtonPrimary: {
                      backgroundColor: '#1976d2',
                      '&:hover': {
                        backgroundColor: '#1565c0'
                      }
                    },
                    card: {
                      boxShadow: 'none',
                      backgroundColor: 'transparent'
                    },
                    headerTitle: {
                      display: 'none'
                    },
                    headerSubtitle: {
                      display: 'none'
                    }
                  },
                  layout: {
                    socialButtonsPlacement: 'bottom'
                  }
                }}
                redirectUrl="/dashboard"
                signInUrl="/sign-in"
              />
            </Box>

            {/* Additional Info */}
            <Box mt={3} textAlign="center">
              <Typography variant="caption" color="text.secondary">
                Secure authentication powered by Clerk
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Footer */}
      <Box 
        position="absolute" 
        bottom={0} 
        left={0} 
        right={0} 
        textAlign="center" 
        py={2}
        sx={{ backgroundColor: 'rgba(255, 255, 255, 0.8)' }}
      >
        <Typography variant="caption" color="text.secondary">
          © 2024 Bahin SARL. All rights reserved. | Real-time security workforce management
        </Typography>
      </Box>
    </Container>
  );
}
