// BahinLink Admin Dashboard
// ⚠️ CRITICAL: Real-time monitoring with production data ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  LocationOn,
  Schedule,
  Assignment,
  People,
  Warning,
  CheckCircle,
  Error,
  Refresh
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

import dynamic from 'next/dynamic';
import ApiService from '../services/ApiService';
import { formatTime, formatDate, enumToDisplayString } from '../utils';

// Dynamically import RealTimeMap to avoid SSR issues
const RealTimeMap = dynamic(() => import('../components/RealTimeMap'), {
  ssr: false,
  loading: () => (
    <Box sx={{
      height: 400,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: 1
    }}>
      <Typography>Loading map...</Typography>
    </Box>
  )
});

export default function Dashboard() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadDashboardData();
      
      // Auto-refresh every 30 seconds for real-time data
      const interval = setInterval(loadDashboardData, 30000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn]);

  const loadDashboardData = async () => {
    try {
      setError(null);
      
      // Get real dashboard analytics
      const response = await ApiService.get('/analytics/dashboard');
      
      if (response.success) {
        setDashboardData(response.data);
      } else {
        throw new Error(response.error?.message || 'Failed to load dashboard data');
      }
    } catch (error) {
      console.error('Dashboard data error:', error);
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading BahinLink Dashboard...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Box>
    );
  }

  const {
    activeAgents = 0,
    totalAgents = 0,
    activeShifts = 0,
    pendingReports = 0,
    geofenceViolations = 0,
    clientSatisfaction = 0,
    recentActivity = [],
    agentLocations = [],
    shiftStats = [],
    alerts = []
  } = dashboardData || {};

  return (
    <Box sx={{ flexGrow: 1, p: 3, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          BahinLink Dashboard
        </Typography>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="body2" color="text.secondary">
            Welcome, {user?.firstName} {user?.lastName}
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </Box>
      </Box>

      {/* Key Metrics */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Agents
                  </Typography>
                  <Typography variant="h4" component="div">
                    {activeAgents}/{totalAgents}
                  </Typography>
                </Box>
                <People color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Active Shifts
                  </Typography>
                  <Typography variant="h4" component="div">
                    {activeShifts}
                  </Typography>
                </Box>
                <Schedule color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Pending Reports
                  </Typography>
                  <Typography variant="h4" component="div">
                    {pendingReports}
                  </Typography>
                </Box>
                <Assignment color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="text.secondary" gutterBottom>
                    Client Satisfaction
                  </Typography>
                  <Typography variant="h4" component="div">
                    {clientSatisfaction.toFixed(1)}/5.0
                  </Typography>
                </Box>
                <CheckCircle color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Alerts */}
      {alerts.length > 0 && (
        <Grid container spacing={3} mb={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Active Alerts
                </Typography>
                {alerts.map((alert, index) => (
                  <Alert 
                    key={index} 
                    severity={alert.severity} 
                    sx={{ mb: 1 }}
                    action={
                      <Button color="inherit" size="small">
                        View
                      </Button>
                    }
                  >
                    {alert.message}
                  </Alert>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Real-time Map and Activity */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Real-time Agent Locations
              </Typography>
              <Box height={400}>
                <RealTimeMap agentLocations={agentLocations} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <List dense>
                {recentActivity.map((activity, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>
                      {activity.type === 'clock_in' && <CheckCircle color="success" />}
                      {activity.type === 'clock_out' && <Schedule color="primary" />}
                      {activity.type === 'report_submitted' && <Assignment color="info" />}
                      {activity.type === 'geofence_violation' && <Warning color="error" />}
                      {activity.type === 'location_update' && <LocationOn color="primary" />}
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.message}
                      secondary={formatTime(activity.timestamp)}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Shift Statistics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Shift Performance (Last 7 Days)
              </Typography>
              <Box height={300}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={shiftStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="completedShifts" 
                      stroke="#2196f3" 
                      strokeWidth={2}
                      name="Completed Shifts"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="onTimePercentage" 
                      stroke="#4caf50" 
                      strokeWidth={2}
                      name="On-time %"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Current Shifts
              </Typography>
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Agent</TableCell>
                      <TableCell>Site</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Time</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dashboardData?.currentShifts?.map((shift) => (
                      <TableRow key={shift.id}>
                        <TableCell>{shift.agentName}</TableCell>
                        <TableCell>{shift.siteName}</TableCell>
                        <TableCell>
                          <Chip 
                            label={enumToDisplayString(shift.status)}
                            color={shift.status === 'IN_PROGRESS' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
