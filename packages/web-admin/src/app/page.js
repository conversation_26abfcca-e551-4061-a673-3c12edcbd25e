// BahinLink Admin Dashboard
// ⚠️ CRITICAL: Real-time monitoring with production data ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  LocationOn,
  Schedule,
  Assignment,
  People,
  Warning,
  CheckCircle,
  Error,
  Refresh,
  Star
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

import dynamic from 'next/dynamic';
import ApiService from '../services/ApiService';
import { formatTime, formatDate, enumToDisplayString } from '../utils';
import ModernSidebar from '../components/ModernSidebar';

// Dynamically import RealTimeMap to avoid SSR issues
const RealTimeMap = dynamic(() => import('../components/RealTimeMap'), {
  ssr: false,
  loading: () => (
    <Box sx={{
      height: 400,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: 1
    }}>
      <Typography>Loading map...</Typography>
    </Box>
  )
});

export default function Dashboard() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadDashboardData();
      
      // Auto-refresh every 30 seconds for real-time data
      const interval = setInterval(loadDashboardData, 30000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn]);

  const loadDashboardData = async () => {
    try {
      setError(null);
      
      // Get real dashboard analytics
      const response = await ApiService.get('/analytics/dashboard');
      
      if (response.success) {
        setDashboardData(response.data);
      } else {
        throw new Error(response.error?.message || 'Failed to load dashboard data');
      }
    } catch (error) {
      console.error('Dashboard data error:', error);
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading BahinLink Dashboard...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Box>
    );
  }

  const {
    activeAgents = 0,
    totalAgents = 0,
    activeShifts = 0,
    pendingReports = 0,
    geofenceViolations = 0,
    clientSatisfaction = 0,
    recentActivity = [],
    agentLocations = [],
    shiftStats = [],
    alerts = []
  } = dashboardData || {};

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      {/* Modern Sidebar */}
      <ModernSidebar />

      {/* Main Content Area */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          backgroundColor: '#f8fafc',
          minHeight: '100vh',
          transition: 'margin-left 0.3s ease-in-out'
        }}
      >
        {/* Content Container */}
        <Box sx={{ p: { xs: 2, sm: 3, md: 4 }, maxWidth: '1400px', mx: 'auto' }}>
          {/* Modern Header */}
          <Box
            display="flex"
            justifyContent="space-between"
            alignItems="center"
            mb={4}
            sx={{
              backgroundColor: 'white',
              borderRadius: 3,
              p: 3,
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(0, 0, 0, 0.05)'
            }}
          >
            <Box>
              <Typography
                variant="h4"
                component="h1"
                sx={{
                  fontWeight: 700,
                  color: '#1e293b',
                  fontSize: { xs: '1.75rem', md: '2.125rem' },
                  letterSpacing: '-0.025em',
                  mb: 0.5
                }}
              >
                Dashboard Overview
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: '#64748b',
                  fontSize: '1rem'
                }}
              >
                Real-time monitoring for Bahin SARL security operations
              </Typography>
            </Box>

            <Box display="flex" alignItems="center" gap={2}>
              <Typography
                variant="body2"
                sx={{
                  color: '#64748b',
                  display: { xs: 'none', sm: 'block' }
                }}
              >
                Welcome back, {user?.firstName}
              </Typography>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={handleRefresh}
                disabled={refreshing}
                sx={{
                  borderColor: '#e2e8f0',
                  color: '#475569',
                  '&:hover': {
                    borderColor: '#cbd5e1',
                    backgroundColor: '#f8fafc'
                  },
                  borderRadius: 2,
                  textTransform: 'none',
                  fontWeight: 500
                }}
              >
                {refreshing ? 'Refreshing...' : 'Refresh'}
              </Button>
            </Box>
          </Box>

      {/* Modern KPI Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography
                    sx={{
                      color: '#64748b',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      mb: 1
                    }}
                  >
                    Active Agents
                  </Typography>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: '#1e293b',
                      fontSize: '2rem',
                      lineHeight: 1.2
                    }}
                  >
                    {activeAgents}
                    <Typography
                      component="span"
                      sx={{
                        color: '#94a3b8',
                        fontSize: '1.25rem',
                        fontWeight: 500
                      }}
                    >
                      /{totalAgents}
                    </Typography>
                  </Typography>
                </Box>
                <Box
                  sx={{
                    backgroundColor: '#dbeafe',
                    borderRadius: 2,
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <People sx={{ fontSize: 28, color: '#3b82f6' }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography
                    sx={{
                      color: '#64748b',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      mb: 1
                    }}
                  >
                    Active Shifts
                  </Typography>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: '#1e293b',
                      fontSize: '2rem',
                      lineHeight: 1.2
                    }}
                  >
                    {activeShifts}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    backgroundColor: '#dcfce7',
                    borderRadius: 2,
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Schedule sx={{ fontSize: 28, color: '#16a34a' }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography
                    sx={{
                      color: '#64748b',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      mb: 1
                    }}
                  >
                    Pending Reports
                  </Typography>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: '#1e293b',
                      fontSize: '2rem',
                      lineHeight: 1.2
                    }}
                  >
                    {pendingReports}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    backgroundColor: '#fef3c7',
                    borderRadius: 2,
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Assignment sx={{ fontSize: 28, color: '#d97706' }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              transition: 'all 0.2s ease-in-out',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
              }
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography
                    sx={{
                      color: '#64748b',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      mb: 1
                    }}
                  >
                    Client Satisfaction
                  </Typography>
                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 700,
                      color: '#1e293b',
                      fontSize: '2rem',
                      lineHeight: 1.2
                    }}
                  >
                    {clientSatisfaction.toFixed(1)}
                    <Typography
                      component="span"
                      sx={{
                        color: '#94a3b8',
                        fontSize: '1.25rem',
                        fontWeight: 500
                      }}
                    >
                      /5.0
                    </Typography>
                  </Typography>
                </Box>
                <Box
                  sx={{
                    backgroundColor: '#dcfce7',
                    borderRadius: 2,
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Star sx={{ fontSize: 28, color: '#16a34a' }} />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Modern Quick Actions */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '1.125rem',
                  mb: 3
                }}
              >
                Quick Actions
              </Typography>
              <Box display="flex" gap={2} flexWrap="wrap">
                <Button
                  variant="contained"
                  startIcon={<People />}
                  onClick={() => window.location.href = '/agents'}
                  sx={{
                    textTransform: 'none',
                    backgroundColor: '#6366f1',
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 500,
                    boxShadow: '0 1px 3px rgba(99, 102, 241, 0.3)',
                    '&:hover': {
                      backgroundColor: '#5855eb',
                      transform: 'translateY(-1px)',
                      boxShadow: '0 4px 12px rgba(99, 102, 241, 0.4)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  Manage Agents
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<LocationOn />}
                  onClick={() => window.location.href = '/sites'}
                  sx={{
                    textTransform: 'none',
                    borderColor: '#e2e8f0',
                    color: '#475569',
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 500,
                    '&:hover': {
                      borderColor: '#cbd5e1',
                      backgroundColor: '#f8fafc',
                      transform: 'translateY(-1px)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  View Sites
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Schedule />}
                  onClick={() => window.location.href = '/shifts'}
                  sx={{
                    textTransform: 'none',
                    borderColor: '#e2e8f0',
                    color: '#475569',
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 500,
                    '&:hover': {
                      borderColor: '#cbd5e1',
                      backgroundColor: '#f8fafc',
                      transform: 'translateY(-1px)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  Schedule Shifts
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Assignment />}
                  onClick={() => window.location.href = '/reports'}
                  sx={{
                    textTransform: 'none',
                    borderColor: '#e2e8f0',
                    color: '#475569',
                    borderRadius: 2,
                    px: 3,
                    py: 1.5,
                    fontWeight: 500,
                    '&:hover': {
                      borderColor: '#cbd5e1',
                      backgroundColor: '#f8fafc',
                      transform: 'translateY(-1px)'
                    },
                    transition: 'all 0.2s ease-in-out'
                  }}
                >
                  View Reports
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Modern Alerts */}
      {alerts.length > 0 && (
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12}>
            <Card
              sx={{
                borderRadius: 3,
                border: '1px solid rgba(0, 0, 0, 0.05)',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                backgroundColor: 'white'
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: '#1e293b',
                    fontSize: '1.125rem',
                    mb: 3
                  }}
                >
                  Active Alerts
                </Typography>
                {alerts.map((alert, index) => (
                  <Alert
                    key={index}
                    severity={alert.severity}
                    sx={{
                      mb: 2,
                      borderRadius: 2,
                      border: '1px solid',
                      borderColor: alert.severity === 'warning' ? '#fbbf24' : '#3b82f6',
                      backgroundColor: alert.severity === 'warning' ? '#fef3c7' : '#dbeafe',
                      '& .MuiAlert-icon': {
                        color: alert.severity === 'warning' ? '#d97706' : '#3b82f6'
                      },
                      '& .MuiAlert-message': {
                        color: '#1e293b',
                        fontWeight: 500
                      }
                    }}
                    action={
                      <Button
                        size="small"
                        sx={{
                          color: alert.severity === 'warning' ? '#d97706' : '#3b82f6',
                          fontWeight: 500,
                          textTransform: 'none',
                          '&:hover': {
                            backgroundColor: 'rgba(0, 0, 0, 0.04)'
                          }
                        }}
                      >
                        VIEW
                      </Button>
                    }
                  >
                    {alert.message}
                  </Alert>
                ))}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Modern Real-time Map and Activity */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={8}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '1.125rem',
                  mb: 3
                }}
              >
                Real-time Agent Locations
              </Typography>
              <Box
                height={400}
                sx={{
                  borderRadius: 2,
                  overflow: 'hidden',
                  border: '1px solid #e2e8f0'
                }}
              >
                <RealTimeMap agentLocations={agentLocations} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '1.125rem',
                  mb: 3
                }}
              >
                Recent Activity
              </Typography>
              <List dense sx={{ '& .MuiListItem-root': { px: 0 } }}>
                {recentActivity.map((activity, index) => (
                  <ListItem
                    key={index}
                    sx={{
                      borderRadius: 2,
                      mb: 1,
                      backgroundColor: '#f8fafc',
                      border: '1px solid #e2e8f0',
                      '&:hover': {
                        backgroundColor: '#f1f5f9'
                      },
                      transition: 'background-color 0.2s ease-in-out'
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      {activity.type === 'clock_in' && (
                        <CheckCircle sx={{ color: '#16a34a', fontSize: 20 }} />
                      )}
                      {activity.type === 'clock_out' && (
                        <Schedule sx={{ color: '#3b82f6', fontSize: 20 }} />
                      )}
                      {activity.type === 'report_submitted' && (
                        <Assignment sx={{ color: '#6366f1', fontSize: 20 }} />
                      )}
                      {activity.type === 'geofence_violation' && (
                        <Warning sx={{ color: '#ef4444', fontSize: 20 }} />
                      )}
                      {activity.type === 'location_update' && (
                        <LocationOn sx={{ color: '#3b82f6', fontSize: 20 }} />
                      )}
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.message}
                      secondary={formatTime(activity.timestamp)}
                      primaryTypographyProps={{
                        sx: {
                          fontSize: '0.875rem',
                          fontWeight: 500,
                          color: '#1e293b',
                          lineHeight: 1.4
                        }
                      }}
                      secondaryTypographyProps={{
                        sx: {
                          fontSize: '0.75rem',
                          color: '#64748b',
                          mt: 0.5
                        }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Modern Shift Statistics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '1.125rem',
                  mb: 3
                }}
              >
                Shift Performance (Last 7 Days)
              </Typography>
              <Box
                height={300}
                sx={{
                  '& .recharts-cartesian-grid-horizontal line': {
                    stroke: '#e2e8f0'
                  },
                  '& .recharts-cartesian-grid-vertical line': {
                    stroke: '#e2e8f0'
                  }
                }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={shiftStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      axisLine={{ stroke: '#e2e8f0' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      axisLine={{ stroke: '#e2e8f0' }}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #e2e8f0',
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="completedShifts"
                      stroke="#6366f1"
                      strokeWidth={3}
                      name="Completed Shifts"
                      dot={{ fill: '#6366f1', strokeWidth: 2, r: 4 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="onTimePercentage"
                      stroke="#16a34a"
                      strokeWidth={3}
                      name="On-time %"
                      dot={{ fill: '#16a34a', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: '1px solid rgba(0, 0, 0, 0.05)',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: 'white'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#1e293b',
                  fontSize: '1.125rem',
                  mb: 3
                }}
              >
                Current Shifts
              </Typography>
              <TableContainer
                sx={{
                  borderRadius: 2,
                  border: '1px solid #e2e8f0',
                  '& .MuiTable-root': {
                    minWidth: 'auto'
                  }
                }}
              >
                <Table size="small">
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          color: '#475569',
                          fontSize: '0.875rem',
                          borderBottom: '1px solid #e2e8f0'
                        }}
                      >
                        Agent
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          color: '#475569',
                          fontSize: '0.875rem',
                          borderBottom: '1px solid #e2e8f0'
                        }}
                      >
                        Site
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          color: '#475569',
                          fontSize: '0.875rem',
                          borderBottom: '1px solid #e2e8f0'
                        }}
                      >
                        Status
                      </TableCell>
                      <TableCell
                        sx={{
                          fontWeight: 600,
                          color: '#475569',
                          fontSize: '0.875rem',
                          borderBottom: '1px solid #e2e8f0'
                        }}
                      >
                        Time
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {dashboardData?.currentShifts?.map((shift) => (
                      <TableRow
                        key={shift.id}
                        sx={{
                          '&:hover': {
                            backgroundColor: '#f8fafc'
                          },
                          '&:last-child td': {
                            borderBottom: 'none'
                          }
                        }}
                      >
                        <TableCell
                          sx={{
                            color: '#1e293b',
                            fontWeight: 500,
                            fontSize: '0.875rem',
                            borderBottom: '1px solid #f1f5f9'
                          }}
                        >
                          {shift.agentName}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: '#64748b',
                            fontSize: '0.875rem',
                            borderBottom: '1px solid #f1f5f9'
                          }}
                        >
                          {shift.siteName}
                        </TableCell>
                        <TableCell sx={{ borderBottom: '1px solid #f1f5f9' }}>
                          <Chip
                            label={enumToDisplayString(shift.status)}
                            size="small"
                            sx={{
                              backgroundColor: shift.status === 'IN_PROGRESS' ? '#dcfce7' : '#f1f5f9',
                              color: shift.status === 'IN_PROGRESS' ? '#16a34a' : '#64748b',
                              fontWeight: 500,
                              fontSize: '0.75rem',
                              height: 24,
                              '& .MuiChip-label': {
                                px: 1.5
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell
                          sx={{
                            color: '#64748b',
                            fontSize: '0.875rem',
                            borderBottom: '1px solid #f1f5f9'
                          }}
                        >
                          {formatTime(shift.startTime)} - {formatTime(shift.endTime)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
        </Box>
      </Box>
    </Box>
  );
}
