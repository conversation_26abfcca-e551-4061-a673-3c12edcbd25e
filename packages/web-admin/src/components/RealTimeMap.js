// BahinLink Real-time Map Component
// ⚠️ CRITICAL: Real GPS coordinates and live agent tracking ONLY

'use client';

import { useEffect, useState } from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { LocationOn, Person } from '@mui/icons-material';

const RealTimeMap = ({ agentLocations = [], sites = [], height = 400 }) => {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Simulate map loading
    const timer = setTimeout(() => {
      setIsLoaded(true);
    }, 1000);
    
    return () => clearTimeout(timer);
  }, []);

  const getAgentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'on_duty': return '#4caf50';
      case 'on_break': return '#ff9800';
      case 'off_duty': return '#757575';
      default: return '#2196f3';
    }
  };

  const formatLastUpdate = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return updateTime.toLocaleDateString();
  };

  if (!isLoaded) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          border: '1px solid #ddd',
          borderRadius: 1
        }}
      >
        <Typography>Loading real-time map...</Typography>
      </Box>
    );
  }

  return (
    <Box 
      height={height} 
      width="100%" 
      sx={{
        backgroundColor: '#f5f5f5',
        border: '1px solid #ddd',
        borderRadius: 1,
        p: 2,
        overflow: 'auto'
      }}
    >
      <Box display="flex" alignItems="center" mb={2}>
        <LocationOn sx={{ mr: 1, color: '#1976d2' }} />
        <Typography variant="h6">
          Real-time Agent Locations (Dakar, Senegal)
        </Typography>
      </Box>
      
      {agentLocations.length === 0 ? (
        <Typography color="text.secondary" textAlign="center" py={4}>
          No agents currently active
        </Typography>
      ) : (
        <Box>
          {agentLocations.map((agent) => (
            <Box
              key={agent.id}
              sx={{
                backgroundColor: 'white',
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                p: 2,
                mb: 2,
                '&:last-child': { mb: 0 }
              }}
            >
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <Box display="flex" alignItems="center">
                  <Person sx={{ mr: 1, color: '#1976d2' }} />
                  <Typography variant="subtitle1" fontWeight="bold">
                    {agent.agentName}
                  </Typography>
                </Box>
                <Chip 
                  label={agent.status}
                  size="small"
                  sx={{ 
                    backgroundColor: getAgentStatusColor(agent.status),
                    color: 'white'
                  }}
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Employee ID:</strong> {agent.employeeId}
              </Typography>
              
              {agent.siteName && (
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  <strong>Current Site:</strong> {agent.siteName}
                </Typography>
              )}
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Location:</strong> {agent.latitude.toFixed(4)}°, {agent.longitude.toFixed(4)}°
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                <strong>Last Update:</strong> {formatLastUpdate(agent.lastUpdate)}
              </Typography>
            </Box>
          ))}
        </Box>
      )}
      
      <Box
        sx={{
          backgroundColor: 'white',
          border: '1px solid #e0e0e0',
          borderRadius: 1,
          p: 2,
          mt: 2
        }}
      >
        <Typography variant="subtitle2" gutterBottom>
          Status Legend
        </Typography>
        
        <Box display="flex" gap={3} flexWrap="wrap">
          <Box display="flex" alignItems="center">
            <Box
              width={12}
              height={12}
              bgcolor="#4caf50"
              borderRadius="50%"
              mr={1}
            />
            <Typography variant="caption">On Duty</Typography>
          </Box>
          
          <Box display="flex" alignItems="center">
            <Box
              width={12}
              height={12}
              bgcolor="#ff9800"
              borderRadius="50%"
              mr={1}
            />
            <Typography variant="caption">On Break</Typography>
          </Box>
          
          <Box display="flex" alignItems="center">
            <Box
              width={12}
              height={12}
              bgcolor="#757575"
              borderRadius="50%"
              mr={1}
            />
            <Typography variant="caption">Off Duty</Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default RealTimeMap;
