// BahinLink Real-time Map Component
// ⚠️ CRITICAL: Real GPS coordinates and live agent tracking ONLY

'use client';

import { useEffect, useState, useRef } from 'react';
import dynamic from 'next/dynamic';
import { Box, Typography, Chip, Alert } from '@mui/material';
import { LocationOn, Person, Business } from '@mui/icons-material';

// Dynamically import map components to avoid SSR issues
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });
const Popup = dynamic(() => import('react-leaflet').then(mod => mod.Popup), { ssr: false });
const Circle = dynamic(() => import('react-leaflet').then(mod => mod.Circle), { ssr: false });

// Initialize Leaflet only on client side
let L;
if (typeof window !== 'undefined') {
  L = require('leaflet');
  require('leaflet/dist/leaflet.css');

  // Fix for default markers in react-leaflet
  delete L.Icon.Default.prototype._getIconUrl;
  L.Icon.Default.mergeOptions({
    iconRetinaUrl: '/leaflet/marker-icon-2x.png',
    iconUrl: '/leaflet/marker-icon.png',
    shadowUrl: '/leaflet/marker-shadow.png',
  });
}

// Custom icons for different marker types
const agentIcon = new L.Icon({
  iconUrl: '/icons/agent-marker.png',
  iconRetinaUrl: '/icons/agent-marker-2x.png',
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
  shadowUrl: '/leaflet/marker-shadow.png',
  shadowSize: [41, 41],
  shadowAnchor: [12, 41]
});

const siteIcon = new L.Icon({
  iconUrl: '/icons/site-marker.png',
  iconRetinaUrl: '/icons/site-marker-2x.png',
  iconSize: [32, 32],
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
  shadowUrl: '/leaflet/marker-shadow.png',
  shadowSize: [41, 41],
  shadowAnchor: [12, 41]
});

const RealTimeMap = ({ agentLocations = [], sites = [], height = 400 }) => {
  const [mapReady, setMapReady] = useState(false);
  const [error, setError] = useState(null);
  const mapRef = useRef(null);

  // Default center: Dakar, Senegal (real coordinates)
  const defaultCenter = [14.6937, -17.4441];
  const defaultZoom = 12;

  useEffect(() => {
    // Ensure we're in browser environment
    if (typeof window !== 'undefined') {
      setMapReady(true);
    }
  }, []);

  // Don't render map on server side
  if (!mapReady) {
    return (
      <Box
        sx={{
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f5f5f5',
          border: '1px solid #ddd',
          borderRadius: 1
        }}
      >
        <Typography>Loading map...</Typography>
      </Box>
    );
  }

  useEffect(() => {
    // Auto-fit map bounds when agent locations change
    if (mapRef.current && agentLocations.length > 0) {
      const map = mapRef.current;
      const bounds = L.latLngBounds();
      
      agentLocations.forEach(agent => {
        if (agent.latitude && agent.longitude) {
          bounds.extend([agent.latitude, agent.longitude]);
        }
      });
      
      sites.forEach(site => {
        if (site.latitude && site.longitude) {
          bounds.extend([site.latitude, site.longitude]);
        }
      });
      
      if (bounds.isValid()) {
        map.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [agentLocations, sites]);

  const getAgentStatusColor = (status) => {
    switch (status) {
      case 'active': return '#4caf50';
      case 'on_shift': return '#2196f3';
      case 'break': return '#ff9800';
      case 'offline': return '#757575';
      default: return '#757575';
    }
  };

  const formatLastUpdate = (timestamp) => {
    if (!timestamp) return 'Unknown';
    
    const now = new Date();
    const updateTime = new Date(timestamp);
    const diffMinutes = Math.floor((now - updateTime) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return updateTime.toLocaleDateString();
  };

  if (!mapReady) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        height={height}
        bgcolor="#f5f5f5"
      >
        <Typography>Loading map...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ height }}>
        Failed to load map: {error}
      </Alert>
    );
  }

  return (
    <Box height={height} width="100%" position="relative">
      <MapContainer
        center={defaultCenter}
        zoom={defaultZoom}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        {/* Real OpenStreetMap tiles */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        {/* Agent markers with real GPS coordinates */}
        {agentLocations.map((agent) => (
          <Marker
            key={agent.id}
            position={[agent.latitude, agent.longitude]}
            icon={agentIcon}
          >
            <Popup>
              <Box p={1}>
                <Typography variant="h6" gutterBottom>
                  <Person sx={{ mr: 1, verticalAlign: 'middle' }} />
                  {agent.name}
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>Employee ID:</strong> {agent.employeeId}
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>Status:</strong>{' '}
                  <Chip 
                    label={agent.status}
                    size="small"
                    sx={{ 
                      backgroundColor: getAgentStatusColor(agent.status),
                      color: 'white'
                    }}
                  />
                </Typography>
                
                {agent.currentShift && (
                  <Typography variant="body2" gutterBottom>
                    <strong>Current Site:</strong> {agent.currentShift.siteName}
                  </Typography>
                )}
                
                <Typography variant="body2" gutterBottom>
                  <strong>Location:</strong> {agent.latitude.toFixed(6)}, {agent.longitude.toFixed(6)}
                </Typography>
                
                <Typography variant="body2" gutterBottom>
                  <strong>Accuracy:</strong> ±{Math.round(agent.accuracy || 0)}m
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  <strong>Last Update:</strong> {formatLastUpdate(agent.lastUpdate)}
                </Typography>
                
                {agent.distanceFromSite !== undefined && (
                  <Typography 
                    variant="body2" 
                    color={agent.withinGeofence ? 'success.main' : 'error.main'}
                  >
                    <strong>Distance from site:</strong> {Math.round(agent.distanceFromSite)}m
                    {agent.withinGeofence ? ' ✓' : ' ⚠️'}
                  </Typography>
                )}
              </Box>
            </Popup>
          </Marker>
        ))}
        
        {/* Site markers with real coordinates */}
        {sites.map((site) => (
          <div key={site.id}>
            <Marker
              position={[site.latitude, site.longitude]}
              icon={siteIcon}
            >
              <Popup>
                <Box p={1}>
                  <Typography variant="h6" gutterBottom>
                    <Business sx={{ mr: 1, verticalAlign: 'middle' }} />
                    {site.name}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Client:</strong> {site.clientName}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Address:</strong> {site.address}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Type:</strong> {site.siteType}
                  </Typography>
                  
                  <Typography variant="body2" gutterBottom>
                    <strong>Geofence:</strong> {site.geofenceRadius}m radius
                  </Typography>
                  
                  {site.activeAgents > 0 && (
                    <Typography variant="body2" color="success.main">
                      <strong>Active Agents:</strong> {site.activeAgents}
                    </Typography>
                  )}
                </Box>
              </Popup>
            </Marker>
            
            {/* Geofence circle */}
            <Circle
              center={[site.latitude, site.longitude]}
              radius={site.geofenceRadius}
              pathOptions={{
                color: '#2196f3',
                fillColor: '#2196f3',
                fillOpacity: 0.1,
                weight: 2
              }}
            />
          </div>
        ))}
      </MapContainer>
      
      {/* Map legend */}
      <Box
        position="absolute"
        top={10}
        right={10}
        bgcolor="white"
        p={1}
        borderRadius={1}
        boxShadow={2}
        zIndex={1000}
      >
        <Typography variant="caption" display="block" gutterBottom>
          <strong>Legend</strong>
        </Typography>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#4caf50"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">Active Agent</Typography>
        </Box>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#2196f3"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">On Shift</Typography>
        </Box>
        <Box display="flex" alignItems="center" mb={0.5}>
          <Box
            width={12}
            height={12}
            bgcolor="#ff9800"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">On Break</Typography>
        </Box>
        <Box display="flex" alignItems="center">
          <Box
            width={12}
            height={12}
            bgcolor="#757575"
            borderRadius="50%"
            mr={1}
          />
          <Typography variant="caption">Offline</Typography>
        </Box>
      </Box>
    </Box>
  );
};

export default RealTimeMap;
