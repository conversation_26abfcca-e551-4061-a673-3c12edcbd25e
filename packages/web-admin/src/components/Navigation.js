// BahinLink Web Admin Navigation Component
// ⚠️ CRITICAL: Real navigation with admin functionality ONLY

'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser, useClerk } from '@clerk/nextjs';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Box,
  Chip,
  Divider,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Dashboard,
  People,
  LocationOn,
  Assignment,
  Analytics,
  Settings,
  ExitToApp,
  AccountCircle,
  Shield,
  Notifications,
  Business
} from '@mui/icons-material';

export default function Navigation() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSignOut = async () => {
    await signOut();
    router.push('/sign-in');
  };

  const navigationItems = [
    { label: 'Dashboard', icon: Dashboard, path: '/' },
    { label: 'Agents', icon: People, path: '/agents' },
    { label: 'Sites', icon: LocationOn, path: '/sites' },
    { label: 'Shifts', icon: Assignment, path: '/shifts' },
    { label: 'Reports', icon: Assignment, path: '/reports' },
    { label: 'Analytics', icon: Analytics, path: '/analytics' },
    { label: 'Users', icon: AccountCircle, path: '/users' }
  ];

  return (
    <AppBar position="static" sx={{ backgroundColor: '#1976d2', mb: 0 }}>
      <Toolbar>
        {/* Logo and Title */}
        <Box display="flex" alignItems="center" sx={{ flexGrow: 1 }}>
          <Shield sx={{ mr: 1, fontSize: 28 }} />
          <Typography variant="h6" component="div" fontWeight="bold">
            BahinLink Admin
          </Typography>
          <Chip 
            label="LIVE" 
            color="success" 
            size="small" 
            sx={{ ml: 2, fontWeight: 'bold' }}
          />
        </Box>

        {/* Navigation Items */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 1, mr: 2 }}>
          {navigationItems.map((item) => (
            <Button
              key={item.path}
              color="inherit"
              startIcon={<item.icon />}
              onClick={() => router.push(item.path)}
              sx={{ 
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)'
                }
              }}
            >
              {item.label}
            </Button>
          ))}
        </Box>

        {/* Notifications */}
        <IconButton color="inherit" sx={{ mr: 1 }}>
          <Notifications />
        </IconButton>

        {/* User Menu */}
        <Box display="flex" alignItems="center">
          <Typography variant="body2" sx={{ mr: 1, display: { xs: 'none', sm: 'block' } }}>
            {user?.firstName} {user?.lastName}
          </Typography>
          <IconButton
            color="inherit"
            onClick={handleMenuOpen}
            sx={{ p: 0 }}
          >
            <Avatar 
              src={user?.imageUrl} 
              alt={user?.fullName}
              sx={{ width: 32, height: 32 }}
            >
              {user?.firstName?.charAt(0)}
            </Avatar>
          </IconButton>
        </Box>

        {/* User Dropdown Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem disabled>
            <ListItemIcon>
              <AccountCircle />
            </ListItemIcon>
            <ListItemText>
              <Typography variant="body2" fontWeight="bold">
                {user?.fullName}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.primaryEmailAddress?.emailAddress}
              </Typography>
            </ListItemText>
          </MenuItem>
          
          <Divider />
          
          <MenuItem onClick={() => { handleMenuClose(); router.push('/settings'); }}>
            <ListItemIcon>
              <Settings />
            </ListItemIcon>
            <ListItemText>Settings</ListItemText>
          </MenuItem>
          
          <MenuItem onClick={() => { handleMenuClose(); router.push('/sites'); }}>
            <ListItemIcon>
              <Business />
            </ListItemIcon>
            <ListItemText>Manage Sites</ListItemText>
          </MenuItem>
          
          <Divider />
          
          <MenuItem onClick={handleSignOut}>
            <ListItemIcon>
              <ExitToApp />
            </ListItemIcon>
            <ListItemText>Sign Out</ListItemText>
          </MenuItem>
        </Menu>
      </Toolbar>
    </AppBar>
  );
}
