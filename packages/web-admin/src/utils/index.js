// BahinLink Web Admin Utilities
// ⚠️ CRITICAL: Utility functions for web admin dashboard

/**
 * Format date for display
 * @param {Date|string} date Date to format
 * @param {string} formatString Format string (default: 'PPP')
 * @returns {string} Formatted date string
 */
export function formatDate(date, formatString = 'PPP') {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!dateObj || isNaN(dateObj.getTime())) return 'Invalid Date';
    
    // Simple date formatting
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  } catch (error) {
    return 'Invalid Date';
  }
}

/**
 * Format time for display
 * @param {Date|string} date Date to format
 * @param {string} formatString Format string (default: 'p')
 * @returns {string} Formatted time string
 */
export function formatTime(date, formatString = 'p') {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (!dateObj || isNaN(dateObj.getTime())) return 'Invalid Time';
    
    // Simple time formatting
    return dateObj.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return 'Invalid Time';
  }
}

/**
 * Convert enum value to display string
 * @param {string} enumValue Enum value
 * @returns {string} Display string
 */
export function enumToDisplayString(enumValue) {
  if (!enumValue) return '';
  
  return enumValue
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

/**
 * Format duration in hours and minutes
 * @param {number} hours Duration in hours (decimal)
 * @returns {string} Formatted duration string
 */
export function formatDuration(hours) {
  const wholeHours = Math.floor(hours);
  const minutes = Math.round((hours - wholeHours) * 60);
  
  if (wholeHours === 0) {
    return `${minutes}m`;
  }
  
  if (minutes === 0) {
    return `${wholeHours}h`;
  }
  
  return `${wholeHours}h ${minutes}m`;
}

/**
 * Calculate distance between two GPS coordinates using Haversine formula
 * @param {number} lat1 Latitude of first point
 * @param {number} lon1 Longitude of first point
 * @param {number} lat2 Latitude of second point
 * @param {number} lon2 Longitude of second point
 * @returns {number} Distance in meters
 */
export function calculateDistance(lat1, lon1, lat2, lon2) {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
}

/**
 * Capitalize first letter of string
 * @param {string} str String to capitalize
 * @returns {string} Capitalized string
 */
export function capitalize(str) {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Generate unique ID
 * @returns {string} Unique ID string
 */
export function generateId() {
  return Math.random().toString(36).substr(2, 9);
}

/**
 * Debounce function
 * @param {Function} func Function to debounce
 * @param {number} wait Wait time in milliseconds
 * @returns {Function} Debounced function
 */
export function debounce(func, wait) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Format file size for display
 * @param {number} bytes File size in bytes
 * @returns {string} Formatted file size string
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Check if current time is within shift hours
 * @param {Date} shiftStart Shift start time
 * @param {Date} shiftEnd Shift end time
 * @param {Date} currentTime Current time (optional, defaults to now)
 * @returns {boolean} True if within shift hours
 */
export function isWithinShiftHours(shiftStart, shiftEnd, currentTime = new Date()) {
  return currentTime >= shiftStart && currentTime <= shiftEnd;
}

/**
 * Calculate shift progress percentage
 * @param {Date} shiftStart Shift start time
 * @param {Date} shiftEnd Shift end time
 * @param {Date} currentTime Current time (optional, defaults to now)
 * @returns {number} Progress percentage (0-100)
 */
export function calculateShiftProgress(shiftStart, shiftEnd, currentTime = new Date()) {
  const totalDuration = shiftEnd.getTime() - shiftStart.getTime();
  const elapsed = currentTime.getTime() - shiftStart.getTime();
  
  if (elapsed <= 0) return 0;
  if (elapsed >= totalDuration) return 100;
  
  return Math.round((elapsed / totalDuration) * 100);
}

/**
 * Validate email address
 * @param {string} email Email to validate
 * @returns {boolean} True if valid email
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate phone number (basic validation)
 * @param {string} phone Phone number to validate
 * @returns {boolean} True if valid phone number
 */
export function isValidPhone(phone) {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

/**
 * Get status color for UI components
 * @param {string} status Status value
 * @returns {string} Color code
 */
export function getStatusColor(status) {
  const statusColors = {
    ACTIVE: '#10b981',
    INACTIVE: '#ef4444',
    PENDING: '#f59e0b',
    COMPLETED: '#10b981',
    IN_PROGRESS: '#3b82f6',
    CANCELLED: '#6b7280',
    APPROVED: '#10b981',
    REJECTED: '#ef4444',
    SUBMITTED: '#f59e0b'
  };
  
  return statusColors[status] || '#6b7280';
}

/**
 * Format currency for display
 * @param {number} amount Amount to format
 * @param {string} currency Currency code (default: 'XOF')
 * @returns {string} Formatted currency string
 */
export function formatCurrency(amount, currency = 'XOF') {
  try {
    return new Intl.NumberFormat('fr-SN', {
      style: 'currency',
      currency: currency
    }).format(amount);
  } catch (error) {
    return `${amount} ${currency}`;
  }
}
