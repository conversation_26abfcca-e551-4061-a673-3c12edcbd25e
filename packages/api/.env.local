# BahinLink API Environment Variables
# ⚠️ CRITICAL: Production environment configuration

# Database Configuration
DATABASE_URL="prisma://accelerate.prisma-data.net/?api_key=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqd3RfaWQiOjEsInNlY3VyZV9rZXkiOiJza19qTGVVWXk1ZXZ1NTZvLUFublhqNzIiLCJhcGlfa2V5IjoiMDFKWldXSzJQRFNUV1JZNVcxVjI1TVZKR0oiLCJ0ZW5hbnRfaWQiOiI5ZDA3NjJhYzgxNGEwMTYyNjYyMzFhY2I2MjQ0NmViYzFhYjRlODJkNTk2ZDRlNGE0YzZiY2Y2NGY1MDQyYTRmIiwiaW50ZXJuYWxfc2VjcmV0IjoiMzA5MWY3NTktMDU5Ny00NTBlLTlhYWQtNGY0ZmI1NDM1OTIyIn0.H-twYVvGFtn4TM6wJPFgexRcUfSDyBGrH614Mvcg4Ws"

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_IHdUQQJ0YWh2ilwmbSiMxhjOoJDyEvvOYFhhuIoGzz
CLERK_PUBLISHABLE_KEY=pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_WEBHOOK_SECRET=whsec_dyTwNQATnQEWOwPOwExwWKmdApqy5eXm

# API Configuration
NODE_ENV=development
PORT=3001
API_BASE_URL=http://localhost:3001

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:19006

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_DIR=./uploads

# Security
JWT_SECRET=bahinlink-jwt-secret-key-2024
ENCRYPTION_KEY=bahinlink-encryption-key-32-chars

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=debug
LOG_FILE=./logs/api.log

# External Services
GOOGLE_MAPS_API_KEY=your-google-maps-api-key
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=your-twilio-phone-number

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379

# Webhook URLs
WEBHOOK_BASE_URL=https://your-api-domain.com

# Feature Flags
ENABLE_REAL_TIME_TRACKING=true
ENABLE_GEOFENCING=true
ENABLE_OFFLINE_SYNC=true
ENABLE_PUSH_NOTIFICATIONS=true
ENABLE_VIDEO_REPORTS=true
ENABLE_CLIENT_PORTAL=true
ENABLE_ANALYTICS=true

# Development Settings
DEBUG_MODE=true
MOCK_GPS_DATA=false
SKIP_EMAIL_VERIFICATION=true
