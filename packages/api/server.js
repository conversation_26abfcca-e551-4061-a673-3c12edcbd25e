// BahinLink API Local Development Server
// ⚠️ CRITICAL: Local development server for testing

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'BahinLink API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API routes - simulate the Vercel functions structure
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    message: 'BahinLink API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Sample endpoints for testing
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        firstName: 'Amadou',
        lastName: 'Ba',
        email: '<EMAIL>',
        role: 'AGENT',
        isActive: true,
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        firstName: 'Fatou',
        lastName: 'Sow',
        email: '<EMAIL>',
        role: 'AGENT',
        isActive: true,
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.get('/api/sites', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Sonatel HQ',
        address: 'Plateau, Dakar, Senegal',
        latitude: 14.6928,
        longitude: -17.4467,
        status: 'ACTIVE',
        client: {
          id: '1',
          companyName: 'Sonatel Senegal'
        }
      },
      {
        id: '2',
        name: 'Sonatel Data Center',
        address: 'Almadies, Dakar, Senegal',
        latitude: 14.7167,
        longitude: -17.4833,
        status: 'ACTIVE',
        client: {
          id: '1',
          companyName: 'Sonatel Senegal'
        }
      }
    ]
  });
});

app.get('/api/shifts', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        shiftDate: new Date().toISOString().split('T')[0],
        startTime: new Date().toISOString(),
        endTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
        status: 'IN_PROGRESS',
        agent: {
          id: '1',
          user: {
            firstName: 'Amadou',
            lastName: 'Ba'
          },
          employeeId: 'BSL001'
        },
        site: {
          id: '1',
          name: 'Sonatel HQ',
          client: {
            companyName: 'Sonatel Senegal'
          }
        },
        scheduledDuration: 8,
        hoursWorked: 4.5
      }
    ]
  });
});

app.get('/api/reports', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        title: 'Security Incident Report',
        description: 'Unauthorized access attempt detected at main entrance',
        type: 'SECURITY',
        priority: 'HIGH',
        status: 'SUBMITTED',
        createdAt: new Date().toISOString(),
        agent: {
          id: '1',
          user: {
            firstName: 'Amadou',
            lastName: 'Ba'
          }
        },
        site: {
          id: '1',
          name: 'Sonatel HQ',
          client: {
            companyName: 'Sonatel Senegal'
          }
        }
      }
    ]
  });
});

// Client portal endpoints
app.get('/api/client/sites', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Main Office',
        status: 'ACTIVE',
        agentsOnSite: 2
      },
      {
        id: '2',
        name: 'Warehouse A',
        status: 'ACTIVE',
        agentsOnSite: 1
      }
    ]
  });
});

app.get('/api/client/shifts', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        site: 'Main Office',
        agent: 'Amadou Ba',
        status: 'IN_PROGRESS',
        startTime: '08:00'
      }
    ]
  });
});

app.get('/api/client/reports', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        type: 'SECURITY',
        priority: 'HIGH',
        status: 'SUBMITTED',
        createdAt: new Date().toISOString()
      }
    ]
  });
});

app.get('/api/client/agents', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: '1',
        name: 'Amadou Ba',
        status: 'ON_DUTY',
        currentSite: 'Main Office'
      }
    ]
  });
});

// Analytics endpoints
app.get('/api/analytics/dashboard', (req, res) => {
  res.json({
    success: true,
    data: {
      // Key metrics
      activeAgents: 12,
      totalAgents: 18,
      activeShifts: 8,
      pendingReports: 5,
      geofenceViolations: 2,
      clientSatisfaction: 4.7,

      // Real-time agent locations (Dakar, Senegal coordinates)
      agentLocations: [
        {
          id: '1',
          agentName: 'Amadou Ba',
          employeeId: 'BSL001',
          latitude: 14.6928,
          longitude: -17.4467,
          status: 'ON_DUTY',
          siteName: 'Sonatel HQ',
          lastUpdate: new Date().toISOString()
        },
        {
          id: '2',
          agentName: 'Fatou Sow',
          employeeId: 'BSL002',
          latitude: 14.7167,
          longitude: -17.4833,
          status: 'ON_DUTY',
          siteName: 'Sonatel Data Center',
          lastUpdate: new Date().toISOString()
        },
        {
          id: '3',
          agentName: 'Moussa Diop',
          employeeId: 'BSL003',
          latitude: 14.6937,
          longitude: -17.4441,
          status: 'ON_BREAK',
          siteName: 'CBAO Bank Plateau',
          lastUpdate: new Date().toISOString()
        }
      ],

      // Recent activity feed
      recentActivity: [
        {
          type: 'clock_in',
          message: 'Amadou Ba clocked in at Sonatel HQ',
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          agentId: '1'
        },
        {
          type: 'report_submitted',
          message: 'Fatou Sow submitted security report',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          agentId: '2'
        },
        {
          type: 'geofence_violation',
          message: 'Moussa Diop left assigned area',
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
          agentId: '3'
        },
        {
          type: 'clock_out',
          message: 'Aissatou Fall completed shift',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          agentId: '4'
        },
        {
          type: 'location_update',
          message: 'Ibrahima Ndiaye location updated',
          timestamp: new Date(Date.now() - 75 * 60 * 1000).toISOString(),
          agentId: '5'
        }
      ],

      // Shift statistics for the last 7 days
      shiftStats: [
        { date: '2024-01-15', completedShifts: 16, onTimePercentage: 94 },
        { date: '2024-01-16', completedShifts: 18, onTimePercentage: 89 },
        { date: '2024-01-17', completedShifts: 15, onTimePercentage: 93 },
        { date: '2024-01-18', completedShifts: 20, onTimePercentage: 95 },
        { date: '2024-01-19', completedShifts: 17, onTimePercentage: 91 },
        { date: '2024-01-20', completedShifts: 19, onTimePercentage: 96 },
        { date: '2024-01-21', completedShifts: 14, onTimePercentage: 88 }
      ],

      // Current active shifts
      currentShifts: [
        {
          id: '1',
          agentName: 'Amadou Ba',
          siteName: 'Sonatel HQ',
          status: 'IN_PROGRESS',
          startTime: '08:00',
          endTime: '16:00'
        },
        {
          id: '2',
          agentName: 'Fatou Sow',
          siteName: 'Sonatel Data Center',
          status: 'IN_PROGRESS',
          startTime: '09:00',
          endTime: '17:00'
        },
        {
          id: '3',
          agentName: 'Moussa Diop',
          siteName: 'CBAO Bank Plateau',
          status: 'IN_PROGRESS',
          startTime: '07:00',
          endTime: '15:00'
        }
      ],

      // Active alerts
      alerts: [
        {
          severity: 'warning',
          message: 'Moussa Diop has been outside geofence for 15 minutes at CBAO Bank Plateau'
        },
        {
          severity: 'info',
          message: '5 reports pending supervisor review'
        }
      ]
    }
  });
});

app.get('/api/analytics/shifts', (req, res) => {
  res.json({
    success: true,
    data: {
      trends: [
        { date: '2024-01-01', scheduled: 20, completed: 18, cancelled: 2 },
        { date: '2024-01-02', scheduled: 22, completed: 20, cancelled: 1 }
      ],
      siteActivity: [
        { site: 'Sonatel HQ', shifts: 45, incidents: 3, efficiency: 95 }
      ]
    }
  });
});

app.get('/api/analytics/reports', (req, res) => {
  res.json({
    success: true,
    data: {
      types: [
        { name: 'Security', value: 35, color: '#DC3545' },
        { name: 'Maintenance', value: 25, color: '#FFC107' }
      ]
    }
  });
});

app.get('/api/analytics/agents', (req, res) => {
  res.json({
    success: true,
    data: {
      performance: [
        { name: 'Amadou Ba', shifts: 15, onTime: 14, reports: 8, rating: 4.8 }
      ]
    }
  });
});

app.get('/api/analytics/kpis', (req, res) => {
  res.json({
    success: true,
    data: {
      totalShifts: 156,
      completedShifts: 142,
      totalHours: 1248,
      averageRating: 4.7,
      activeAgents: 18,
      totalReports: 89,
      responseTime: 12.5,
      efficiency: 94.2
    }
  });
});

// Catch all for undefined routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 BahinLink API Server running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🔗 API endpoints: http://localhost:${PORT}/api/*`);
  console.log(`📱 Ready for mobile app and web dashboard testing!`);
});

module.exports = app;
